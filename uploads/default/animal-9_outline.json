{"meta": {"version": "3.0", "compression_type": "contour", "regions": 96, "colors": 12, "size": [574, 589], "simplification_level": 0.009730290456431534, "coordinate_compression": "relative"}, "regions": [{"id": 1, "hex": "#96bbc3", "contour": {"type": "relative", "data": [[529, 73], [-44, 31], [10, -13], [-53, -4], [-29, 24], [-104, 24], [-14, -6], [30, -9], [9, -43], [-74, 72], [-63, 41], [4, -10], [-55, -31], [-4, 25], [24, 17], [17, -3], [-27, 15], [7, 15], [14, -4], [15, 17], [41, 3], [-24, 14], [-9, -9], [10, 9], [45, -21], [62, -10], [74, -42], [20, 0], [87, -73], [7, 10], [24, -39]], "is_closed": true}, "area": 23104.5, "original_pixel_count": 20676, "contour_points": 30, "order": 1}, {"id": 2, "hex": "#79a9b4", "contour": {"type": "relative", "data": [[573, 36], [-162, 140], [-202, 73], [57, 35], [-28, -20], [56, 0], [-22, -5], [5, -29], [35, 31], [129, -77], [2, 17], [49, -21], [26, -56], [48, 46], [7, -90], [-17, 26], [-23, -10], [40, -60]], "is_closed": true}, "area": 15308.5, "original_pixel_count": 16912, "contour_points": 17, "order": 2}, {"id": 3, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[135, 55], [46, 9], [-8, -29], [40, -14], [23, 25], [-22, 7], [37, 14], [2, 26], [-51, 3], [81, 23], [94, -88], [59, 16], [56, -47], [-221, 0], [0, 24], [25, -18], [41, 37], [-31, 23], [-54, -20], [-21, -43], [-41, -3], [-22, 43], [-33, 12]], "is_closed": true}, "area": 14077.5, "original_pixel_count": 13866, "contour_points": 22, "order": 3}, {"id": 4, "hex": "#96bbc3", "contour": {"type": "relative", "data": [[515, 150], [-23, 31], [-7, -9], [-39, 29], [-5, -16], [-117, 72], [-49, 13], [-40, -10], [102, 61], [60, -21], [22, -57], [17, 37], [32, -24], [-2, -36], [19, -35], [14, 16], [16, -51]], "is_closed": true}, "area": 13488.0, "original_pixel_count": 13221, "contour_points": 16, "order": 4}, {"id": 5, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[86, 13], [13, 81], [34, -22], [39, 27], [72, -4], [26, 17], [-24, -16], [14, -1], [-10, -28], [-37, -14], [22, -7], [-29, -27], [-32, 15], [7, 31], [-46, -9], [32, -13], [22, -43], [-60, 0], [-5, 18], [-38, -5]], "is_closed": true}, "area": 10623.0, "original_pixel_count": 10284, "contour_points": 19, "order": 5}, {"id": 6, "hex": "#c1cecc", "contour": {"type": "relative", "data": [[565, 38], [-46, -13], [-52, 38], [-14, -20], [-22, 5], [28, 23], [-39, 32], [-33, -69], [-1, 23], [-17, -10], [-33, 27], [41, -42], [-45, 44], [-6, 44], [-32, 11], [110, -18], [39, -28], [49, 8], [73, -55]], "is_closed": true}, "area": 9169.0, "original_pixel_count": 9423, "contour_points": 18, "order": 6}, {"id": 7, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[80, 222], [4, 114], [4, 3], [8, -7], [19, -2], [17, 18], [5, -2], [0, -119], [-57, -5]], "is_closed": true}, "area": 6140.5, "original_pixel_count": 6431, "contour_points": 8, "order": 7}, {"id": 8, "hex": "#2a6c77", "contour": {"type": "relative", "data": [[108, 340], [-25, 3], [-3, 13], [27, 18], [-6, 58], [-31, 16], [-32, -3], [-34, 56], [7, -13], [43, 2], [4, 16], [-13, -3], [-2, 14], [33, 5], [-18, -53], [87, -14], [-30, -30], [20, -9], [-22, -31], [19, -27], [-24, -18]], "is_closed": true}, "area": 6243.0, "original_pixel_count": 6353, "contour_points": 20, "order": 8}, {"id": 9, "hex": "#c1cecc", "contour": {"type": "relative", "data": [[100, 94], [9, 33], [15, -26], [18, 34], [-3, 43], [21, 15], [-7, 15], [33, -20], [-17, 5], [-25, -17], [3, -26], [53, 29], [-6, 11], [72, -48], [-42, 27], [14, -12], [-80, -57], [0, 25], [-19, -50], [7, 29], [-11, -30], [-35, 20]], "is_closed": true}, "area": 5865.0, "original_pixel_count": 5773, "contour_points": 21, "order": 9}, {"id": 10, "hex": "#469ca6", "contour": {"type": "relative", "data": [[573, 513], [0, -22], [-254, 6], [3, 18], [143, 3], [-17, 11], [15, -2], [7, -17], [76, 19], [15, -9], [-44, -1], [-5, -10], [61, 4]], "is_closed": true}, "area": 5644.0, "original_pixel_count": 5468, "contour_points": 12, "order": 10}, {"id": 11, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[567, 270], [-50, -3], [-1, 114], [9, 8], [-2, -8], [14, -21], [14, 2], [2, 11], [16, -15], [-2, -88]], "is_closed": true}, "area": 5142.0, "original_pixel_count": 5175, "contour_points": 9, "order": 11}, {"id": 12, "hex": "#e6a089", "contour": {"type": "relative", "data": [[0, 343], [9, 30], [-9, 21], [17, 19], [4, -15], [12, 5], [-12, 9], [12, 11], [-33, 2], [0, 13], [18, 5], [14, -17], [27, 7], [4, 11], [28, -9], [-50, -19], [-5, -11], [58, 12], [8, -13], [-10, 4], [-22, -16], [8, -12], [-31, -12], [-4, -13], [-43, -12]], "is_closed": true}, "area": 4830.0, "original_pixel_count": 5145, "contour_points": 24, "order": 12}, {"id": 13, "hex": "#c3c19e", "contour": {"type": "relative", "data": [[139, 306], [0, 40], [-16, -5], [12, 13], [-4, 13], [17, -11], [17, 15], [-14, 8], [4, 9], [23, -24], [0, 21], [11, -4], [5, 16], [9, -16], [17, -3], [9, 9], [9, -8], [8, 16], [9, -10], [17, 5], [-5, -32], [3, 28], [-21, -3], [-14, -21], [-96, -56]], "is_closed": true}, "area": 4575.0, "original_pixel_count": 4956, "contour_points": 24, "order": 13}, {"id": 14, "hex": "#c1cecc", "contour": {"type": "relative", "data": [[5, 22], [21, 46], [-22, -31], [7, 20], [-11, -5], [0, 36], [12, 12], [-5, 12], [-7, -4], [0, 21], [13, 20], [13, -5], [8, 21], [-4, -29], [18, -34], [11, 26], [-5, -18], [14, -8], [6, -21], [-26, -3], [-25, -42], [-18, -14]], "is_closed": true}, "area": 4524.0, "original_pixel_count": 4630, "contour_points": 21, "order": 14}, {"id": 15, "hex": "#79a9b4", "contour": {"type": "relative", "data": [[143, 228], [4, 46], [25, 29], [11, -12], [10, 33], [11, -18], [10, 35], [-9, -35], [-13, 12], [4, -25], [31, 13], [-10, 25], [10, -16], [10, 28], [11, -14], [38, 44], [19, -15], [8, 20], [9, -24], [14, 11], [8, -20], [-24, -9], [-41, 17], [-30, -24], [-12, 14], [-6, -42], [19, 23], [15, -12], [-30, -20], [-30, 6], [-14, -42], [-48, -28]], "is_closed": true}, "area": 5035.0, "original_pixel_count": 4746, "contour_points": 31, "order": 15}, {"id": 16, "hex": "#469ca6", "contour": {"type": "relative", "data": [[438, 487], [-311, 7], [2, 30], [-9, 1], [1, -9], [-12, 5], [6, 21], [17, -2], [10, -16], [40, 3], [17, -8], [9, 6], [39, -7], [20, 11], [22, -8], [-15, -3], [20, -4], [-5, -9], [-33, 6], [-71, -9], [1, 8], [-42, 1], [-13, -8], [13, -8], [294, -8]], "is_closed": true}, "area": 3293.5, "original_pixel_count": 4591, "contour_points": 24, "order": 16}, {"id": 17, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[187, 12], [22, -12], [22, 2], [18, 21], [3, 22], [11, 1], [15, 18], [20, -5], [18, 9], [-2, -12], [12, 5], [4, -10], [8, 0], [-11, -17], [-12, -5], [0, -10], [-19, -12], [-25, 17], [-1, -24], [-76, 0], [-7, 12]], "is_closed": true}, "area": 3855.5, "original_pixel_count": 4173, "contour_points": 20, "order": 17}, {"id": 18, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[0, 563], [14, 20], [14, -8], [0, 7], [7, -2], [3, 6], [15, -6], [1, 8], [6, -6], [4, 6], [11, 0], [22, -30], [23, 1], [17, 29], [40, -5], [10, 5], [87, 0], [-31, -4], [2, -5], [12, -1], [-118, -13], [-29, -8], [-6, -7], [2, 6], [-62, -2], [11, 2], [3, 9], [-58, -2]], "is_closed": true}, "area": 3905.5, "original_pixel_count": 4069, "contour_points": 27, "order": 18}, {"id": 19, "hex": "#c3c19e", "contour": {"type": "relative", "data": [[163, 234], [30, 21], [-2, 10], [17, 10], [-1, 16], [33, -3], [14, 17], [17, 1], [45, 27], [34, 4], [-45, -36], [-10, 5], [-65, -46], [-20, -3], [-32, -39], [7, 19], [-22, -3]], "is_closed": true}, "area": 3644.0, "original_pixel_count": 3526, "contour_points": 16, "order": 19}, {"id": 20, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[0, 319], [0, 23], [16, 1], [8, 11], [19, 0], [4, 13], [32, 13], [-10, 9], [24, 19], [5, -6], [7, 4], [-7, -41], [-21, -4], [-12, -29], [-26, -8], [-13, 7], [-9, -10], [-17, -2]], "is_closed": true}, "area": 3321.0, "original_pixel_count": 3354, "contour_points": 17, "order": 20}, {"id": 21, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[0, 0], [0, 13], [26, 21], [23, 42], [-4, -7], [3, -3], [22, 8], [5, -4], [0, 8], [4, -18], [-11, -9], [-6, -17], [7, -12], [-1, -22], [-68, 0]], "is_closed": true}, "area": 3284.0, "original_pixel_count": 3320, "contour_points": 14, "order": 21}, {"id": 22, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[152, 410], [35, 21], [-8, 10], [9, 7], [12, -16], [-2, -18], [8, 7], [-3, 10], [19, -3], [2, 10], [11, -13], [29, 13], [8, -11], [27, 10], [-2, -24], [-13, -10], [-20, 9], [-19, -7], [-15, 11], [-19, -15], [-13, 7], [-3, 17], [-14, -20], [-21, -4], [-8, 9]], "is_closed": true}, "area": 3127.0, "original_pixel_count": 3393, "contour_points": 24, "order": 22}, {"id": 23, "hex": "#96bbc3", "contour": {"type": "relative", "data": [[191, 254], [-3, 13], [18, 8], [-1, 22], [13, -4], [17, 14], [-7, 7], [9, 28], [9, -14], [19, 24], [58, -16], [21, 8], [-16, 13], [9, 8], [14, -12], [10, 12], [22, -17], [-102, -28], [-41, -31], [-32, 4], [-17, -39]], "is_closed": true}, "area": 3677.0, "original_pixel_count": 3515, "contour_points": 20, "order": 23}, {"id": 24, "hex": "#be7968", "contour": {"type": "relative", "data": [[89, 57], [-4, -1], [-32, 95], [-37, 42], [60, -11], [6, 5], [69, 6], [-72, -10], [7, -119], [23, 76], [20, 32], [-21, -35], [-19, -80]], "is_closed": true}, "area": 3272.0, "original_pixel_count": 3421, "contour_points": 12, "order": 24}, {"id": 25, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[452, 45], [22, 8], [-3, -5], [32, -12], [18, -14], [30, 13], [-9, -4], [3, -5], [9, 3], [19, -8], [0, -21], [-26, 0], [-2, 19], [-22, -19], [-32, 0], [-15, 13], [5, 3], [-4, 8], [-19, 3], [-6, 18]], "is_closed": true}, "area": 3125.0, "original_pixel_count": 3214, "contour_points": 19, "order": 25}, {"id": 26, "hex": "#c1cecc", "contour": {"type": "relative", "data": [[31, 227], [0, 57], [23, 2], [4, 6], [7, 1], [5, 5], [0, 7], [9, 6], [-4, -90], [-44, 6]], "is_closed": true}, "area": 3088.0, "original_pixel_count": 3072, "contour_points": 9, "order": 26}, {"id": 27, "hex": "#e6a089", "contour": {"type": "relative", "data": [[86, 59], [-7, 122], [72, 11], [-39, -42], [-26, -91]], "is_closed": true}, "area": 3202.0, "original_pixel_count": 3065, "contour_points": 4, "order": 27}, {"id": 28, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[472, 513], [-9, 27], [2, 4], [-9, 0], [0, 3], [8, 5], [-3, 11], [3, 14], [16, 11], [6, 0], [18, -10], [5, -9], [1, -12], [-12, -21], [-5, -20], [-6, -2], [-6, 3], [-9, -4]], "is_closed": true}, "area": 2649.5, "original_pixel_count": 2773, "contour_points": 17, "order": 28}, {"id": 29, "hex": "#be7968", "contour": {"type": "relative", "data": [[421, 254], [-25, 59], [36, 1], [-11, 22], [54, -4], [-3, -46], [-24, 5], [-12, 22], [-14, -2], [-1, -51], [7, 9], [-7, -15]], "is_closed": true}, "area": 2552.0, "original_pixel_count": 2759, "contour_points": 11, "order": 29}, {"id": 30, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[330, 376], [-29, 44], [3, 37], [9, -3], [0, -15], [5, -5], [11, -1], [10, 6], [7, 12], [-1, 6], [5, 2], [5, -3], [0, -38], [-25, -42]], "is_closed": true}, "area": 2497.5, "original_pixel_count": 2661, "contour_points": 13, "order": 30}, {"id": 31, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[435, 515], [-18, 2], [-7, 16], [-1, 15], [21, 12], [-5, 4], [-12, -8], [-4, 2], [-2, 19], [11, 11], [23, -2], [11, -14], [-3, -11], [5, -7], [-19, -39]], "is_closed": true}, "area": 2447.5, "original_pixel_count": 2592, "contour_points": 14, "order": 31}, {"id": 32, "hex": "#c1cecc", "contour": {"type": "relative", "data": [[403, 527], [-74, -2], [0, 16], [9, 3], [17, -15], [15, 17], [-5, 7], [-5, -4], [8, 8], [-7, 16], [-7, -1], [-2, 9], [-6, -7], [-11, 4], [-2, -6], [7, -3], [-6, -8], [-7, 25], [62, -2], [-6, -22], [12, -16], [11, -3], [-3, -16]], "is_closed": true}, "area": 2450.0, "original_pixel_count": 2384, "contour_points": 22, "order": 32}, {"id": 33, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[317, 516], [-6, 3], [-14, -1], [-4, 11], [1, 10], [-10, 19], [2, 15], [12, 10], [19, 0], [11, -15], [0, -8], [-6, -6], [2, -7], [-2, -19], [-5, -12]], "is_closed": true}, "area": 2139.5, "original_pixel_count": 2260, "contour_points": 14, "order": 33}, {"id": 34, "hex": "#2a6c77", "contour": {"type": "relative", "data": [[180, 292], [-8, 24], [33, 31], [31, 12], [13, 21], [10, -2], [8, -24], [-7, -9], [-6, 7], [-5, -20], [-13, 13], [-10, -29], [-13, 27], [-9, -35], [-12, 20], [-12, -36]], "is_closed": true}, "area": 2270.0, "original_pixel_count": 2345, "contour_points": 15, "order": 34}, {"id": 35, "hex": "#c3c19e", "contour": {"type": "relative", "data": [[573, 255], [-98, 11], [8, 1], [-7, 6], [28, -4], [1, 12], [-11, -6], [-8, 12], [17, 30], [-14, 8], [12, 0], [-3, 36], [-10, -3], [0, -29], [0, 30], [11, 8], [-9, 6], [25, 4], [0, -112], [53, 4], [5, -14]], "is_closed": true}, "area": 2685.0, "original_pixel_count": 2454, "contour_points": 20, "order": 35}, {"id": 36, "hex": "#e6a089", "contour": {"type": "relative", "data": [[331, 371], [17, 20], [7, 19], [7, 6], [25, 1], [-7, -11], [25, -18], [-7, -8], [-12, -5], [-3, 4], [-2, -6], [-50, -2]], "is_closed": true}, "area": 1927.5, "original_pixel_count": 2008, "contour_points": 11, "order": 36}, {"id": 37, "hex": "#c1cecc", "contour": {"type": "relative", "data": [[329, 78], [-46, 42], [-37, -23], [-31, 10], [-24, -15], [-19, 5], [82, 48], [6, -4], [-13, -16], [-1, -17], [30, 16], [-14, 16], [67, -62]], "is_closed": true}, "area": 1955.5, "original_pixel_count": 1969, "contour_points": 12, "order": 37}, {"id": 38, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[140, 76], [23, 56], [-3, -34], [13, 5], [35, 40], [5, -2], [22, 15], [17, -10], [-36, -26], [-50, -23], [-26, -21]], "is_closed": true}, "area": 1740.5, "original_pixel_count": 1916, "contour_points": 10, "order": 38}, {"id": 39, "hex": "#2a6c77", "contour": {"type": "relative", "data": [[329, 366], [-7, -10], [-10, 25], [-7, -21], [-5, 7], [-6, -6], [-8, 14], [-6, -21], [-11, 43], [-7, 1], [-6, -10], [-11, 10], [-8, -16], [-12, 22], [8, 5], [16, -8], [16, 5], [11, -7], [9, -23], [7, 14], [-3, 13], [10, 7], [30, -44]], "is_closed": true}, "area": 1931.5, "original_pixel_count": 1859, "contour_points": 22, "order": 39}, {"id": 40, "hex": "#949c85", "contour": {"type": "relative", "data": [[226, 382], [-23, -1], [-9, 16], [-4, -14], [-10, 0], [2, -15], [-9, -3], [2, 12], [-20, 12], [-4, -11], [13, -7], [-16, -14], [-11, 7], [-3, -6], [-6, 8], [23, 22], [-5, 37], [10, 17], [-6, 6], [6, -14], [-9, -13], [8, -20], [18, -3], [15, 15], [22, -14], [8, 4], [-9, -11], [12, -12], [5, 16], [0, -14]], "is_closed": true}, "area": 1957.0, "original_pixel_count": 1919, "contour_points": 29, "order": 40}, {"id": 41, "hex": "#c3c19e", "contour": {"type": "relative", "data": [[454, 381], [-33, 30], [1, 48], [-24, 3], [37, 7], [-6, -45], [27, -26], [-29, 26], [0, 31], [-6, -42], [34, -29], [20, 35], [-1, 64], [10, 1], [19, -32], [-6, -24], [-15, 11], [1, -25], [47, -3], [-50, 5], [-26, -35]], "is_closed": true}, "area": 1924.0, "original_pixel_count": 1989, "contour_points": 20, "order": 41}, {"id": 42, "hex": "#949c85", "contour": {"type": "relative", "data": [[181, 488], [122, -5], [-10, -5], [10, -5], [-16, 0], [-1, -6], [34, 8], [13, -9], [-6, 1], [0, -18], [-6, -2], [5, 16], [-10, -5], [5, 7], [-13, 2], [7, -11], [-17, -1], [4, -17], [-9, -2], [-13, 2], [1, 18], [-17, -2], [3, -14], [-11, 9], [-16, 0], [-3, -7], [9, -4], [-10, -8], [-8, 11], [-9, -5], [-4, 6], [8, 4], [-17, 8], [5, -2], [2, 11], [2, -11], [27, -2], [1, 7], [9, -6], [5, 29], [-28, 2], [-6, -23], [-3, 20], [-14, 2], [-1, -16], [-3, 19], [-19, -7], [0, -18], [-2, 29]], "is_closed": true}, "area": 3091.5, "original_pixel_count": 2241, "contour_points": 48, "order": 42}, {"id": 43, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[387, 38], [26, 43], [-2, 8], [8, 15], [24, -20], [12, -2], [3, -13], [-16, -14], [-28, -3], [24, 16], [-7, 10], [-24, -21], [-3, -8], [6, -2], [-10, 6], [-13, -15]], "is_closed": true}, "area": 1484.0, "original_pixel_count": 1597, "contour_points": 15, "order": 43}, {"id": 44, "hex": "#469ca6", "contour": {"type": "relative", "data": [[114, 372], [0, 20], [6, 13], [15, 8], [-2, 6], [-17, 6], [5, 10], [8, -1], [-3, 9], [7, -2], [11, 12], [8, -13], [-1, -7], [-11, -3], [-2, -5], [9, -14], [-4, -6], [-8, 1], [-5, -10], [14, 0], [3, -7], [-8, -4], [-8, -14], [-6, 5], [-11, -4]], "is_closed": true}, "area": 1503.0, "original_pixel_count": 1586, "contour_points": 24, "order": 44}, {"id": 45, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[478, 272], [-1, 98], [12, 2], [9, -2], [4, -10], [-11, 3], [-4, -4], [2, -36], [8, -4], [6, 10], [-1, -12], [-9, -8], [6, -11], [-11, 1], [-3, -6], [6, -18], [14, 5], [-2, -12], [-25, 4]], "is_closed": true}, "area": 1504.0, "original_pixel_count": 1591, "contour_points": 18, "order": 45}, {"id": 46, "hex": "#469ca6", "contour": {"type": "relative", "data": [[6, 142], [-2, 16], [5, 6], [-9, 2], [0, 60], [5, 18], [4, 1], [-4, -13], [5, -1], [9, 9], [-2, 14], [9, -1], [1, -23], [-12, -10], [-1, -18], [-7, -9], [27, -23], [-3, -6], [-14, 14], [-6, -11], [0, -13], [6, -5], [-9, 3], [-2, -10]], "is_closed": true}, "area": 1462.5, "original_pixel_count": 1544, "contour_points": 23, "order": 46}, {"id": 47, "hex": "#e6a089", "contour": {"type": "relative", "data": [[458, 374], [12, 16], [13, 12], [35, 1], [1, 1], [15, 0], [1, -1], [-7, -5], [-14, -14], [0, -3], [-2, 0], [-4, -3], [-48, -5], [-2, 1]], "is_closed": true}, "area": 1358.0, "original_pixel_count": 1436, "contour_points": 13, "order": 47}, {"id": 48, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[454, 409], [-11, 5], [-8, 10], [-2, 8], [1, 32], [2, 4], [-1, 14], [7, -1], [0, -37], [4, -7], [5, -3], [8, 1], [5, 6], [2, 9], [0, 32], [3, 0], [1, -58], [-7, -9], [-9, -6]], "is_closed": true}, "area": 1292.0, "original_pixel_count": 1476, "contour_points": 18, "order": 48}, {"id": 49, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[38, 514], [-6, -2], [-13, 1], [-6, 5], [-8, 22], [5, 9], [20, 4], [3, -2], [4, 1], [7, -4], [4, -16], [-4, -4], [0, -5], [-6, -9]], "is_closed": true}, "area": 1318.0, "original_pixel_count": 1384, "contour_points": 13, "order": 49}, {"id": 50, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[129, 0], [-60, 0], [1, 22], [-7, 12], [5, 5], [1, 12], [9, 11], [3, -25], [-5, -2], [3, -7], [-3, -6], [5, -8], [6, -2], [4, 7], [4, -5], [18, 4], [15, -2], [-4, -10], [5, -6]], "is_closed": true}, "area": 1349.0, "original_pixel_count": 1442, "contour_points": 18, "order": 50}, {"id": 51, "hex": "#949c85", "contour": {"type": "relative", "data": [[422, 406], [34, -25], [25, 36], [54, -5], [0, 69], [18, 1], [0, -30], [-17, -16], [7, -32], [-61, 4], [-28, -32], [-32, 30]], "is_closed": true}, "area": 1542.0, "original_pixel_count": 1568, "contour_points": 11, "order": 51}, {"id": 52, "hex": "#2a6c77", "contour": {"type": "relative", "data": [[396, 304], [-7, -7], [-4, 6], [-6, 0], [-6, 14], [-4, 0], [-7, -9], [-1, 5], [-9, 8], [-6, 2], [-4, -4], [-3, 5], [10, 6], [15, 1], [15, 9], [14, 2], [5, -5], [0, -18], [-4, -4], [2, -11]], "is_closed": true}, "area": 1301.5, "original_pixel_count": 1403, "contour_points": 19, "order": 52}, {"id": 53, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[428, 317], [-27, 1], [0, 41], [10, 5], [-4, 5], [0, 17], [10, -7], [1, -12], [6, 6], [0, -28], [-5, -9], [3, -4], [-1, -10], [10, -3], [-3, -2]], "is_closed": true}, "area": 1218.0, "original_pixel_count": 1285, "contour_points": 14, "order": 53}, {"id": 54, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[472, 341], [-32, 1], [-2, 4], [-9, 2], [0, 23], [19, -2], [23, 1], [1, -29]], "is_closed": true}, "area": 1145.0, "original_pixel_count": 1229, "contour_points": 7, "order": 54}, {"id": 55, "hex": "#e6a089", "contour": {"type": "relative", "data": [[519, 156], [-5, 71], [33, 2], [1, -1], [-12, -22], [-14, -47], [-3, -3]], "is_closed": true}, "area": 1163.5, "original_pixel_count": 1248, "contour_points": 6, "order": 55}, {"id": 56, "hex": "#c3c19e", "contour": {"type": "relative", "data": [[473, 225], [1, 36], [44, -5], [-4, -5], [3, -20], [-29, 3], [-2, 4], [-4, 0], [-1, -6], [6, 0], [3, -9], [-17, 2]], "is_closed": true}, "area": 1191.0, "original_pixel_count": 1238, "contour_points": 11, "order": 56}, {"id": 57, "hex": "#c1cecc", "contour": {"type": "relative", "data": [[479, 109], [-49, 35], [34, -23], [4, 4], [-63, 47], [-16, -3], [35, -22], [-101, 55], [-56, 23], [21, -2], [15, -12], [12, 5], [75, -42], [20, 0], [24, -24], [28, -16], [17, -25]], "is_closed": true}, "area": 1220.0, "original_pixel_count": 1429, "contour_points": 16, "order": 57}, {"id": 58, "hex": "#79a9b4", "contour": {"type": "relative", "data": [[504, 536], [9, 15], [2, 11], [0, -7], [9, -11], [11, 3], [7, 8], [1, 11], [7, 2], [4, -3], [0, -13], [7, -1], [4, 8], [8, -3], [-2, -21], [-67, 1]], "is_closed": true}, "area": 1174.0, "original_pixel_count": 1203, "contour_points": 15, "order": 58}, {"id": 59, "hex": "#be7968", "contour": {"type": "relative", "data": [[518, 155], [-26, 75], [55, 0], [-1, -13], [27, 0], [-17, -2], [0, -27], [-14, 22], [-8, -15], [13, 35], [-33, -3], [4, -72]], "is_closed": true}, "area": 1188.5, "original_pixel_count": 1235, "contour_points": 11, "order": 59}, {"id": 60, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[187, 524], [-5, 12], [-7, 9], [3, 3], [-1, 8], [6, 9], [15, 1], [10, -9], [1, -8], [-5, -5], [2, -9], [-7, -10], [-7, 2], [-5, -3]], "is_closed": true}, "area": 994.5, "original_pixel_count": 1060, "contour_points": 13, "order": 60}, {"id": 61, "hex": "#be7968", "contour": {"type": "relative", "data": [[448, 372], [-20, 1], [-6, 3], [-1, 3], [-2, 0], [-7, 6], [-14, 8], [-16, 12], [-2, 3], [5, 4], [-4, -3], [1, -4], [3, 1], [31, 0], [2, -1], [31, -30], [-1, -3]], "is_closed": true}, "area": 995.5, "original_pixel_count": 1062, "contour_points": 16, "order": 61}, {"id": 62, "hex": "#949c85", "contour": {"type": "relative", "data": [[33, 286], [1, 3], [-4, 3], [4, 3], [0, 4], [-2, 2], [-6, 0], [0, 3], [8, 2], [5, 6], [7, -2], [7, 7], [6, 1], [4, 4], [1, 8], [4, 2], [0, 7], [0, -3], [3, -1], [7, 6], [1, -3], [3, 0], [3, 3], [-3, -4], [3, -9], [-7, -7], [0, -11], [-5, -3], [-1, 3], [-6, 0], [-3, -3], [3, -6], [2, 0], [1, 3], [-2, -8], [-3, -2], [-6, 0], [-4, -6], [-6, -2], [-4, 3], [-1, -3], [2, -1], [-12, 1]], "is_closed": true}, "area": 1291.0, "original_pixel_count": 1122, "contour_points": 42, "order": 62}, {"id": 63, "hex": "#c1cecc", "contour": {"type": "relative", "data": [[79, 190], [-35, 4], [1, 9], [-3, 2], [-8, -1], [1, -9], [-16, 3], [0, 18], [53, -8], [3, -2], [0, -12], [4, -4]], "is_closed": true}, "area": 911.0, "original_pixel_count": 992, "contour_points": 11, "order": 63}, {"id": 64, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[79, 191], [0, 16], [69, 7], [0, -16], [-19, -1], [1, 7], [-11, -1], [0, -8], [-13, -2], [-2, 9], [-8, 0], [-1, -9], [-16, -2]], "is_closed": true}, "area": 931.5, "original_pixel_count": 995, "contour_points": 12, "order": 64}, {"id": 65, "hex": "#727266", "contour": {"type": "relative", "data": [[452, 436], [-5, 3], [-3, 6], [-1, 40], [19, -1], [9, 3], [-8, -3], [0, -38], [-2, -6], [-5, -4], [-4, 0]], "is_closed": true}, "area": 892.5, "original_pixel_count": 934, "contour_points": 10, "order": 65}, {"id": 66, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[83, 584], [0, 4], [49, 0], [0, -3], [-7, -5], [-3, -10], [-6, -6], [-5, 2], [-11, -3], [-3, 1], [-7, 15], [-7, 5]], "is_closed": true}, "area": 811.5, "original_pixel_count": 873, "contour_points": 11, "order": 66}, {"id": 67, "hex": "#79a9b4", "contour": {"type": "relative", "data": [[298, 493], [-77, -2], [-24, 7], [-10, 0], [-1, -5], [-3, -1], [-26, 9], [-13, -5], [-12, 4], [0, 3], [10, 3], [3, 6], [3, -4], [18, 2], [7, -5], [13, 4], [1, -8], [11, 0], [5, -4], [33, 1], [10, -5], [49, 0], [1, 3], [2, -3]], "is_closed": true}, "area": 861.0, "original_pixel_count": 984, "contour_points": 23, "order": 67}, {"id": 68, "hex": "#96bbc3", "contour": {"type": "relative", "data": [[573, 61], [-29, 22], [-11, 14], [5, 3], [-4, 10], [13, -11], [4, 0], [3, 6], [8, -6], [2, -13], [9, -7], [0, -18]], "is_closed": true}, "area": 798.5, "original_pixel_count": 848, "contour_points": 11, "order": 68}, {"id": 69, "hex": "#2a6c77", "contour": {"type": "relative", "data": [[470, 258], [-5, 3], [-5, -5], [-10, 18], [-6, -3], [-2, 8], [-7, 5], [5, 14], [6, -9], [27, -6], [1, -14], [-4, -11]], "is_closed": true}, "area": 764.5, "original_pixel_count": 827, "contour_points": 11, "order": 69}, {"id": 70, "hex": "#727266", "contour": {"type": "relative", "data": [[508, 143], [0, 4], [6, -1], [2, 2], [0, 6], [-16, 50], [-5, -1], [-7, -15], [-3, -1], [-17, 33], [2, 4], [21, -3], [-1, 12], [25, -3], [-24, 0], [27, -76], [0, -7], [-8, -1], [-2, -3]], "is_closed": true}, "area": 781.0, "original_pixel_count": 910, "contour_points": 18, "order": 70}, {"id": 71, "hex": "#c3c19e", "contour": {"type": "relative", "data": [[360, 423], [-1, 1], [-1, 7], [-2, 1], [-1, 3], [1, 1], [0, 20], [-5, 3], [3, -2], [2, 0], [4, 3], [8, -2], [9, 5], [5, -5], [5, 0], [1, -1], [0, -32], [-1, -2], [-1, 1], [-26, -1]], "is_closed": true}, "area": 1130.5, "original_pixel_count": 890, "contour_points": 19, "order": 71}, {"id": 72, "hex": "#96bbc3", "contour": {"type": "relative", "data": [[524, 152], [12, 37], [16, -15], [5, 1], [5, -5], [0, -3], [-7, -4], [-2, -8], [-7, 0], [-2, -4], [-6, 5], [-14, -4]], "is_closed": true}, "area": 744.0, "original_pixel_count": 807, "contour_points": 11, "order": 72}, {"id": 73, "hex": "#79a9b4", "contour": {"type": "relative", "data": [[181, 523], [-8, 4], [-38, 0], [-3, 14], [-12, -3], [-5, 7], [18, 2], [2, 5], [-5, 1], [4, 1], [2, -14], [14, 3], [1, -7], [11, -6], [8, 26], [-12, -6], [-12, 3], [5, 3], [9, -5], [16, 14], [-3, -21], [8, -21]], "is_closed": true}, "area": 823.0, "original_pixel_count": 901, "contour_points": 21, "order": 73}, {"id": 74, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[37, 405], [4, 11], [12, -2], [8, 11], [5, -5], [6, 4], [13, 2], [5, 8], [7, -2], [3, -7], [-4, -6], [-16, -9], [-14, 5], [-8, -5], [0, -4], [-6, 0], [-2, -4], [-10, 0], [-3, 3]], "is_closed": true}, "area": 744.5, "original_pixel_count": 794, "contour_points": 18, "order": 74}, {"id": 75, "hex": "#e6a089", "contour": {"type": "relative", "data": [[153, 408], [-3, 15], [7, 8], [2, 10], [-6, 7], [-2, 8], [9, -3], [5, -11], [21, 7], [1, -5], [-9, -3], [8, -7], [0, -3], [-7, 1], [-10, -8], [-1, -8], [-5, 1], [-9, -4], [-1, -5]], "is_closed": true}, "area": 742.5, "original_pixel_count": 785, "contour_points": 18, "order": 75}, {"id": 76, "hex": "#2a6c77", "contour": {"type": "relative", "data": [[141, 264], [0, 35], [5, 2], [3, 5], [7, 0], [4, 6], [8, 1], [0, -3], [-5, 0], [-2, -8], [4, -1], [-1, -4], [4, -1], [-3, -4], [2, -3], [-2, -8], [-8, 7], [-3, -17], [-3, -2], [-2, 7], [-3, 1], [-5, -13]], "is_closed": true}, "area": 697.0, "original_pixel_count": 764, "contour_points": 21, "order": 76}, {"id": 77, "hex": "#96bbc3", "contour": {"type": "relative", "data": [[297, 494], [-51, 0], [-10, 5], [-7, -2], [-11, 2], [-15, -1], [-24, 2], [19, 0], [6, 2], [21, -2], [2, 3], [12, -1], [5, 2], [11, 0], [1, 5], [2, 0], [2, -5], [27, 0], [5, -3], [5, -7]], "is_closed": true}, "area": 688.0, "original_pixel_count": 785, "contour_points": 19, "order": 77}, {"id": 78, "hex": "#727266", "contour": {"type": "relative", "data": [[55, 436], [-11, 1], [-1, -6], [-9, 5], [-3, -5], [-5, 2], [0, 8], [-3, 3], [-14, 4], [-9, -8], [0, 3], [6, 2], [-1, 3], [-5, 0], [0, 62], [2, 1], [-2, 6], [3, -4], [2, -11], [-2, -4], [7, -22], [27, -31], [14, -4], [16, 7], [-13, -5], [1, -7]], "is_closed": true}, "area": 1170.0, "original_pixel_count": 851, "contour_points": 25, "order": 78}, {"id": 79, "hex": "#727266", "contour": {"type": "relative", "data": [[563, 365], [-26, 28], [7, 5], [0, 23], [-6, 12], [-6, 2], [1, 22], [-9, 4], [0, 3], [9, 1], [0, 15], [-5, 4], [29, -1], [0, -34], [16, 5], [0, -8], [-20, -2], [-7, 3], [-4, -9], [3, -21], [10, 0], [-5, -8], [-5, 1], [3, -5], [-2, -8], [4, -1], [1, 4], [18, 0], [4, -4], [-5, -5], [-26, 0], [21, -26]], "is_closed": true}, "area": 1632.0, "original_pixel_count": 944, "contour_points": 31, "order": 79}, {"id": 80, "hex": "#c3c19e", "contour": {"type": "relative", "data": [[573, 401], [-6, 2], [-1, 3], [-16, -3], [0, 3], [-3, 2], [5, -1], [5, 8], [-2, 10], [3, 0], [2, 5], [-5, 1], [3, 5], [-2, 3], [2, 5], [15, 0], [0, -5], [-3, -2], [0, -27], [3, -2], [0, -7]], "is_closed": true}, "area": 602.0, "original_pixel_count": 666, "contour_points": 20, "order": 80}, {"id": 81, "hex": "#c1cecc", "contour": {"type": "relative", "data": [[392, 421], [0, 30], [3, 0], [2, 2], [0, 3], [-4, 2], [-1, -3], [0, 15], [2, 1], [0, 2], [-3, 4], [5, 5], [4, 1], [4, -4], [0, -4], [2, -2], [5, -1], [2, 3], [1, -1], [-3, -3], [0, -3], [6, -6], [-20, 0], [4, -7], [0, -28], [4, -5], [-5, 1], [-3, 4], [-2, 0], [-1, -6], [-2, 0]], "is_closed": true}, "area": 629.0, "original_pixel_count": 684, "contour_points": 30, "order": 81}, {"id": 82, "hex": "#f1d2b7", "contour": {"type": "relative", "data": [[455, 385], [-20, 21], [-13, 6], [0, 37], [3, 6], [5, -4], [-1, -23], [-3, -4], [13, -16], [10, -5], [0, -9], [5, -2], [0, 10], [15, 7], [-14, -24]], "is_closed": true}, "area": 629.0, "original_pixel_count": 675, "contour_points": 14, "order": 82}, {"id": 83, "hex": "#96bbc3", "contour": {"type": "relative", "data": [[185, 289], [9, 32], [7, -17], [5, 2], [8, 31], [13, -31], [-8, -1], [-7, -5], [-9, 1], [-7, -7], [-5, 4], [-6, -9]], "is_closed": true}, "area": 619.5, "original_pixel_count": 652, "contour_points": 11, "order": 83}, {"id": 84, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[355, 534], [-4, 2], [0, 5], [-3, 3], [-4, 0], [-4, 8], [-1, 9], [6, 6], [8, 1], [3, -3], [3, 0], [4, -6], [0, -5], [-3, -3], [0, -2], [4, -2], [1, -3], [-10, -10]], "is_closed": true}, "area": 564.5, "original_pixel_count": 602, "contour_points": 17, "order": 84}, {"id": 85, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[518, 234], [-1, 2], [0, 5], [-1, 1], [0, 9], [1, 1], [0, 3], [21, 1], [1, 1], [16, 0], [0, -1], [-2, 1], [-9, 0], [-3, -2], [0, -5], [1, -1], [0, -15], [-24, 0]], "is_closed": true}, "area": 548.5, "original_pixel_count": 608, "contour_points": 17, "order": 85}, {"id": 86, "hex": "#e6a089", "contour": {"type": "relative", "data": [[62, 475], [8, 21], [7, -8], [20, 2], [3, -3], [2, 3], [11, 0], [1, -5], [-5, -9], [-33, 0], [-3, 2], [-4, -3], [-7, 0]], "is_closed": true}, "area": 655.5, "original_pixel_count": 633, "contour_points": 12, "order": 86}, {"id": 87, "hex": "#727266", "contour": {"type": "relative", "data": [[180, 485], [-37, 4], [8, -26], [-14, 11], [-4, -10], [9, -5], [-16, -1], [12, 29], [-28, -13], [16, 30], [-43, 2], [-15, -9], [-10, -23], [17, -2], [-18, -1], [20, 51], [9, -16], [31, 0], [9, 12], [-1, -29], [55, -4]], "is_closed": true}, "area": 804.5, "original_pixel_count": 859, "contour_points": 20, "order": 87}, {"id": 88, "hex": "#c3c19e", "contour": {"type": "relative", "data": [[546, 361], [-9, 1], [-6, 7], [-3, 10], [-4, 4], [3, 3], [-1, 4], [-11, -8], [24, 20], [-4, -3], [1, -7], [11, -11], [-2, -4], [4, -1], [1, 2], [5, -5], [-4, 0], [-2, -6], [3, -2], [-6, -4]], "is_closed": true}, "area": 570.5, "original_pixel_count": 613, "contour_points": 19, "order": 88}, {"id": 89, "hex": "#79a9b4", "contour": {"type": "relative", "data": [[501, 522], [0, 4], [1, -4], [13, 1], [7, 7], [51, 0], [-4, -6], [4, -10], [-45, -2], [3, -5], [-20, 5], [6, 6], [0, -4], [17, 0], [-6, 5], [29, 0], [-7, 11], [-8, -5], [-41, -3]], "is_closed": true}, "area": 678.5, "original_pixel_count": 720, "contour_points": 18, "order": 89}, {"id": 90, "hex": "#c3c19e", "contour": {"type": "relative", "data": [[262, 468], [0, 15], [3, 1], [6, -1], [7, -5], [3, 1], [0, 4], [13, -5], [6, 0], [3, 3], [32, -1], [0, -8], [-12, 0], [-2, 3], [-5, -3], [-4, 5], [-4, 0], [-1, -9], [-15, 0], [-1, -4], [-14, -1], [5, 0], [3, 2], [0, 8], [-4, -2], [0, -3], [-4, 0], [-1, 2], [-2, 0], [-2, -3], [-3, 0], [-2, 3], [-2, 0], [-2, -3]], "is_closed": true}, "area": 766.0, "original_pixel_count": 701, "contour_points": 34, "order": 90}, {"id": 91, "hex": "#c3c19e", "contour": {"type": "relative", "data": [[468, 120], [-7, 5], [-5, 0], [-3, 6], [-6, 3], [-7, 7], [-4, 2], [-5, -1], [-4, 2], [-5, 7], [-3, 2], [-4, -1], [-13, 7], [-6, 8], [-8, 4], [17, 0], [8, -7], [3, 0], [13, -12], [4, 0], [1, -3], [9, -8], [3, 0], [7, -6], [8, -3], [6, -7], [1, -5]], "is_closed": true}, "area": 575.5, "original_pixel_count": 628, "contour_points": 26, "order": 91}, {"id": 92, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[94, 523], [-2, 6], [-3, 3], [0, 6], [-3, 6], [4, 2], [0, 2], [-2, 1], [8, -3], [1, 2], [7, 1], [5, 4], [3, -3], [1, -6], [-4, -3], [2, -7], [-3, -5], [0, -5], [-14, -1]], "is_closed": true}, "area": 521.0, "original_pixel_count": 564, "contour_points": 18, "order": 92}, {"id": 93, "hex": "#2a6c77", "contour": {"type": "relative", "data": [[0, 299], [0, 16], [5, 4], [6, 1], [7, -3], [8, 4], [3, 5], [8, -6], [7, 0], [5, 3], [3, -2], [-5, -8], [-10, 1], [-5, -6], [-8, -2], [-1, 4], [-9, 3], [-5, -10], [-6, 1], [-3, -5]], "is_closed": true}, "area": 543.5, "original_pixel_count": 585, "contour_points": 19, "order": 93}, {"id": 94, "hex": "#f7ead2", "contour": {"type": "relative", "data": [[484, 417], [0, 22], [10, -12], [4, -1], [4, 6], [-3, 5], [3, 4], [0, -13], [7, -7], [10, 5], [1, 13], [10, -10], [-1, -12], [-6, 4], [-5, -5], [-25, 0], [-2, 5], [-5, -6], [-2, 2]], "is_closed": true}, "area": 556.0, "original_pixel_count": 600, "contour_points": 18, "order": 94}, {"id": 95, "hex": "#e6a089", "contour": {"type": "relative", "data": [[422, 256], [1, 55], [12, 2], [5, -5], [1, -4], [-5, -7], [-14, -41]], "is_closed": true}, "area": 512.5, "original_pixel_count": 568, "contour_points": 6, "order": 95}, {"id": 96, "hex": "#79a9b4", "contour": {"type": "relative", "data": [[362, 126], [-38, 5], [-18, 6], [-23, -6], [-5, 2], [-9, 12], [-42, 27], [4, 0], [4, -5], [4, 2], [9, -10], [17, -5], [3, -5], [7, 1], [0, 7], [-4, 1], [-9, 14], [18, -18], [8, -21], [20, 5], [54, -12]], "is_closed": true}, "area": 535.5, "original_pixel_count": 682, "contour_points": 20, "order": 96}], "palette": [{"id": 1, "color_hex": "#96bbc3", "color_rgb": [150, 187, 195], "name": "颜色1", "usage_count": 38415}, {"id": 2, "color_hex": "#79a9b4", "color_rgb": [121, 169, 180], "name": "颜色2", "usage_count": 23904}, {"id": 3, "color_hex": "#f7ead2", "color_rgb": [247, 234, 210], "name": "颜色3", "usage_count": 42476}, {"id": 4, "color_hex": "#f1d2b7", "color_rgb": [241, 210, 183], "name": "颜色4", "usage_count": 38823}, {"id": 5, "color_hex": "#c1cecc", "color_rgb": [193, 206, 204], "name": "颜色5", "usage_count": 28099}, {"id": 6, "color_hex": "#2a6c77", "color_rgb": [42, 108, 119], "name": "颜色6", "usage_count": 13014}, {"id": 7, "color_hex": "#469ca6", "color_rgb": [70, 156, 166], "name": "颜色7", "usage_count": 11860}, {"id": 8, "color_hex": "#e6a089", "color_rgb": [230, 160, 137], "name": "颜色8", "usage_count": 13858}, {"id": 9, "color_hex": "#c3c19e", "color_rgb": [195, 193, 158], "name": "颜色9", "usage_count": 15514}, {"id": 10, "color_hex": "#be7968", "color_rgb": [190, 121, 104], "name": "颜色10", "usage_count": 7607}, {"id": 11, "color_hex": "#949c85", "color_rgb": [148, 156, 133], "name": "颜色11", "usage_count": 5422}, {"id": 12, "color_hex": "#727266", "color_rgb": [114, 114, 102], "name": "颜色12", "usage_count": 3471}]}