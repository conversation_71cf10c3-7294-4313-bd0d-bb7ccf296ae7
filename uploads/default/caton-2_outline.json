{"meta": {"version": "3.0", "compression_type": "contour", "regions": 195, "colors": 12, "size": [768, 894], "simplification_level": 0.03495850622406639, "coordinate_compression": "relative"}, "regions": [{"id": 1, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[523, 78], [-208, -78], [-298, 101], [-17, 70], [96, -8], [14, -53], [19, 59], [171, -1], [11, -80], [8, 88], [59, -58], [28, 63], [28, -49], [64, 30], [25, -84]], "is_closed": true}, "area": 56047.0, "original_pixel_count": 51488, "contour_points": 14, "order": 1}, {"id": 2, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[767, 40], [-136, -22], [-66, 46], [36, 14], [-76, 1], [1, 48], [50, 35], [16, -71], [29, 62], [39, -72], [28, 69], [79, -3], [0, -107]], "is_closed": true}, "area": 22358.5, "original_pixel_count": 23068, "contour_points": 12, "order": 2}, {"id": 3, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[358, 337], [-32, 240], [40, -78], [83, 96], [25, -145], [-116, -113]], "is_closed": true}, "area": 19713.5, "original_pixel_count": 19643, "contour_points": 5, "order": 3}, {"id": 4, "hex": "#ab7056", "contour": {"type": "relative", "data": [[155, 365], [-43, 72], [2, 135], [60, 14], [36, -147], [-55, -74]], "is_closed": true}, "area": 14846.0, "original_pixel_count": 13955, "contour_points": 5, "order": 4}, {"id": 5, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[0, 107], [63, -50], [96, 17], [20, -59], [68, -15], [-247, 0], [0, 107]], "is_closed": true}, "area": 12854.0, "original_pixel_count": 13214, "contour_points": 6, "order": 5}, {"id": 6, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[3, 179], [3, 48], [55, -38], [93, 44], [57, -50], [-5, 43], [63, 22], [27, -73], [-159, -13], [35, 45], [-37, -4], [-25, -80], [-36, 68], [-11, -35], [-60, 23]], "is_closed": true}, "area": 13542.5, "original_pixel_count": 12343, "contour_points": 14, "order": 6}, {"id": 7, "hex": "#eae8df", "contour": {"type": "relative", "data": [[387, 6], [58, 58], [68, 6], [10, 16], [24, -16], [50, 9], [-11, -9], [27, -16], [-46, 16], [0, -40], [-74, -30], [-93, 0], [3, 19], [-16, -13]], "is_closed": true}, "area": 9684.0, "original_pixel_count": 10668, "contour_points": 13, "order": 7}, {"id": 8, "hex": "#bd3421", "contour": {"type": "relative", "data": [[239, 504], [-34, 74], [43, -13], [59, 113], [33, -28], [-49, -144], [-26, 29], [-26, -31]], "is_closed": true}, "area": 9399.0, "original_pixel_count": 9860, "contour_points": 7, "order": 8}, {"id": 9, "hex": "#eae8df", "contour": {"type": "relative", "data": [[334, 0], [-153, 15], [-22, 60], [-96, -17], [-63, 57], [143, -24], [76, -60], [33, 9], [82, -40]], "is_closed": true}, "area": 8205.5, "original_pixel_count": 8357, "contour_points": 8, "order": 9}, {"id": 10, "hex": "#dd835f", "contour": {"type": "relative", "data": [[739, 508], [-147, 1], [34, 10], [1, 55], [-32, 2], [-3, -37], [-1, 71], [32, -24], [4, 49], [2, -49], [30, -1], [7, 50], [1, -48], [27, -2], [8, 50], [2, -47], [29, -11], [-28, -7], [0, -51], [34, -11]], "is_closed": true}, "area": 8089.5, "original_pixel_count": 8332, "contour_points": 19, "order": 10}, {"id": 11, "hex": "#254c65", "contour": {"type": "relative", "data": [[405, 724], [-26, 65], [12, 7], [18, -15], [33, 20], [72, -16], [5, 32], [5, -34], [-34, -19], [-14, -33], [-56, -15], [-15, 8]], "is_closed": true}, "area": 7158.0, "original_pixel_count": 7656, "contour_points": 11, "order": 11}, {"id": 12, "hex": "#ab7056", "contour": {"type": "relative", "data": [[460, 476], [123, -3], [-31, -67], [-61, 0], [-31, 70]], "is_closed": true}, "area": 6302.0, "original_pixel_count": 6814, "contour_points": 4, "order": 12}, {"id": 13, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[494, 0], [58, 15], [16, 53], [64, -55], [135, 18], [0, -31], [-273, 0]], "is_closed": true}, "area": 6661.0, "original_pixel_count": 6730, "contour_points": 6, "order": 13}, {"id": 14, "hex": "#87ada9", "contour": {"type": "relative", "data": [[332, 201], [11, 44], [42, -31], [12, 40], [-55, 21], [63, 18], [-93, 48], [75, -39], [55, 66], [-22, -50], [52, 13], [-79, -24], [33, -80], [-94, -26]], "is_closed": true}, "area": 4123.0, "original_pixel_count": 5639, "contour_points": 13, "order": 14}, {"id": 15, "hex": "#dd835f", "contour": {"type": "relative", "data": [[431, 380], [188, -30], [-133, -28], [-55, 58]], "is_closed": true}, "area": 4627.0, "original_pixel_count": 4798, "contour_points": 3, "order": 15}, {"id": 16, "hex": "#87ada9", "contour": {"type": "relative", "data": [[339, 280], [-191, 54], [140, 10], [51, -64]], "is_closed": true}, "area": 4735.0, "original_pixel_count": 4407, "contour_points": 3, "order": 16}, {"id": 17, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[381, 122], [-8, 84], [88, 30], [-49, -26], [55, -14], [17, 57], [8, -91], [-68, -4], [-12, 34], [-31, -70]], "is_closed": true}, "area": 6003.5, "original_pixel_count": 4706, "contour_points": 9, "order": 17}, {"id": 18, "hex": "#dd835f", "contour": {"type": "relative", "data": [[212, 667], [-11, 33], [21, -9], [138, 137], [176, -2], [-171, -4], [-85, -82], [30, -25], [-54, -1], [-44, -47]], "is_closed": true}, "area": 3021.0, "original_pixel_count": 4253, "contour_points": 9, "order": 18}, {"id": 19, "hex": "#dd835f", "contour": {"type": "relative", "data": [[40, 405], [-40, 40], [0, 69], [74, -1], [21, -27], [-19, -22], [-11, 37], [-50, -5], [2, -51], [56, 4], [-33, -44]], "is_closed": true}, "area": 4113.0, "original_pixel_count": 4050, "contour_points": 10, "order": 19}, {"id": 20, "hex": "#dd835f", "contour": {"type": "relative", "data": [[602, 386], [47, 1], [2, -36], [17, -7], [9, 44], [46, -1], [-28, -20], [-13, -41], [-22, -17], [-58, 77]], "is_closed": true}, "area": 3729.5, "original_pixel_count": 3645, "contour_points": 9, "order": 20}, {"id": 21, "hex": "#4098a0", "contour": {"type": "relative", "data": [[405, 292], [-44, -34], [-73, 78], [52, 6], [6, -18], [-22, 4], [31, -28], [50, -8]], "is_closed": true}, "area": 3207.0, "original_pixel_count": 3485, "contour_points": 7, "order": 21}, {"id": 22, "hex": "#ab7056", "contour": {"type": "relative", "data": [[767, 405], [-27, 100], [-152, 1], [2, 118], [2, -116], [143, -2], [3, 121], [29, -56], [-20, -124], [20, -42]], "is_closed": true}, "area": 3052.5, "original_pixel_count": 3569, "contour_points": 9, "order": 22}, {"id": 23, "hex": "#dd835f", "contour": {"type": "relative", "data": [[261, 588], [-15, 41], [4, 32], [11, 5], [-9, 10], [51, -3], [-26, -66], [-16, -19]], "is_closed": true}, "area": 2911.5, "original_pixel_count": 2992, "contour_points": 7, "order": 23}, {"id": 24, "hex": "#87ada9", "contour": {"type": "relative", "data": [[604, 452], [-2, 44], [136, 0], [-2, -45], [-18, 40], [-3, -40], [-50, 38], [-61, -37]], "is_closed": true}, "area": 3469.5, "original_pixel_count": 3221, "contour_points": 7, "order": 24}, {"id": 25, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[47, 388], [45, 52], [0, 23], [16, 5], [3, -79], [-64, -1]], "is_closed": true}, "area": 2499.5, "original_pixel_count": 2833, "contour_points": 5, "order": 25}, {"id": 26, "hex": "#87ada9", "contour": {"type": "relative", "data": [[7, 288], [-7, 25], [37, 15], [-17, 17], [35, 19], [-10, -18], [29, -12], [40, 23], [17, 46], [-14, -75], [-20, -4], [20, -11], [-58, -12], [-3, 21], [22, 1], [-31, 21], [-40, -56]], "is_closed": true}, "area": 3141.0, "original_pixel_count": 3025, "contour_points": 16, "order": 26}, {"id": 27, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[264, 337], [-36, 83], [41, -30], [19, 7], [20, 43], [11, -11], [-39, -56], [-1, -34], [-15, -2]], "is_closed": true}, "area": 2540.0, "original_pixel_count": 2627, "contour_points": 8, "order": 27}, {"id": 28, "hex": "#eae8df", "contour": {"type": "relative", "data": [[656, 95], [-35, 59], [-15, 0], [-3, -35], [1, 75], [-47, -5], [-26, -26], [14, 35], [23, -9], [30, 23], [28, -22], [12, 10], [18, -105]], "is_closed": true}, "area": 3118.5, "original_pixel_count": 2692, "contour_points": 12, "order": 28}, {"id": 29, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[540, 215], [-44, 8], [10, 80], [19, -24], [13, 18], [-26, -17], [29, 1], [-1, -66]], "is_closed": true}, "area": 2652.5, "original_pixel_count": 2634, "contour_points": 7, "order": 29}, {"id": 30, "hex": "#4098a0", "contour": {"type": "relative", "data": [[46, 345], [23, 37], [8, -10], [15, 0], [9, 12], [17, 3], [1, 15], [11, 0], [-1, -21], [-17, -25], [-16, 0], [-6, -12], [-14, 5], [-8, -17], [-22, 13]], "is_closed": true}, "area": 2254.5, "original_pixel_count": 2428, "contour_points": 14, "order": 30}, {"id": 31, "hex": "#eae8df", "contour": {"type": "relative", "data": [[767, 398], [-32, 49], [-45, -1], [-3, -37], [-26, 1], [2, 37], [-73, 1], [50, 0], [1, 35], [47, 0], [2, -33], [48, 0], [2, 15], [27, -67]], "is_closed": true}, "area": 3312.5, "original_pixel_count": 2612, "contour_points": 13, "order": 31}, {"id": 32, "hex": "#6b8283", "contour": {"type": "relative", "data": [[587, 443], [13, -37], [7, 17], [30, 3], [2, 17], [2, -36], [48, 0], [24, 15], [1, -24], [-127, -1], [0, 46]], "is_closed": true}, "area": 2285.5, "original_pixel_count": 2436, "contour_points": 10, "order": 32}, {"id": 33, "hex": "#87ada9", "contour": {"type": "relative", "data": [[95, 252], [59, 45], [28, -15], [-15, -25], [-20, 5], [-33, -21], [-19, 11]], "is_closed": true}, "area": 2086.0, "original_pixel_count": 2194, "contour_points": 6, "order": 33}, {"id": 34, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[737, 157], [-43, 1], [25, 52], [-24, -12], [-17, 36], [29, -6], [-25, -6], [13, -24], [16, 29], [32, -18], [-6, -52]], "is_closed": true}, "area": 2341.5, "original_pixel_count": 2242, "contour_points": 10, "order": 34}, {"id": 35, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[338, 260], [-35, 16], [31, -26], [-54, 1], [-11, 46], [-65, -27], [-36, 43], [55, -26], [-5, 18], [22, -17], [29, 16], [28, -30], [41, 8], [0, -22]], "is_closed": true}, "area": 2927.5, "original_pixel_count": 2395, "contour_points": 13, "order": 35}, {"id": 36, "hex": "#4098a0", "contour": {"type": "relative", "data": [[555, 317], [12, 16], [55, 0], [13, -25], [-16, -6], [-25, 17], [0, -20], [-17, -7], [-22, 25]], "is_closed": true}, "area": 2016.5, "original_pixel_count": 2022, "contour_points": 8, "order": 36}, {"id": 37, "hex": "#eae8df", "contour": {"type": "relative", "data": [[20, 450], [1, 44], [41, 1], [0, -45], [-42, 0]], "is_closed": true}, "area": 1846.5, "original_pixel_count": 1977, "contour_points": 4, "order": 37}, {"id": 38, "hex": "#ab7056", "contour": {"type": "relative", "data": [[247, 565], [-21, 58], [13, 51], [-24, 6], [82, 5], [7, -12], [-51, 4], [47, -11], [-51, -5], [-2, -96]], "is_closed": true}, "area": 2497.5, "original_pixel_count": 2128, "contour_points": 9, "order": 38}, {"id": 39, "hex": "#eae8df", "contour": {"type": "relative", "data": [[663, 94], [18, 119], [14, -16], [72, 14], [-18, -3], [-3, -41], [21, -12], [-37, -6], [13, 61], [-42, -15], [-9, -37], [15, -7], [-20, -2], [-24, -55]], "is_closed": true}, "area": 2386.0, "original_pixel_count": 2177, "contour_points": 13, "order": 39}, {"id": 40, "hex": "#87ada9", "contour": {"type": "relative", "data": [[0, 264], [0, 16], [21, 4], [18, 19], [11, -1], [18, -12], [-13, -23], [-17, 6], [-23, -15], [-15, 6]], "is_closed": true}, "area": 1628.5, "original_pixel_count": 1806, "contour_points": 9, "order": 40}, {"id": 41, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[183, 267], [-98, -42], [-22, 72], [-63, 1], [51, 45], [26, -20], [-22, -21], [45, 5], [-32, -11], [3, -48], [58, 36], [-19, -43], [59, 16], [-14, 41], [28, -31]], "is_closed": true}, "area": 3563.0, "original_pixel_count": 2321, "contour_points": 14, "order": 41}, {"id": 42, "hex": "#254c65", "contour": {"type": "relative", "data": [[286, 433], [-28, -1], [-13, 10], [-10, 50], [3, -7], [5, 11], [17, 2], [2, -18], [-15, -18], [8, 4], [18, -29], [18, 26], [8, -13], [-13, -17]], "is_closed": true}, "area": 1670.5, "original_pixel_count": 1779, "contour_points": 13, "order": 42}, {"id": 43, "hex": "#4098a0", "contour": {"type": "relative", "data": [[391, 302], [24, 18], [21, -8], [36, 18], [12, -20], [12, 4], [5, -7], [-17, -1], [-2, -9], [-20, 10], [-15, -8], [-9, 5], [-4, -10], [-19, 1], [-4, 10], [-20, -3]], "is_closed": true}, "area": 1619.0, "original_pixel_count": 1773, "contour_points": 15, "order": 43}, {"id": 44, "hex": "#4098a0", "contour": {"type": "relative", "data": [[702, 802], [23, 36], [-40, 51], [13, -18], [4, 22], [20, 0], [-11, -27], [11, -6], [7, 22], [38, -19], [0, -29], [-31, 29], [-1, -20], [-12, 5], [9, -35], [-30, -11]], "is_closed": true}, "area": 2165.5, "original_pixel_count": 1838, "contour_points": 15, "order": 44}, {"id": 45, "hex": "#eae8df", "contour": {"type": "relative", "data": [[0, 631], [0, 9], [213, 0], [-213, -9]], "is_closed": true}, "area": 958.5, "original_pixel_count": 1768, "contour_points": 3, "order": 45}, {"id": 46, "hex": "#bd3421", "contour": {"type": "relative", "data": [[309, 132], [-31, 116], [26, 0], [9, -16], [-4, -100]], "is_closed": true}, "area": 1990.0, "original_pixel_count": 1651, "contour_points": 4, "order": 46}, {"id": 47, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[581, 109], [-2, 64], [-17, 5], [-5, -15], [-18, 8], [-9, -25], [0, 26], [19, 16], [5, -8], [27, 11], [2, -20], [16, 1], [-1, -50], [-16, -1], [-1, -12]], "is_closed": true}, "area": 1698.0, "original_pixel_count": 1723, "contour_points": 14, "order": 47}, {"id": 48, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[672, 146], [-27, 15], [-6, 70], [27, -42], [12, 41], [-6, -84]], "is_closed": true}, "area": 1735.5, "original_pixel_count": 1699, "contour_points": 5, "order": 48}, {"id": 49, "hex": "#eae8df", "contour": {"type": "relative", "data": [[563, 657], [204, -12], [-199, -4], [-5, 16]], "is_closed": true}, "area": 1602.0, "original_pixel_count": 1681, "contour_points": 3, "order": 49}, {"id": 50, "hex": "#87ada9", "contour": {"type": "relative", "data": [[700, 352], [22, 25], [35, 2], [10, -40], [-48, 5], [14, 33], [-33, -25]], "is_closed": true}, "area": 1465.5, "original_pixel_count": 1520, "contour_points": 6, "order": 50}, {"id": 51, "hex": "#254c65", "contour": {"type": "relative", "data": [[520, 106], [-11, 44], [2, 25], [-18, 39], [34, -2], [-7, -1], [9, -57], [-8, -6], [-1, -42]], "is_closed": true}, "area": 1485.0, "original_pixel_count": 1512, "contour_points": 8, "order": 51}, {"id": 52, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[532, 554], [2, 43], [37, 1], [2, -44], [-2, 12], [-10, -12], [-4, 12], [-5, -11], [-17, 11], [-3, -12]], "is_closed": true}, "area": 1439.5, "original_pixel_count": 1461, "contour_points": 9, "order": 52}, {"id": 53, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[489, 393], [60, -1], [-15, -26], [-18, -4], [-19, 16], [-8, 15]], "is_closed": true}, "area": 1199.0, "original_pixel_count": 1339, "contour_points": 5, "order": 53}, {"id": 54, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[174, 335], [1, 6], [-7, 4], [12, 17], [-6, 5], [38, 12], [5, -9], [3, 3], [-4, -13], [-9, -4], [2, -4], [-15, -18], [-20, 1]], "is_closed": true}, "area": 1338.0, "original_pixel_count": 1357, "contour_points": 12, "order": 54}, {"id": 55, "hex": "#ab7056", "contour": {"type": "relative", "data": [[39, 395], [-39, 50], [46, -37], [25, 32], [-41, 7], [35, 1], [0, 47], [10, -31], [32, 19], [1, 30], [0, -38], [-24, -10], [-45, -70]], "is_closed": true}, "area": 1133.0, "original_pixel_count": 1538, "contour_points": 12, "order": 55}, {"id": 56, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[656, 828], [-36, 6], [-1, 15], [-21, -11], [41, -17], [-76, 8], [4, 23], [49, 0], [2, 14], [4, -22], [34, -16]], "is_closed": true}, "area": 1510.5, "original_pixel_count": 1418, "contour_points": 10, "order": 56}, {"id": 57, "hex": "#dd835f", "contour": {"type": "relative", "data": [[566, 256], [9, 2], [8, -8], [3, 7], [31, 1], [2, -7], [-27, -38], [-26, 43]], "is_closed": true}, "area": 1236.0, "original_pixel_count": 1303, "contour_points": 7, "order": 57}, {"id": 58, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[327, 744], [0, 27], [35, 17], [-1, -44], [-34, 0]], "is_closed": true}, "area": 1220.5, "original_pixel_count": 1281, "contour_points": 4, "order": 58}, {"id": 59, "hex": "#eae8df", "contour": {"type": "relative", "data": [[285, 399], [-28, 0], [0, 29], [-31, 17], [-4, 60], [-8, -72], [-2, 125], [48, -158], [26, 2], [-16, 25], [28, 12], [4, 31], [-17, -71]], "is_closed": true}, "area": 1995.0, "original_pixel_count": 1591, "contour_points": 12, "order": 59}, {"id": 60, "hex": "#dd835f", "contour": {"type": "relative", "data": [[324, 518], [4, 52], [4, -9], [120, -3], [-42, -10], [-33, 5], [-6, -10], [-10, 10], [-28, 0], [-9, -35]], "is_closed": true}, "area": 1114.0, "original_pixel_count": 1373, "contour_points": 9, "order": 60}, {"id": 61, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[355, 142], [-16, 33], [-10, -14], [-9, 10], [23, 73], [-14, -41], [15, 1], [2, -12], [13, 8], [-15, -9], [16, -5], [-5, -44]], "is_closed": true}, "area": 1193.5, "original_pixel_count": 1387, "contour_points": 11, "order": 61}, {"id": 62, "hex": "#87ada9", "contour": {"type": "relative", "data": [[628, 192], [-21, 11], [1, 14], [-7, -4], [14, 24], [24, -5], [-2, -30], [-9, -10]], "is_closed": true}, "area": 1128.5, "original_pixel_count": 1214, "contour_points": 7, "order": 62}, {"id": 63, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[487, 551], [18, 1], [-2, 13], [-17, -12], [2, 38], [6, 1], [5, -13], [15, 4], [-3, 18], [13, -5], [-1, -42], [-36, -3]], "is_closed": true}, "area": 1182.0, "original_pixel_count": 1288, "contour_points": 11, "order": 63}, {"id": 64, "hex": "#87ada9", "contour": {"type": "relative", "data": [[483, 717], [7, 42], [35, 11], [0, -39], [-29, 16], [16, -35], [-29, 5]], "is_closed": true}, "area": 1444.0, "original_pixel_count": 1314, "contour_points": 6, "order": 64}, {"id": 65, "hex": "#254c65", "contour": {"type": "relative", "data": [[55, 822], [16, 23], [-29, -1], [2, 16], [32, 20], [-4, -43], [38, -4], [-55, -11]], "is_closed": true}, "area": 1173.0, "original_pixel_count": 1265, "contour_points": 7, "order": 65}, {"id": 66, "hex": "#dd835f", "contour": {"type": "relative", "data": [[2, 716], [2, 7], [7, -1], [14, 8], [9, -3], [19, -20], [-7, -6], [-18, 1], [-4, -10], [-9, -1], [-13, 25]], "is_closed": true}, "area": 1128.0, "original_pixel_count": 1195, "contour_points": 10, "order": 66}, {"id": 67, "hex": "#4098a0", "contour": {"type": "relative", "data": [[379, 220], [-42, 10], [6, 40], [18, -20], [38, 4], [-23, -11], [3, -23]], "is_closed": true}, "area": 1285.0, "original_pixel_count": 1185, "contour_points": 6, "order": 67}, {"id": 68, "hex": "#ab7056", "contour": {"type": "relative", "data": [[526, 652], [33, 1], [8, -15], [200, -1], [-193, -1], [-42, -18], [21, 19], [-27, 15]], "is_closed": true}, "area": 794.0, "original_pixel_count": 1365, "contour_points": 7, "order": 68}, {"id": 69, "hex": "#bd3421", "contour": {"type": "relative", "data": [[502, 604], [1, 15], [6, 4], [-3, 14], [10, 0], [7, 12], [5, 1], [1, -10], [26, -4], [-6, -3], [0, -9], [-14, 0], [-15, -12], [-14, -1], [-4, -7]], "is_closed": true}, "area": 995.0, "original_pixel_count": 1133, "contour_points": 14, "order": 69}, {"id": 70, "hex": "#dd835f", "contour": {"type": "relative", "data": [[134, 867], [-3, -3], [-13, 14], [1, -23], [-19, -2], [-5, 18], [-8, -15], [-6, 30], [8, 4], [32, -7], [13, -16]], "is_closed": true}, "area": 1124.0, "original_pixel_count": 1144, "contour_points": 10, "order": 70}, {"id": 71, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[464, 351], [-18, 23], [-18, 1], [6, 26], [31, -1], [-6, 16], [14, 4], [22, -64], [-22, 16], [9, 16], [-12, -11], [-36, 11], [-6, -13], [24, 2], [12, -26]], "is_closed": true}, "area": 1572.5, "original_pixel_count": 1288, "contour_points": 14, "order": 71}, {"id": 72, "hex": "#87ada9", "contour": {"type": "relative", "data": [[530, 318], [20, -12], [-11, 14], [28, 14], [63, 0], [-6, -13], [10, 1], [11, -19], [-21, -7], [-29, 13], [0, -14], [-27, -16], [-31, 20], [-7, 19]], "is_closed": true}, "area": 3753.0, "original_pixel_count": 1360, "contour_points": 13, "order": 72}, {"id": 73, "hex": "#87ada9", "contour": {"type": "relative", "data": [[201, 735], [-35, 72], [35, -18], [-22, 19], [26, -2], [-4, -27], [11, -12], [-19, 9], [26, -24], [-18, -17]], "is_closed": true}, "area": 1515.5, "original_pixel_count": 1201, "contour_points": 9, "order": 73}, {"id": 74, "hex": "#dd835f", "contour": {"type": "relative", "data": [[10, 804], [-1, 17], [4, 8], [24, 14], [10, -5], [2, -7], [-7, -18], [-19, 0], [-4, -11], [-9, 2]], "is_closed": true}, "area": 967.5, "original_pixel_count": 1057, "contour_points": 9, "order": 74}, {"id": 75, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[108, 573], [1, -99], [-21, 11], [-14, 29], [-69, 3], [101, 0], [2, 56]], "is_closed": true}, "area": 1198.5, "original_pixel_count": 1248, "contour_points": 6, "order": 75}, {"id": 76, "hex": "#dd835f", "contour": {"type": "relative", "data": [[702, 763], [-13, 11], [0, 12], [28, 11], [14, -14], [2, -12], [-12, -3], [-9, 2], [-10, -7]], "is_closed": true}, "area": 965.5, "original_pixel_count": 1045, "contour_points": 8, "order": 76}, {"id": 77, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[220, 427], [24, 9], [14, -39], [28, 0], [3, 31], [15, 2], [5, 17], [11, -5], [-18, -39], [17, 25], [-14, 13], [-18, -44], [-20, -7], [-47, 37]], "is_closed": true}, "area": 821.5, "original_pixel_count": 1201, "contour_points": 13, "order": 77}, {"id": 78, "hex": "#eae8df", "contour": {"type": "relative", "data": [[543, 732], [-15, 4], [-2, 57], [12, -2], [13, -37], [-8, -22]], "is_closed": true}, "area": 1063.5, "original_pixel_count": 1051, "contour_points": 5, "order": 78}, {"id": 79, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[673, 189], [-9, 21], [-18, -5], [-7, 28], [36, 7], [-2, -51]], "is_closed": true}, "area": 1021.5, "original_pixel_count": 1073, "contour_points": 5, "order": 79}, {"id": 80, "hex": "#254c65", "contour": {"type": "relative", "data": [[387, 861], [45, 13], [76, -7], [1, 26], [7, -25], [18, -4], [-106, -6], [-2, -24], [-3, 27], [-36, 0]], "is_closed": true}, "area": 1330.5, "original_pixel_count": 1163, "contour_points": 9, "order": 80}, {"id": 81, "hex": "#87ada9", "contour": {"type": "relative", "data": [[313, 799], [-6, 11], [12, 4], [1, 21], [-8, -11], [2, 13], [-14, 3], [2, 11], [-14, -3], [-2, 18], [9, 5], [18, -18], [5, 40], [10, 0], [-15, -94]], "is_closed": true}, "area": 1049.0, "original_pixel_count": 1186, "contour_points": 14, "order": 81}, {"id": 82, "hex": "#4098a0", "contour": {"type": "relative", "data": [[3, 309], [-3, 38], [33, -6], [1, -19], [-31, -13]], "is_closed": true}, "area": 919.0, "original_pixel_count": 1021, "contour_points": 4, "order": 82}, {"id": 83, "hex": "#dd835f", "contour": {"type": "relative", "data": [[23, 739], [-1, 21], [25, 8], [-1, 14], [15, 0], [2, -26], [-15, -16], [-9, 7], [-16, -8]], "is_closed": true}, "area": 1044.0, "original_pixel_count": 1042, "contour_points": 8, "order": 83}, {"id": 84, "hex": "#eae8df", "contour": {"type": "relative", "data": [[564, 786], [41, 11], [-5, -9], [33, 3], [-7, -16], [-31, -15], [5, 10], [-22, -2], [15, 11], [-3, 6], [-23, -8], [-3, 9]], "is_closed": true}, "area": 1125.0, "original_pixel_count": 1098, "contour_points": 11, "order": 84}, {"id": 85, "hex": "#87ada9", "contour": {"type": "relative", "data": [[309, 452], [-10, 54], [16, 49], [3, -100], [-9, -3]], "is_closed": true}, "area": 1131.5, "original_pixel_count": 1043, "contour_points": 4, "order": 85}, {"id": 86, "hex": "#4098a0", "contour": {"type": "relative", "data": [[179, 863], [-3, 13], [22, 17], [22, -8], [-9, -21], [-32, -1]], "is_closed": true}, "area": 906.0, "original_pixel_count": 998, "contour_points": 5, "order": 86}, {"id": 87, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[409, 278], [4, 11], [3, -9], [8, -2], [7, 9], [15, 5], [6, -8], [6, 1], [4, 9], [6, -2], [-4, -11], [-7, -3], [-1, -11], [-13, 1], [-8, -7], [-19, 8], [-7, 9]], "is_closed": true}, "area": 972.5, "original_pixel_count": 1007, "contour_points": 16, "order": 87}, {"id": 88, "hex": "#eae8df", "contour": {"type": "relative", "data": [[517, 98], [-19, 63], [-58, -18], [4, 28], [36, -19], [22, 23], [-12, 8], [-5, -11], [-1, 17], [16, -2], [-9, 11], [-27, -4], [16, 20], [28, -39], [9, -77]], "is_closed": true}, "area": 1468.5, "original_pixel_count": 1163, "contour_points": 14, "order": 88}, {"id": 89, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[578, 532], [-6, 8], [-41, 0], [-5, -50], [-4, 48], [-39, 11], [0, 22], [2, -23], [88, 5], [5, -21]], "is_closed": true}, "area": 1092.0, "original_pixel_count": 1113, "contour_points": 9, "order": 89}, {"id": 90, "hex": "#6b8283", "contour": {"type": "relative", "data": [[173, 445], [2, 38], [22, -1], [1, -37], [-25, 0]], "is_closed": true}, "area": 881.5, "original_pixel_count": 961, "contour_points": 4, "order": 90}, {"id": 91, "hex": "#4098a0", "contour": {"type": "relative", "data": [[177, 269], [-19, -10], [-15, 6], [-16, -2], [-8, 9], [10, 9], [6, -8], [16, 6], [6, 13], [17, -6], [-13, -17], [12, 4], [4, -4]], "is_closed": true}, "area": 887.5, "original_pixel_count": 981, "contour_points": 12, "order": 91}, {"id": 92, "hex": "#eae8df", "contour": {"type": "relative", "data": [[556, 406], [9, 40], [20, 2], [-1, 15], [-13, -8], [15, 49], [153, -2], [-150, -4], [-4, -62], [-16, 0], [5, -16], [-12, 13], [-6, -27]], "is_closed": true}, "area": 1164.0, "original_pixel_count": 1199, "contour_points": 12, "order": 92}, {"id": 93, "hex": "#4098a0", "contour": {"type": "relative", "data": [[125, 498], [-4, 2], [1, 50], [15, 2], [1, -51], [-13, -3]], "is_closed": true}, "area": 827.0, "original_pixel_count": 958, "contour_points": 5, "order": 93}, {"id": 94, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[666, 245], [-5, 27], [-37, -1], [-2, -13], [-61, 7], [11, 5], [-6, -10], [26, 0], [15, 8], [2, 13], [33, -3], [5, 13], [11, 0], [8, -46]], "is_closed": true}, "area": 794.0, "original_pixel_count": 1096, "contour_points": 13, "order": 94}, {"id": 95, "hex": "#6b8283", "contour": {"type": "relative", "data": [[274, 754], [3, 40], [-12, 11], [11, 8], [4, 80], [2, -79], [17, 1], [0, -9], [-19, -5], [-6, -47]], "is_closed": true}, "area": 661.0, "original_pixel_count": 1130, "contour_points": 9, "order": 95}, {"id": 96, "hex": "#dd835f", "contour": {"type": "relative", "data": [[432, 249], [8, 6], [7, -9], [10, 7], [1, 9], [25, 8], [-8, 6], [2, 5], [12, -2], [1, 7], [7, -1], [-10, -26], [-19, -3], [-9, -17], [-20, 3], [-7, 7]], "is_closed": true}, "area": 817.5, "original_pixel_count": 969, "contour_points": 15, "order": 96}, {"id": 97, "hex": "#4098a0", "contour": {"type": "relative", "data": [[128, 570], [-6, 3], [1, 47], [9, 2], [8, -6], [0, -42], [-12, -4]], "is_closed": true}, "area": 844.5, "original_pixel_count": 926, "contour_points": 6, "order": 97}, {"id": 98, "hex": "#ab7056", "contour": {"type": "relative", "data": [[618, 247], [6, 22], [38, 0], [-2, -21], [-42, -1]], "is_closed": true}, "area": 858.0, "original_pixel_count": 920, "contour_points": 4, "order": 98}, {"id": 99, "hex": "#87ada9", "contour": {"type": "relative", "data": [[127, 705], [26, 2], [-9, 6], [8, 6], [-4, 50], [12, -17], [0, 27], [16, -27], [-9, 1], [-7, -36], [28, 0], [-61, -12]], "is_closed": true}, "area": 879.5, "original_pixel_count": 1059, "contour_points": 11, "order": 99}, {"id": 100, "hex": "#4098a0", "contour": {"type": "relative", "data": [[476, 218], [-4, -15], [-9, -6], [-11, 10], [-23, -6], [-16, 10], [15, 3], [6, 11], [14, -9], [13, 3], [3, -6], [4, 8], [1, -4], [7, 1]], "is_closed": true}, "area": 824.5, "original_pixel_count": 934, "contour_points": 13, "order": 100}, {"id": 101, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[645, 829], [-1, 11], [-18, 1], [-8, 18], [8, 12], [13, -5], [-11, -5], [8, -16], [18, 9], [-7, 20], [-17, 4], [8, 7], [18, -10], [6, -38], [-17, 5], [0, -13]], "is_closed": true}, "area": 880.0, "original_pixel_count": 996, "contour_points": 15, "order": 101}, {"id": 102, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[507, 673], [-18, 41], [21, -3], [-7, 13], [13, 7], [-19, 1], [-1, 15], [27, -10], [-16, -64]], "is_closed": true}, "area": 1030.5, "original_pixel_count": 961, "contour_points": 8, "order": 102}, {"id": 103, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[111, 474], [-2, 45], [-97, -1], [54, 0], [-31, 7], [32, -1], [1, 29], [34, -30], [1, 56], [-34, 4], [36, 7], [-105, 9], [65, -5], [2, 31], [5, -31], [29, 0], [10, 33], [0, -153]], "is_closed": true}, "area": 1991.0, "original_pixel_count": 1538, "contour_points": 17, "order": 103}, {"id": 104, "hex": "#ab7056", "contour": {"type": "relative", "data": [[570, 337], [18, 27], [-9, -11], [6, -11], [25, -3], [9, 10], [-33, 18], [7, 11], [18, -9], [15, -28], [-56, -4]], "is_closed": true}, "area": 633.5, "original_pixel_count": 965, "contour_points": 10, "order": 104}, {"id": 105, "hex": "#dd835f", "contour": {"type": "relative", "data": [[312, 166], [1, 81], [18, -14], [-19, -67]], "is_closed": true}, "area": 736.0, "original_pixel_count": 910, "contour_points": 3, "order": 105}, {"id": 106, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[662, 346], [-9, 8], [0, 32], [21, 1], [0, -35], [-3, -4], [-9, -2]], "is_closed": true}, "area": 793.5, "original_pixel_count": 873, "contour_points": 6, "order": 106}, {"id": 107, "hex": "#4098a0", "contour": {"type": "relative", "data": [[0, 761], [0, 21], [6, 8], [14, 7], [-2, -8], [3, -1], [3, 10], [6, -1], [5, -15], [-4, -8], [-31, -13]], "is_closed": true}, "area": 811.5, "original_pixel_count": 884, "contour_points": 10, "order": 107}, {"id": 108, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[256, 421], [-12, 16], [-18, -17], [-17, 14], [3, 119], [-21, 3], [19, 1], [-10, 10], [13, -9], [0, -51], [16, -8], [-11, -43], [38, -35]], "is_closed": true}, "area": 1440.5, "original_pixel_count": 1108, "contour_points": 12, "order": 108}, {"id": 109, "hex": "#eae8df", "contour": {"type": "relative", "data": [[378, 454], [-1, 31], [23, 0], [0, -32], [-14, -3], [-8, 4]], "is_closed": true}, "area": 748.5, "original_pixel_count": 840, "contour_points": 5, "order": 109}, {"id": 110, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[456, 609], [-3, 25], [19, 11], [-2, 16], [15, -7], [-6, -29], [-23, -16]], "is_closed": true}, "area": 814.5, "original_pixel_count": 858, "contour_points": 6, "order": 110}, {"id": 111, "hex": "#6b8283", "contour": {"type": "relative", "data": [[737, 453], [-17, -1], [-2, 43], [-46, 1], [65, 0], [0, -43]], "is_closed": true}, "area": 807.5, "original_pixel_count": 894, "contour_points": 5, "order": 111}, {"id": 112, "hex": "#dd835f", "contour": {"type": "relative", "data": [[82, 271], [0, 10], [25, 1], [15, 14], [11, 0], [16, 10], [-5, -16], [-19, -2], [-20, -23], [-23, 6]], "is_closed": true}, "area": 769.5, "original_pixel_count": 869, "contour_points": 9, "order": 112}, {"id": 113, "hex": "#dd835f", "contour": {"type": "relative", "data": [[179, 336], [-4, 9], [7, 19], [9, 7], [15, 1], [-2, -8], [3, -6], [-10, -18], [-10, -5], [-8, 1]], "is_closed": true}, "area": 797.5, "original_pixel_count": 830, "contour_points": 9, "order": 113}, {"id": 114, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[507, 869], [-41, 3], [2, 21], [38, 0], [1, -24]], "is_closed": true}, "area": 889.5, "original_pixel_count": 851, "contour_points": 4, "order": 114}, {"id": 115, "hex": "#6b8283", "contour": {"type": "relative", "data": [[699, 347], [17, 25], [17, 4], [7, -7], [-13, 2], [-8, -7], [6, -4], [-7, -16], [8, -7], [41, 0], [-65, -1], [-3, 11]], "is_closed": true}, "area": 679.0, "original_pixel_count": 894, "contour_points": 11, "order": 115}, {"id": 116, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[319, 596], [26, 34], [101, -3], [-59, 1], [1, -17], [-33, 17], [0, -36], [-30, -9], [17, 33], [-23, -20]], "is_closed": true}, "area": 1198.5, "original_pixel_count": 1090, "contour_points": 9, "order": 116}, {"id": 117, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[8, 651], [66, 3], [22, -3], [3, -7], [-6, 6], [2, -7], [-79, -1], [-8, 9]], "is_closed": true}, "area": 844.0, "original_pixel_count": 882, "contour_points": 7, "order": 117}, {"id": 118, "hex": "#87ada9", "contour": {"type": "relative", "data": [[68, 738], [0, 14], [48, 42], [-13, -46], [-11, 7], [-9, -11], [12, -4], [-27, -2]], "is_closed": true}, "area": 1020.0, "original_pixel_count": 959, "contour_points": 7, "order": 118}, {"id": 119, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[327, 722], [1, 24], [33, -2], [1, 30], [1, -52], [-36, 0]], "is_closed": true}, "area": 834.0, "original_pixel_count": 855, "contour_points": 5, "order": 119}, {"id": 120, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[419, 453], [-2, 30], [23, 2], [1, -30], [-22, -2]], "is_closed": true}, "area": 678.0, "original_pixel_count": 813, "contour_points": 4, "order": 120}, {"id": 121, "hex": "#254c65", "contour": {"type": "relative", "data": [[453, 597], [-8, 12], [3, 4], [13, -13], [16, 21], [10, 0], [-3, 17], [16, -14], [-6, -12], [-10, -1], [-10, -11], [-1, -7], [-20, 4]], "is_closed": true}, "area": 704.5, "original_pixel_count": 835, "contour_points": 12, "order": 121}, {"id": 122, "hex": "#6b8283", "contour": {"type": "relative", "data": [[52, 735], [61, -20], [18, 73], [-14, -74], [18, 0], [1, 61], [8, -63], [-80, 18], [63, -26], [63, 12], [-77, -13], [-61, 32]], "is_closed": true}, "area": 513.0, "original_pixel_count": 1181, "contour_points": 11, "order": 122}, {"id": 123, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[751, 802], [-20, 21], [11, 24], [10, -2], [8, -24], [-9, -19]], "is_closed": true}, "area": 755.5, "original_pixel_count": 813, "contour_points": 5, "order": 123}, {"id": 124, "hex": "#ab7056", "contour": {"type": "relative", "data": [[369, 333], [-17, 12], [7, 22], [9, 0], [5, 9], [9, -38], [-13, -5]], "is_closed": true}, "area": 741.0, "original_pixel_count": 803, "contour_points": 6, "order": 124}, {"id": 125, "hex": "#4098a0", "contour": {"type": "relative", "data": [[673, 850], [-20, 43], [23, 0], [-1, -7], [8, 7], [4, -23], [-14, -20]], "is_closed": true}, "area": 821.5, "original_pixel_count": 827, "contour_points": 6, "order": 125}, {"id": 126, "hex": "#87ada9", "contour": {"type": "relative", "data": [[635, 589], [-1, 45], [20, 0], [1, -43], [-20, -2]], "is_closed": true}, "area": 881.0, "original_pixel_count": 832, "contour_points": 4, "order": 126}, {"id": 127, "hex": "#eae8df", "contour": {"type": "relative", "data": [[514, 280], [18, -3], [11, 21], [39, -25], [14, 34], [19, -23], [46, 10], [-84, -26], [-31, 17], [-1, -63], [0, 62], [-31, -4]], "is_closed": true}, "area": 1107.5, "original_pixel_count": 1004, "contour_points": 11, "order": 127}, {"id": 128, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[263, 681], [22, 33], [11, 4], [6, -5], [-1, -13], [2, 10], [-6, 3], [-9, -19], [4, -9], [4, 4], [-6, -12], [-27, 4]], "is_closed": true}, "area": 692.0, "original_pixel_count": 805, "contour_points": 11, "order": 128}, {"id": 129, "hex": "#eae8df", "contour": {"type": "relative", "data": [[196, 703], [-56, -14], [-56, 16], [-40, 28], [68, -32], [37, -1], [40, 13], [7, -10]], "is_closed": true}, "area": 1122.5, "original_pixel_count": 888, "contour_points": 7, "order": 129}, {"id": 130, "hex": "#87ada9", "contour": {"type": "relative", "data": [[636, 521], [0, 46], [0, -8], [10, -2], [12, 8], [0, -38], [-22, -6]], "is_closed": true}, "area": 784.0, "original_pixel_count": 811, "contour_points": 6, "order": 130}, {"id": 131, "hex": "#4098a0", "contour": {"type": "relative", "data": [[152, 572], [0, 39], [3, 3], [13, -1], [0, -40], [-16, -1]], "is_closed": true}, "area": 653.0, "original_pixel_count": 760, "contour_points": 5, "order": 131}, {"id": 132, "hex": "#6b8283", "contour": {"type": "relative", "data": [[86, 287], [17, 24], [41, 2], [-39, -27], [-19, 1]], "is_closed": true}, "area": 751.0, "original_pixel_count": 776, "contour_points": 4, "order": 132}, {"id": 133, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[275, 441], [2, 11], [-6, -4], [-13, 15], [8, 17], [13, 3], [10, -14], [-8, -11], [7, -3], [-13, -14]], "is_closed": true}, "area": 726.5, "original_pixel_count": 766, "contour_points": 9, "order": 133}, {"id": 134, "hex": "#ab7056", "contour": {"type": "relative", "data": [[399, 332], [-6, 5], [6, 22], [20, -1], [-2, 18], [12, 37], [-8, -70], [-8, -9], [-14, -2]], "is_closed": true}, "area": 857.0, "original_pixel_count": 780, "contour_points": 8, "order": 134}, {"id": 135, "hex": "#ab7056", "contour": {"type": "relative", "data": [[660, 104], [-17, 34], [-1, 48], [24, -4], [-22, -3], [5, -26], [11, 0], [1, -10], [16, 3], [-14, -5], [-3, -37]], "is_closed": true}, "area": 822.5, "original_pixel_count": 831, "contour_points": 10, "order": 135}, {"id": 136, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[251, 366], [-32, -1], [5, 35], [10, 0], [17, -34]], "is_closed": true}, "area": 727.5, "original_pixel_count": 733, "contour_points": 4, "order": 136}, {"id": 137, "hex": "#6b8283", "contour": {"type": "relative", "data": [[543, 847], [-6, -17], [-18, 1], [-2, 27], [-9, -1], [0, -22], [-2, 21], [-40, 2], [-6, -25], [-17, 27], [-17, -8], [-1, -14], [3, 19], [101, 5], [14, -15]], "is_closed": true}, "area": 1097.0, "original_pixel_count": 948, "contour_points": 14, "order": 137}, {"id": 138, "hex": "#87ada9", "contour": {"type": "relative", "data": [[548, 375], [6, 17], [4, -4], [14, 2], [-11, 20], [22, 4], [-2, -24], [5, -2], [-4, -5], [6, -6], [-36, 2], [-4, -4]], "is_closed": true}, "area": 734.0, "original_pixel_count": 759, "contour_points": 11, "order": 138}, {"id": 139, "hex": "#eae8df", "contour": {"type": "relative", "data": [[767, 32], [-45, -20], [-48, 18], [-34, -17], [-22, 7], [-6, 19], [19, -22], [44, 16], [52, -18], [32, 25], [8, -8]], "is_closed": true}, "area": 779.0, "original_pixel_count": 841, "contour_points": 10, "order": 139}, {"id": 140, "hex": "#ab7056", "contour": {"type": "relative", "data": [[581, 230], [-32, 1], [-1, 29], [14, 1], [19, -31]], "is_closed": true}, "area": 690.0, "original_pixel_count": 715, "contour_points": 4, "order": 140}, {"id": 141, "hex": "#87ada9", "contour": {"type": "relative", "data": [[682, 215], [2, 8], [11, 0], [9, 4], [9, -1], [6, -10], [-20, -17], [-9, 4], [-8, 12]], "is_closed": true}, "area": 649.5, "original_pixel_count": 705, "contour_points": 8, "order": 141}, {"id": 142, "hex": "#eae8df", "contour": {"type": "relative", "data": [[271, 333], [13, 32], [33, 1], [0, 47], [-17, -20], [18, 49], [28, -37], [7, -52], [-12, -8], [6, 49], [-26, 33], [6, -64], [-42, -3], [8, -18], [-22, -9]], "is_closed": true}, "area": 1699.5, "original_pixel_count": 943, "contour_points": 14, "order": 142}, {"id": 143, "hex": "#87ada9", "contour": {"type": "relative", "data": [[660, 704], [-42, 13], [12, 15], [-6, -14], [22, -6], [1, 36], [-14, -9], [21, 26], [6, -61]], "is_closed": true}, "area": 753.0, "original_pixel_count": 814, "contour_points": 8, "order": 143}, {"id": 144, "hex": "#dd835f", "contour": {"type": "relative", "data": [[63, 383], [-21, -31], [-25, 31], [15, -1], [4, -10], [7, 10], [20, 1]], "is_closed": true}, "area": 629.5, "original_pixel_count": 714, "contour_points": 6, "order": 144}, {"id": 145, "hex": "#87ada9", "contour": {"type": "relative", "data": [[710, 589], [-1, 40], [17, 3], [-6, -9], [8, -3], [1, -28], [-6, 8], [3, -11], [-16, 0]], "is_closed": true}, "area": 706.0, "original_pixel_count": 731, "contour_points": 8, "order": 145}, {"id": 146, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[0, 599], [0, 12], [4, -14], [22, -1], [1, 29], [10, 1], [0, -28], [26, -1], [-1, 29], [11, 0], [1, -27], [23, -3], [8, 31], [-4, -32], [-101, 4]], "is_closed": true}, "area": 720.0, "original_pixel_count": 902, "contour_points": 14, "order": 146}, {"id": 147, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[0, 522], [27, 8], [-3, 49], [11, 0], [3, -58], [68, -2], [-106, 3]], "is_closed": true}, "area": 744.5, "original_pixel_count": 813, "contour_points": 6, "order": 147}, {"id": 148, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[561, 266], [-12, 18], [7, 0], [-17, 14], [22, -12], [-4, 11], [12, 5], [8, -19], [19, 12], [-4, 18], [8, -7], [-4, -17], [-11, -14], [-25, 0], [1, -9]], "is_closed": true}, "area": 787.5, "original_pixel_count": 793, "contour_points": 14, "order": 148}, {"id": 149, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[102, 645], [-14, 10], [-88, 0], [3, 8], [22, -5], [6, 14], [9, -14], [23, 2], [2, 12], [-10, 3], [16, 0], [6, -16], [19, -1], [2, 17], [4, -30]], "is_closed": true}, "area": 783.5, "original_pixel_count": 819, "contour_points": 14, "order": 149}, {"id": 150, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[384, 569], [0, 22], [-24, 0], [5, 29], [22, -3], [-3, -48]], "is_closed": true}, "area": 671.5, "original_pixel_count": 736, "contour_points": 5, "order": 150}, {"id": 151, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[323, 279], [-6, -6], [-12, 4], [-8, -2], [-7, 15], [-16, 2], [-3, 11], [9, 1], [8, -7], [15, -1], [5, -9], [15, -8]], "is_closed": true}, "area": 595.5, "original_pixel_count": 689, "contour_points": 11, "order": 151}, {"id": 152, "hex": "#87ada9", "contour": {"type": "relative", "data": [[150, 645], [-6, 32], [37, 0], [2, -21], [-1, 17], [-11, 1], [5, -18], [-26, -11]], "is_closed": true}, "area": 755.0, "original_pixel_count": 716, "contour_points": 7, "order": 152}, {"id": 153, "hex": "#efd8c2", "contour": {"type": "relative", "data": [[0, 583], [0, 7], [105, -3], [-105, -4]], "is_closed": true}, "area": 367.5, "original_pixel_count": 727, "contour_points": 3, "order": 153}, {"id": 154, "hex": "#87ada9", "contour": {"type": "relative", "data": [[561, 830], [10, 40], [17, 19], [18, -14], [-9, -7], [20, -15], [-27, 2], [-11, 22], [-9, -14], [12, -7], [-17, -5], [-3, -12], [6, -1], [-7, -8]], "is_closed": true}, "area": 742.5, "original_pixel_count": 766, "contour_points": 13, "order": 154}, {"id": 155, "hex": "#eae8df", "contour": {"type": "relative", "data": [[384, 640], [0, 7], [10, 1], [71, -1], [-14, -7], [-67, 0]], "is_closed": true}, "area": 558.5, "original_pixel_count": 697, "contour_points": 5, "order": 155}, {"id": 156, "hex": "#87ada9", "contour": {"type": "relative", "data": [[82, 246], [-11, 3], [-13, 11], [0, 5], [13, 12], [6, -3], [3, -9], [7, -1], [8, -6], [-13, -12]], "is_closed": true}, "area": 617.0, "original_pixel_count": 658, "contour_points": 9, "order": 156}, {"id": 157, "hex": "#4098a0", "contour": {"type": "relative", "data": [[160, 499], [-5, 2], [5, 5], [-7, 5], [1, 32], [10, 1], [4, -1], [1, -33], [-1, -10], [-8, -1]], "is_closed": true}, "area": 619.5, "original_pixel_count": 668, "contour_points": 9, "order": 157}, {"id": 158, "hex": "#dd835f", "contour": {"type": "relative", "data": [[704, 324], [2, 8], [13, 0], [11, -6], [6, 0], [5, 6], [26, 0], [0, -11], [-61, 0], [-2, 3]], "is_closed": true}, "area": 598.0, "original_pixel_count": 680, "contour_points": 9, "order": 158}, {"id": 159, "hex": "#87ada9", "contour": {"type": "relative", "data": [[580, 199], [-28, -3], [-3, 15], [30, 9], [-30, 7], [-2, -13], [1, 13], [31, 0], [1, -28]], "is_closed": true}, "area": 649.0, "original_pixel_count": 718, "contour_points": 8, "order": 159}, {"id": 160, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[393, 651], [6, 20], [32, -4], [-4, -15], [15, 2], [-2, 14], [11, -19], [-58, 2]], "is_closed": true}, "area": 752.5, "original_pixel_count": 705, "contour_points": 7, "order": 160}, {"id": 161, "hex": "#87ada9", "contour": {"type": "relative", "data": [[671, 523], [2, 43], [22, 0], [-2, -40], [-20, 1], [-2, -4]], "is_closed": true}, "area": 869.0, "original_pixel_count": 730, "contour_points": 5, "order": 161}, {"id": 162, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[283, 400], [-24, 2], [4, 31], [21, -7], [-1, -26]], "is_closed": true}, "area": 652.5, "original_pixel_count": 678, "contour_points": 4, "order": 162}, {"id": 163, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[587, 655], [-9, 0], [1, 10], [-28, -6], [0, 14], [16, 12], [20, -6], [0, -24]], "is_closed": true}, "area": 733.0, "original_pixel_count": 701, "contour_points": 7, "order": 163}, {"id": 164, "hex": "#dd835f", "contour": {"type": "relative", "data": [[237, 713], [-1, 23], [8, 12], [7, 2], [17, -4], [0, -4], [-31, -29]], "is_closed": true}, "area": 618.0, "original_pixel_count": 640, "contour_points": 6, "order": 164}, {"id": 165, "hex": "#bd3421", "contour": {"type": "relative", "data": [[122, 345], [12, 21], [17, -12], [11, 1], [8, 6], [2, -3], [-3, -9], [-6, -3], [-41, -1]], "is_closed": true}, "area": 557.5, "original_pixel_count": 649, "contour_points": 8, "order": 165}, {"id": 166, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[432, 397], [10, 19], [3, -12], [11, 4], [7, 21], [18, 0], [9, -24], [-11, -9], [9, -10], [-11, 8], [4, 20], [-8, 7], [-15, -5], [12, -34], [-11, 1], [3, 19], [-30, -5]], "is_closed": true}, "area": 760.5, "original_pixel_count": 757, "contour_points": 16, "order": 166}, {"id": 167, "hex": "#6b8283", "contour": {"type": "relative", "data": [[422, 838], [-5, 23], [-24, -1], [0, -23], [-23, 22], [2, -23], [-24, 19], [19, 8], [-33, 5], [77, -3], [11, -27]], "is_closed": true}, "area": 846.0, "original_pixel_count": 824, "contour_points": 10, "order": 167}, {"id": 168, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[605, 425], [1, 19], [33, 0], [-2, -10], [-8, -10], [-15, -2], [-9, 3]], "is_closed": true}, "area": 620.0, "original_pixel_count": 640, "contour_points": 6, "order": 168}, {"id": 169, "hex": "#254c65", "contour": {"type": "relative", "data": [[461, 488], [-1, 54], [12, 2], [1, -53], [-12, -3]], "is_closed": true}, "area": 644.5, "original_pixel_count": 648, "contour_points": 4, "order": 169}, {"id": 170, "hex": "#b5c9cd", "contour": {"type": "relative", "data": [[636, 154], [-8, 1], [-4, 23], [-5, -1], [-4, -14], [-7, 2], [2, 29], [10, -2], [3, -11], [13, -7], [0, -20]], "is_closed": true}, "area": 538.5, "original_pixel_count": 651, "contour_points": 10, "order": 170}, {"id": 171, "hex": "#eae8df", "contour": {"type": "relative", "data": [[583, 391], [159, 9], [-4, -9], [-155, 0]], "is_closed": true}, "area": 697.5, "original_pixel_count": 742, "contour_points": 3, "order": 171}, {"id": 172, "hex": "#254c65", "contour": {"type": "relative", "data": [[667, 654], [2, 24], [22, 2], [5, -26], [-29, 0]], "is_closed": true}, "area": 639.0, "original_pixel_count": 627, "contour_points": 4, "order": 172}, {"id": 173, "hex": "#87ada9", "contour": {"type": "relative", "data": [[244, 212], [-36, -1], [-1, 13], [8, -2], [2, 11], [5, 0], [4, -10], [9, 5], [6, -2], [3, -14]], "is_closed": true}, "area": 533.0, "original_pixel_count": 624, "contour_points": 9, "order": 173}, {"id": 174, "hex": "#254c65", "contour": {"type": "relative", "data": [[385, 719], [3, 6], [10, 1], [11, -13], [-2, -7], [39, 6], [-41, -15], [-17, 12], [-3, 10]], "is_closed": true}, "area": 590.0, "original_pixel_count": 637, "contour_points": 8, "order": 174}, {"id": 175, "hex": "#87ada9", "contour": {"type": "relative", "data": [[201, 496], [-11, 6], [2, 9], [4, -10], [5, 3], [0, 11], [-16, -1], [6, 3], [-1, 22], [-8, -2], [-1, -30], [2, 46], [18, -2], [0, -55]], "is_closed": true}, "area": 563.5, "original_pixel_count": 686, "contour_points": 13, "order": 175}, {"id": 176, "hex": "#4098a0", "contour": {"type": "relative", "data": [[767, 785], [-37, 5], [-5, 10], [12, 7], [20, -8], [10, 10], [0, -24]], "is_closed": true}, "area": 629.5, "original_pixel_count": 615, "contour_points": 6, "order": 176}, {"id": 177, "hex": "#dd835f", "contour": {"type": "relative", "data": [[91, 806], [-18, -2], [-4, 6], [-10, -1], [-1, 5], [15, 14], [9, 0], [5, -4], [-5, -2], [-1, -4], [12, -1], [-2, -11]], "is_closed": true}, "area": 524.5, "original_pixel_count": 602, "contour_points": 11, "order": 177}, {"id": 178, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[378, 878], [0, 9], [7, 6], [10, 0], [2, -7], [3, 7], [9, -16], [4, 8], [9, -6], [8, 14], [-1, -10], [-9, -9], [-8, 9], [2, -13], [-13, -1], [-23, 9]], "is_closed": true}, "area": 659.0, "original_pixel_count": 655, "contour_points": 15, "order": 178}, {"id": 179, "hex": "#4098a0", "contour": {"type": "relative", "data": [[288, 315], [-29, -2], [-18, 19], [10, 15], [6, -20], [29, -2], [2, -10]], "is_closed": true}, "area": 600.0, "original_pixel_count": 608, "contour_points": 6, "order": 179}, {"id": 180, "hex": "#eae8df", "contour": {"type": "relative", "data": [[202, 679], [-202, 4]], "is_closed": true}, "area": 0.0, "original_pixel_count": 745, "contour_points": 2, "order": 180}, {"id": 181, "hex": "#87ada9", "contour": {"type": "relative", "data": [[534, 488], [0, 27], [18, 0], [2, -25], [-2, -2], [-18, 0]], "is_closed": true}, "area": 513.0, "original_pixel_count": 584, "contour_points": 5, "order": 181}, {"id": 182, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[527, 176], [-5, 32], [25, 2], [1, -4], [-17, -31], [-4, 1]], "is_closed": true}, "area": 532.5, "original_pixel_count": 588, "contour_points": 5, "order": 182}, {"id": 183, "hex": "#eae8df", "contour": {"type": "relative", "data": [[688, 310], [13, 10], [-12, 7], [11, 37], [43, 24], [-29, -14], [-21, -34], [11, -22], [34, 0], [-12, -2], [2, -14], [-11, 15], [-29, -7]], "is_closed": true}, "area": 384.5, "original_pixel_count": 709, "contour_points": 12, "order": 183}, {"id": 184, "hex": "#87ada9", "contour": {"type": "relative", "data": [[672, 590], [19, 0], [1, 10], [-20, 1], [-1, 31], [9, 2], [8, -16], [4, 16], [1, -43], [-21, -1]], "is_closed": true}, "area": 599.5, "original_pixel_count": 625, "contour_points": 9, "order": 184}, {"id": 185, "hex": "#d0b7a0", "contour": {"type": "relative", "data": [[210, 843], [3, 13], [20, 11], [-1, 6], [13, -28], [-35, -2]], "is_closed": true}, "area": 535.5, "original_pixel_count": 592, "contour_points": 5, "order": 185}, {"id": 186, "hex": "#eae8df", "contour": {"type": "relative", "data": [[352, 339], [28, -8], [15, 16], [-15, 13], [16, 0], [3, -32], [28, 17], [-40, -41], [-15, 15], [39, 7], [-29, 7], [11, -10], [-9, -3], [-32, 19]], "is_closed": true}, "area": 916.5, "original_pixel_count": 735, "contour_points": 13, "order": 186}, {"id": 187, "hex": "#eae8df", "contour": {"type": "relative", "data": [[591, 89], [-12, 18], [-3, 56], [-32, -6], [-17, -32], [12, 46], [15, -12], [9, 20], [16, -8], [0, -64], [12, -18]], "is_closed": true}, "area": 650.0, "original_pixel_count": 694, "contour_points": 10, "order": 187}, {"id": 188, "hex": "#254c65", "contour": {"type": "relative", "data": [[15, 841], [-8, 13], [27, 28], [5, -5], [-9, -14], [6, -11], [-21, -11]], "is_closed": true}, "area": 592.0, "original_pixel_count": 578, "contour_points": 6, "order": 188}, {"id": 189, "hex": "#87ada9", "contour": {"type": "relative", "data": [[600, 589], [1, 37], [10, -3], [-1, 6], [7, 2], [1, -21], [-6, -7], [6, -2], [1, -10], [-19, -2]], "is_closed": true}, "area": 616.0, "original_pixel_count": 600, "contour_points": 9, "order": 189}, {"id": 190, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[691, 420], [-1, 23], [24, 1], [-1, -21], [-22, -3]], "is_closed": true}, "area": 506.0, "original_pixel_count": 558, "contour_points": 4, "order": 190}, {"id": 191, "hex": "#87ada9", "contour": {"type": "relative", "data": [[608, 723], [0, 11], [53, 54], [-31, -45], [-6, 4], [-17, -18], [7, -5], [-6, -1]], "is_closed": true}, "area": 331.0, "original_pixel_count": 597, "contour_points": 7, "order": 191}, {"id": 192, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[262, 249], [-32, 0], [-18, 14], [-9, -6], [3, 8], [-19, 0], [3, -7], [-5, 6], [52, 2], [25, -17]], "is_closed": true}, "area": 529.0, "original_pixel_count": 603, "contour_points": 9, "order": 192}, {"id": 193, "hex": "#6b8283", "contour": {"type": "relative", "data": [[597, 717], [46, -19], [45, -2], [40, 11], [39, 34], [-31, -31], [-48, -16], [-49, 3], [-42, 20]], "is_closed": true}, "area": 246.0, "original_pixel_count": 699, "contour_points": 8, "order": 193}, {"id": 194, "hex": "#b99c7f", "contour": {"type": "relative", "data": [[664, 350], [-8, 4], [-1, 29], [2, 2], [14, 0], [0, -31], [-7, -4]], "is_closed": true}, "area": 509.5, "original_pixel_count": 548, "contour_points": 6, "order": 194}, {"id": 195, "hex": "#eae8df", "contour": {"type": "relative", "data": [[512, 790], [-11, 8], [-35, -7], [-1, 25], [6, -6], [-1, -13], [24, 2], [3, 9], [-11, 6], [22, 2], [4, -26]], "is_closed": true}, "area": 513.0, "original_pixel_count": 595, "contour_points": 10, "order": 195}], "palette": [{"id": 1, "color_hex": "#efd8c2", "color_rgb": [239, 216, 194], "name": "颜色1", "usage_count": 80718}, {"id": 2, "color_hex": "#b99c7f", "color_rgb": [185, 156, 127], "name": "颜色2", "usage_count": 33860}, {"id": 3, "color_hex": "#ab7056", "color_rgb": [171, 112, 86], "name": "颜色3", "usage_count": 31822}, {"id": 4, "color_hex": "#b5c9cd", "color_rgb": [181, 201, 205], "name": "颜色4", "usage_count": 50624}, {"id": 5, "color_hex": "#eae8df", "color_rgb": [234, 232, 223], "name": "颜色5", "usage_count": 41916}, {"id": 6, "color_hex": "#bd3421", "color_rgb": [189, 52, 33], "name": "颜色6", "usage_count": 12682}, {"id": 7, "color_hex": "#dd835f", "color_rgb": [221, 131, 95], "name": "颜色7", "usage_count": 39278}, {"id": 8, "color_hex": "#254c65", "color_rgb": [37, 76, 101], "name": "颜色8", "usage_count": 15495}, {"id": 9, "color_hex": "#87ada9", "color_rgb": [135, 173, 169], "name": "颜色9", "usage_count": 37663}, {"id": 10, "color_hex": "#4098a0", "color_rgb": [64, 152, 160], "name": "颜色10", "usage_count": 21070}, {"id": 11, "color_hex": "#d0b7a0", "color_rgb": [208, 183, 160], "name": "颜色11", "usage_count": 15130}, {"id": 12, "color_hex": "#6b8283", "color_rgb": [107, 130, 131], "name": "颜色12", "usage_count": 8751}]}