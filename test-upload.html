<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量上传测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-upload"></i>
                            批量上传测试
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="http://localhost:8083/admin/resources/batch-upload" method="post" enctype="multipart/form-data">
                            
                            <!-- 文件选择 -->
                            <div class="mb-3">
                                <label class="form-label">选择文件</label>
                                <input type="file" name="files" class="form-control" accept="image/*,.json" multiple required>
                                <div class="form-text">支持图片文件和JSON文件，文件名需要配对（如：house1.png 和 house1.json）</div>
                            </div>

                            <!-- 分类选择 -->
                            <div class="mb-3">
                                <label class="form-label">选择分类</label>
                                <select name="categoryId" class="form-select" required>
                                    <option value="">请选择分类</option>
                                    <option value="1">房屋建筑 (Houses)</option>
                                    <option value="2">树屋小屋 (Treehouses)</option>
                                    <option value="3">农场庄园 (Farmhouses)</option>
                                    <option value="4">城堡宫殿 (Castles)</option>
                                    <option value="5">动物世界 (Animals)</option>
                                    <option value="6">植物花卉 (Plants)</option>
                                    <option value="7">卡通人物 (Cartoon)</option>
                                    <option value="8">交通工具 (Vehicles)</option>
                                    <option value="9">食物美食 (Food)</option>
                                    <option value="10">节日庆典 (Holidays)</option>
                                </select>
                            </div>

                            <!-- 难度等级 -->
                            <div class="mb-3">
                                <label class="form-label">难度等级</label>
                                <select name="difficulty" class="form-select" required>
                                    <option value="1">⭐ 简单</option>
                                    <option value="2">⭐⭐ 容易</option>
                                    <option value="3" selected>⭐⭐⭐ 中等</option>
                                    <option value="4">⭐⭐⭐⭐ 困难</option>
                                    <option value="5">⭐⭐⭐⭐⭐ 专家</option>
                                </select>
                            </div>

                            <!-- 版本号 -->
                            <div class="mb-3">
                                <label class="form-label">版本号</label>
                                <input type="text" name="version" class="form-control" value="1.0.0" required>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload"></i>
                                    开始上传
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 使用说明 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">使用说明</h6>
                    </div>
                    <div class="card-body">
                        <h6>文件配对规则：</h6>
                        <ul>
                            <li>图片文件：house1.png, house2.jpg 等</li>
                            <li>JSON文件：house1.json, house2.json 等</li>
                            <li>系统会自动检测文件配对关系</li>
                            <li>缺少配对的文件会显示警告</li>
                        </ul>
                        
                        <h6>分类存储：</h6>
                        <ul>
                            <li>文件会按照英文分类名存储到对应目录</li>
                            <li>例如：选择"房屋建筑"，文件存储到 uploads/houses/ 目录</li>
                            <li>选择"动物世界"，文件存储到 uploads/animals/ 目录</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
