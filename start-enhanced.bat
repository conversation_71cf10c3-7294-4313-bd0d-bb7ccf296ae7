@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ================================
echo Dreaming涂色App后端服务启动脚本
echo ================================
echo.

REM 设置应用端口
set APP_PORT=8083

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorlevel% equ 0 (
    echo 🔑 检测到管理员权限
) else (
    echo ⚠️  未检测到管理员权限，某些操作可能失败
)

echo.
echo [1/6] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java环境
    echo    请先安装Java 11或更高版本
    echo    下载地址: https://adoptium.net/
    pause
    exit /b 1
) else (
    echo ✅ Java环境检查通过
    for /f "tokens=3" %%a in ('java -version 2^>^&1 ^| findstr "version"') do (
        set JAVA_VERSION=%%a
        set JAVA_VERSION=!JAVA_VERSION:"=!
        echo    Java版本: !JAVA_VERSION!
    )
)

echo.
echo [2/6] 检查项目文件...
if not exist "pom.xml" (
    echo ❌ 错误: 未找到pom.xml文件
    echo    请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

if not exist "src\main\java\com\dreaming\DreamingColoringApplication.java" (
    echo ❌ 错误: 项目文件不完整
    echo    请检查项目文件是否完整
    pause
    exit /b 1
)
echo ✅ 项目文件检查通过

echo.
echo [3/6] 创建必要目录...
if not exist "uploads" (
    mkdir uploads
    echo ✅ 创建uploads目录
) else (
    echo ✅ uploads目录已存在
)

if not exist "logs" (
    mkdir logs
    echo ✅ 创建logs目录
) else (
    echo ✅ logs目录已存在
)

echo.
echo [4/6] 检查端口占用...
echo 检查端口 %APP_PORT% 是否被占用...

REM 查找占用端口的进程
set OCCUPIED_PID=
set PROCESS_NAME=
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%APP_PORT% " 2^>nul') do (
    set OCCUPIED_PID=%%a
    goto :getProcessName
)

echo ✅ 端口 %APP_PORT% 未被占用
goto :skipPortKill

:getProcessName
REM 获取进程名称
for /f "skip=1 tokens=1" %%b in ('tasklist /fi "PID eq %OCCUPIED_PID%" /fo table /nh 2^>nul') do (
    set PROCESS_NAME=%%b
    goto :portOccupied
)

:portOccupied
if "%OCCUPIED_PID%"=="" (
    echo ✅ 端口 %APP_PORT% 未被占用
    goto :skipPortKill
)

echo ⚠️  端口 %APP_PORT% 被占用
echo    进程ID: %OCCUPIED_PID%
echo    进程名: %PROCESS_NAME%

REM 检查是否是Java进程
echo %PROCESS_NAME% | findstr /i "java" >nul
if %errorlevel% equ 0 (
    echo    这可能是另一个Java应用，将自动停止
    set AUTO_KILL=Y
) else (
    echo    这是非Java进程，请确认是否停止
    set /p AUTO_KILL=是否停止该进程？(Y/N，默认N): 
    if "!AUTO_KILL!"=="" set AUTO_KILL=N
)

if /i "!AUTO_KILL!"=="Y" (
    echo 正在停止进程 %OCCUPIED_PID%...
    taskkill /PID %OCCUPIED_PID% /F >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ 成功停止进程 %OCCUPIED_PID%
        timeout /t 3 /nobreak >nul
        echo 等待端口释放...
    ) else (
        echo ❌ 停止进程失败
        echo    请尝试以管理员身份运行此脚本
        echo    或手动停止占用端口的进程
        pause
        exit /b 1
    )
) else (
    echo ❌ 端口 %APP_PORT% 仍被占用，无法启动应用
    echo    请手动停止占用端口的进程或修改配置文件中的端口
    pause
    exit /b 1
)

:skipPortKill

echo.
echo [5/6] 清理旧进程...
echo 检查并清理可能的旧应用进程...

set OLD_PROCESS_COUNT=0
for /f "skip=1 tokens=2" %%a in ('tasklist /fi "imagename eq java.exe" /fo table 2^>nul') do (
    set JAVA_PID=%%a
    
    REM 检查是否是Spring Boot应用
    wmic process where "ProcessId=!JAVA_PID!" get CommandLine 2>nul | findstr /i "spring-boot:run\|DreamingColoringApplication" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ⚠️  发现旧的应用进程: !JAVA_PID!
        taskkill /PID !JAVA_PID! /F >nul 2>&1
        if !errorlevel! equ 0 (
            echo ✅ 成功停止旧进程 !JAVA_PID!
            set /a OLD_PROCESS_COUNT+=1
        )
    )
)

if %OLD_PROCESS_COUNT% gtr 0 (
    echo ✅ 清理了 %OLD_PROCESS_COUNT% 个旧进程
    timeout /t 2 /nobreak >nul
) else (
    echo ✅ 未发现需要清理的旧进程
)

echo.
echo [6/6] 启动应用...
echo 正在启动Spring Boot应用，请稍候...
echo 应用将在端口 %APP_PORT% 启动
echo 启动完成后可访问: http://localhost:%APP_PORT%/admin
echo.

if exist "mvnw.cmd" (
    echo 使用Maven Wrapper启动...
    call mvnw.cmd spring-boot:run -Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS
) else (
    echo 使用系统Maven启动...
    mvn spring-boot:run -Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS
)

if %errorlevel% neq 0 (
    echo.
    echo ❌ 应用启动失败！
    echo.
    echo 常见问题及解决方案：
    echo 1. 端口占用：脚本已自动处理，如仍有问题请手动检查
    echo 2. Java版本：确保使用Java 11或更高版本
    echo 3. 网络问题：检查Maven依赖下载是否正常
    echo 4. 权限问题：尝试以管理员身份运行此脚本
    echo 5. 内存不足：关闭其他应用释放内存
    echo.
    echo 详细错误信息请查看上方输出
    pause
    exit /b 1
) else (
    echo.
    echo ✅ 应用启动成功！
    echo 🌐 访问地址: http://localhost:%APP_PORT%/admin
    echo 📚 API文档: http://localhost:%APP_PORT%/swagger-ui.html
    echo.
)

pause
