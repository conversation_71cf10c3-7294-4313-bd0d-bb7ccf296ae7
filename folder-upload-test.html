<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件夹上传功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .test-area:hover {
            border-color: #0d6efd;
            background-color: #f8f9ff;
        }
        .log-area {
            height: 200px;
            overflow-y: auto;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-folder-open"></i>
                            文件夹上传功能测试
                        </h4>
                        <small>独立测试页面 - 无需后端服务</small>
                    </div>
                    <div class="card-body">
                        
                        <!-- 浏览器兼容性检查 -->
                        <div class="alert alert-info mb-4">
                            <h6><i class="fas fa-info-circle"></i> 浏览器兼容性检查</h6>
                            <div id="browserCompatibility"></div>
                        </div>

                        <!-- 测试区域 -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6>测试1: 文件夹选择</h6>
                                <div class="test-area" onclick="selectFolder()">
                                    <i class="fas fa-folder fa-3x text-primary mb-2"></i>
                                    <p class="mb-0">点击选择文件夹</p>
                                </div>
                                <input type="file" id="folderInput" class="d-none" webkitdirectory directory multiple>
                                <div id="folderResult" class="mt-2 small text-muted">未选择</div>
                            </div>
                            <div class="col-md-6">
                                <h6>测试2: 文件选择（对比）</h6>
                                <div class="test-area" onclick="selectFiles()">
                                    <i class="fas fa-files fa-3x text-success mb-2"></i>
                                    <p class="mb-0">点击选择文件</p>
                                </div>
                                <input type="file" id="filesInput" class="d-none" multiple accept="image/*,.json">
                                <div id="filesResult" class="mt-2 small text-muted">未选择</div>
                            </div>
                        </div>

                        <!-- 拖拽测试区域 -->
                        <div class="mb-4">
                            <h6>测试3: 拖拽上传</h6>
                            <div class="test-area" id="dropZone">
                                <i class="fas fa-cloud-upload-alt fa-3x text-info mb-2"></i>
                                <p class="mb-0">拖拽文件夹或文件到此处</p>
                            </div>
                            <div id="dropResult" class="mt-2 small text-muted">未拖拽</div>
                        </div>

                        <!-- 文件分析结果 -->
                        <div class="mb-4">
                            <h6>文件分析结果</h6>
                            <div id="analysisResult" class="border rounded p-3" style="background-color: #f8f9fa; min-height: 100px;">
                                <div class="text-muted text-center">请选择文件夹或文件进行分析</div>
                            </div>
                        </div>

                        <!-- 实际上传表单 -->
                        <div class="mb-4">
                            <h6>实际上传测试</h6>
                            <form action="http://localhost:8083/admin/resources/batch-upload" method="post" enctype="multipart/form-data">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">选择文件夹</label>
                                        <input type="file" name="files" class="form-control" webkitdirectory directory multiple>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">选择分类</label>
                                        <select class="form-select" name="categoryId" required>
                                            <option value="">请选择分类</option>
                                            <option value="1">房屋建筑 (Houses)</option>
                                            <option value="2">树屋小屋 (Treehouses)</option>
                                            <option value="3">农场庄园 (Farmhouses)</option>
                                            <option value="4">城堡宫殿 (Castles)</option>
                                            <option value="5">动物世界 (Animals)</option>
                                            <option value="6">植物花卉 (Plants)</option>
                                            <option value="7">卡通人物 (Cartoon)</option>
                                            <option value="8">交通工具 (Vehicles)</option>
                                            <option value="9">食物美食 (Food)</option>
                                            <option value="10">节日庆典 (Holidays)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">难度等级</label>
                                        <select class="form-select" name="difficulty" required>
                                            <option value="3" selected>⭐⭐⭐ 中等</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">版本号</label>
                                        <input type="text" class="form-control" name="version" value="1.0.0" required>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-upload"></i>
                                    提交到服务器
                                </button>
                            </form>
                        </div>

                        <!-- 调试日志 -->
                        <div class="mb-4">
                            <h6>调试日志</h6>
                            <div id="debugLog" class="log-area"></div>
                            <button type="button" class="btn btn-sm btn-outline-secondary mt-2" onclick="clearLog()">
                                清空日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 日志功能
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debugLog');
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        // 选择文件夹
        function selectFolder() {
            log('点击选择文件夹');
            try {
                const input = document.getElementById('folderInput');
                if (input) {
                    input.click();
                    log('文件夹选择对话框已触发');
                } else {
                    log('错误：未找到文件夹输入元素');
                }
            } catch (error) {
                log('错误：' + error.message);
            }
        }

        // 选择文件
        function selectFiles() {
            log('点击选择文件');
            try {
                const input = document.getElementById('filesInput');
                if (input) {
                    input.click();
                    log('文件选择对话框已触发');
                } else {
                    log('错误：未找到文件输入元素');
                }
            } catch (error) {
                log('错误：' + error.message);
            }
        }

        // 分析文件
        function analyzeFiles(files, source) {
            log(`${source}: 开始分析 ${files.length} 个文件`);
            
            const imageFiles = [];
            const jsonFiles = [];
            const otherFiles = [];
            const filePairs = new Map();

            Array.from(files).forEach(file => {
                const fileName = file.name;
                const baseName = fileName.replace(/\.[^/.]+$/, "");
                
                if (/\.(png|jpg|jpeg|gif)$/i.test(fileName)) {
                    imageFiles.push(file);
                    if (!filePairs.has(baseName)) filePairs.set(baseName, {});
                    filePairs.get(baseName).image = file;
                } else if (/\.json$/i.test(fileName)) {
                    jsonFiles.push(file);
                    if (!filePairs.has(baseName)) filePairs.set(baseName, {});
                    filePairs.get(baseName).json = file;
                } else {
                    otherFiles.push(file);
                }
            });

            // 统计配对情况
            let pairedCount = 0;
            let unpairedImages = [];
            let unpairedJsons = [];

            filePairs.forEach((pair, baseName) => {
                if (pair.image && pair.json) {
                    pairedCount++;
                } else if (pair.image && !pair.json) {
                    unpairedImages.push(pair.image.name);
                } else if (!pair.image && pair.json) {
                    unpairedJsons.push(pair.json.name);
                }
            });

            // 显示分析结果
            const analysisResult = document.getElementById('analysisResult');
            let html = `
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-primary">文件统计</h6>
                        <ul class="small mb-0">
                            <li>总文件数: <strong>${files.length}</strong></li>
                            <li>图片文件: <strong>${imageFiles.length}</strong></li>
                            <li>JSON文件: <strong>${jsonFiles.length}</strong></li>
                            <li>其他文件: <strong>${otherFiles.length}</strong></li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-success">配对情况</h6>
                        <ul class="small mb-0">
                            <li>成功配对: <strong>${pairedCount}</strong> 对</li>
                            <li>未配对图片: <strong>${unpairedImages.length}</strong> 个</li>
                            <li>未配对JSON: <strong>${unpairedJsons.length}</strong> 个</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-info">文件夹信息</h6>
                        <ul class="small mb-0">
            `;

            if (files.length > 0 && files[0].webkitRelativePath) {
                const rootFolder = files[0].webkitRelativePath.split('/')[0];
                const folders = new Set();
                Array.from(files).forEach(file => {
                    const path = file.webkitRelativePath;
                    const folder = path.substring(0, path.lastIndexOf('/'));
                    if (folder) folders.add(folder);
                });
                
                html += `
                            <li>根文件夹: <strong>${rootFolder}</strong></li>
                            <li>子文件夹: <strong>${folders.size}</strong> 个</li>
                `;
            } else {
                html += `
                            <li>类型: 文件选择</li>
                            <li>来源: ${source}</li>
                `;
            }

            html += `
                        </ul>
                    </div>
                </div>
            `;

            if (unpairedImages.length > 0 || unpairedJsons.length > 0) {
                html += `<hr><h6 class="text-warning">未配对文件</h6>`;
                if (unpairedImages.length > 0) {
                    html += `<div class="small"><strong>未配对图片:</strong> ${unpairedImages.slice(0, 3).join(', ')}`;
                    if (unpairedImages.length > 3) html += ` 等${unpairedImages.length}个`;
                    html += `</div>`;
                }
                if (unpairedJsons.length > 0) {
                    html += `<div class="small"><strong>未配对JSON:</strong> ${unpairedJsons.slice(0, 3).join(', ')}`;
                    if (unpairedJsons.length > 3) html += ` 等${unpairedJsons.length}个`;
                    html += `</div>`;
                }
            }

            analysisResult.innerHTML = html;
            log(`${source}: 分析完成 - ${pairedCount}个配对，${unpairedImages.length + unpairedJsons.length}个未配对`);
        }

        // 页面加载完成后设置
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化');

            // 浏览器兼容性检查
            const supportsDirectory = 'webkitdirectory' in document.createElement('input');
            const browserInfo = document.getElementById('browserCompatibility');
            
            let compatibilityHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>文件夹选择支持:</strong> ${supportsDirectory ? '✅ 支持' : '❌ 不支持'}<br>
                        <strong>浏览器:</strong> ${navigator.userAgent.split(' ').pop()}<br>
                        <strong>平台:</strong> ${navigator.platform}
                    </div>
                    <div class="col-md-6">
                        <strong>推荐浏览器:</strong><br>
                        • Chrome 21+ ✅<br>
                        • Firefox 50+ ✅<br>
                        • Edge 14+ ✅<br>
                        • Safari 11.1+ ⚠️
                    </div>
                </div>
            `;

            browserInfo.innerHTML = compatibilityHtml;

            if (!supportsDirectory) {
                document.querySelector('.alert-info').className = 'alert alert-danger mb-4';
                log('警告：当前浏览器不支持文件夹选择功能，请使用Chrome、Firefox或Edge');
            } else {
                log('浏览器支持文件夹选择功能');
            }

            // 文件夹选择监听
            document.getElementById('folderInput').addEventListener('change', function(e) {
                const files = e.target.files;
                log(`文件夹选择结果: ${files.length} 个文件`);
                document.getElementById('folderResult').innerHTML = 
                    `<span class="text-success">✅ 已选择文件夹，包含 ${files.length} 个文件</span>`;
                analyzeFiles(files, '文件夹选择');
            });

            // 文件选择监听
            document.getElementById('filesInput').addEventListener('change', function(e) {
                const files = e.target.files;
                log(`文件选择结果: ${files.length} 个文件`);
                document.getElementById('filesResult').innerHTML = 
                    `<span class="text-success">✅ 已选择 ${files.length} 个文件</span>`;
                analyzeFiles(files, '文件选择');
            });

            // 拖拽功能
            const dropZone = document.getElementById('dropZone');
            
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#0d6efd';
                dropZone.style.backgroundColor = '#e7f3ff';
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.style.borderColor = '#dee2e6';
                dropZone.style.backgroundColor = 'transparent';
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#dee2e6';
                dropZone.style.backgroundColor = 'transparent';
                
                const files = e.dataTransfer.files;
                log(`拖拽结果: ${files.length} 个文件`);
                document.getElementById('dropResult').innerHTML = 
                    `<span class="text-success">✅ 已拖拽 ${files.length} 个文件</span>`;
                analyzeFiles(files, '拖拽');
            });

            log('所有事件监听器设置完成');
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
