<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件选择功能调试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bug"></i>
                            文件选择功能调试
                        </h5>
                    </div>
                    <div class="card-body">
                        
                        <!-- 测试1：直接文件输入 -->
                        <div class="mb-4">
                            <h6>测试1：直接文件输入</h6>
                            <input type="file" id="test1" class="form-control" multiple accept="image/*,.json">
                            <div id="result1" class="mt-2 text-muted">未选择文件</div>
                        </div>

                        <!-- 测试2：按钮触发 -->
                        <div class="mb-4">
                            <h6>测试2：按钮触发文件选择</h6>
                            <input type="file" id="test2-hidden" class="d-none" multiple accept="image/*,.json">
                            <button type="button" class="btn btn-primary" onclick="triggerTest2()">
                                <i class="fas fa-folder-open"></i>
                                点击选择文件
                            </button>
                            <div id="result2" class="mt-2 text-muted">未选择文件</div>
                        </div>

                        <!-- 测试3：点击区域 -->
                        <div class="mb-4">
                            <h6>测试3：点击区域触发</h6>
                            <div class="border rounded p-4 text-center" 
                                 style="cursor: pointer; background-color: #f8f9fa;" 
                                 onclick="document.getElementById('test3-hidden').click();">
                                <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                <p class="mb-0">点击此区域选择文件</p>
                            </div>
                            <input type="file" id="test3-hidden" class="d-none" multiple accept="image/*,.json">
                            <div id="result3" class="mt-2 text-muted">未选择文件</div>
                        </div>

                        <!-- 测试4：事件监听器方式 -->
                        <div class="mb-4">
                            <h6>测试4：事件监听器方式</h6>
                            <input type="file" id="test4-hidden" class="d-none" multiple accept="image/*,.json">
                            <button type="button" class="btn btn-success" id="test4-btn">
                                <i class="fas fa-upload"></i>
                                事件监听器触发
                            </button>
                            <div id="result4" class="mt-2 text-muted">未选择文件</div>
                        </div>

                        <!-- 浏览器信息 -->
                        <div class="mb-4">
                            <h6>浏览器信息</h6>
                            <div id="browser-info" class="small text-muted"></div>
                        </div>

                        <!-- 控制台日志 -->
                        <div class="mb-4">
                            <h6>控制台日志</h6>
                            <div id="console-log" class="border rounded p-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa; font-family: monospace; font-size: 12px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 自定义控制台日志
        const consoleDiv = document.getElementById('console-log');
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            consoleDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            console.log(message);
        }

        // 显示浏览器信息
        document.getElementById('browser-info').innerHTML = `
            <strong>User Agent:</strong> ${navigator.userAgent}<br>
            <strong>Platform:</strong> ${navigator.platform}<br>
            <strong>Language:</strong> ${navigator.language}<br>
            <strong>Cookie Enabled:</strong> ${navigator.cookieEnabled}<br>
            <strong>Online:</strong> ${navigator.onLine}
        `;

        // 文件显示函数
        function displayFiles(files, resultId) {
            const resultDiv = document.getElementById(resultId);
            if (files.length === 0) {
                resultDiv.innerHTML = '<span class="text-muted">未选择文件</span>';
                return;
            }

            let html = `<span class="text-success">已选择 ${files.length} 个文件:</span><br>`;
            Array.from(files).forEach((file, index) => {
                html += `<small>${index + 1}. ${file.name} (${(file.size / 1024).toFixed(1)} KB)</small><br>`;
            });
            resultDiv.innerHTML = html;
        }

        // 测试2：按钮触发
        function triggerTest2() {
            log('测试2：按钮点击，准备触发文件选择');
            try {
                document.getElementById('test2-hidden').click();
                log('测试2：文件选择对话框已触发');
            } catch (error) {
                log('测试2：错误 - ' + error.message);
            }
        }

        // 页面加载完成后设置事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始设置事件监听器');

            // 测试1：直接文件输入
            document.getElementById('test1').addEventListener('change', function(e) {
                log(`测试1：文件选择变化，选择了 ${e.target.files.length} 个文件`);
                displayFiles(e.target.files, 'result1');
            });

            // 测试2：按钮触发
            document.getElementById('test2-hidden').addEventListener('change', function(e) {
                log(`测试2：文件选择变化，选择了 ${e.target.files.length} 个文件`);
                displayFiles(e.target.files, 'result2');
            });

            // 测试3：点击区域
            document.getElementById('test3-hidden').addEventListener('change', function(e) {
                log(`测试3：文件选择变化，选择了 ${e.target.files.length} 个文件`);
                displayFiles(e.target.files, 'result3');
            });

            // 测试4：事件监听器方式
            const test4Btn = document.getElementById('test4-btn');
            const test4Input = document.getElementById('test4-hidden');

            test4Btn.addEventListener('click', function() {
                log('测试4：按钮点击事件触发');
                try {
                    test4Input.click();
                    log('测试4：文件输入点击成功');
                } catch (error) {
                    log('测试4：错误 - ' + error.message);
                }
            });

            test4Input.addEventListener('change', function(e) {
                log(`测试4：文件选择变化，选择了 ${e.target.files.length} 个文件`);
                displayFiles(e.target.files, 'result4');
            });

            log('所有事件监听器设置完成');

            // 测试点击事件
            document.addEventListener('click', function(e) {
                if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
                    log(`点击事件：${e.target.textContent || e.target.closest('button').textContent}`);
                }
            });
        });

        // 全局错误处理
        window.addEventListener('error', function(e) {
            log('全局错误：' + e.message + ' 在 ' + e.filename + ':' + e.lineno);
        });

        log('脚本加载完成');
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
