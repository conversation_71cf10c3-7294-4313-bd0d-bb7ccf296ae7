<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件夹选择功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-folder-open"></i>
                            文件夹选择功能测试
                        </h5>
                    </div>
                    <div class="card-body">
                        
                        <!-- 测试1：文件夹选择 -->
                        <div class="mb-4">
                            <h6>测试1：文件夹选择（推荐）</h6>
                            <div class="border rounded p-4 text-center" 
                                 style="cursor: pointer; background-color: #f8f9fa; border: 2px dashed #dee2e6;" 
                                 onclick="selectFolder()" id="folderZone">
                                <i class="fas fa-folder-open fa-3x text-primary mb-3"></i>
                                <h5 class="text-primary">点击选择文件夹</h5>
                                <p class="text-muted mb-0">选择包含图片和JSON文件的文件夹</p>
                            </div>
                            <input type="file" id="folderInput" class="d-none" webkitdirectory directory multiple>
                            <div id="folderResult" class="mt-2 text-muted">未选择文件夹</div>
                        </div>

                        <!-- 测试2：文件选择 -->
                        <div class="mb-4">
                            <h6>测试2：多文件选择</h6>
                            <input type="file" id="fileInput" class="form-control" multiple accept="image/*,.json">
                            <div id="fileResult" class="mt-2 text-muted">未选择文件</div>
                        </div>

                        <!-- 测试3：拖拽区域 -->
                        <div class="mb-4">
                            <h6>测试3：拖拽文件夹/文件</h6>
                            <div class="border rounded p-4 text-center" 
                                 style="background-color: #f0f8ff; border: 2px dashed #0d6efd;" 
                                 id="dropZone">
                                <i class="fas fa-cloud-upload-alt fa-3x text-info mb-3"></i>
                                <h5 class="text-info">拖拽文件夹或文件到此处</h5>
                                <p class="text-muted mb-0">支持从文件管理器直接拖拽</p>
                            </div>
                            <div id="dropResult" class="mt-2 text-muted">未拖拽文件</div>
                        </div>

                        <!-- 文件分析结果 -->
                        <div class="mb-4">
                            <h6>文件分析结果</h6>
                            <div id="analysisResult" class="border rounded p-3" style="background-color: #f8f9fa; min-height: 100px;">
                                <div class="text-muted text-center">请选择文件夹或文件进行分析</div>
                            </div>
                        </div>

                        <!-- 浏览器兼容性信息 -->
                        <div class="mb-4">
                            <h6>浏览器兼容性</h6>
                            <div id="browserInfo" class="small"></div>
                        </div>

                        <!-- 控制台日志 -->
                        <div class="mb-4">
                            <h6>操作日志</h6>
                            <div id="logContainer" class="border rounded p-3" 
                                 style="height: 200px; overflow-y: auto; background-color: #f8f9fa; font-family: monospace; font-size: 12px;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 日志功能
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML += `[${timestamp}] ${message}<br>`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        // 选择文件夹
        function selectFolder() {
            log('点击选择文件夹按钮');
            document.getElementById('folderInput').click();
        }

        // 分析文件列表
        function analyzeFiles(files, source) {
            log(`${source}: 开始分析 ${files.length} 个文件`);
            
            const imageFiles = [];
            const jsonFiles = [];
            const otherFiles = [];
            const filePairs = new Map();

            Array.from(files).forEach(file => {
                const fileName = file.name;
                const baseName = fileName.replace(/\.[^/.]+$/, "");
                
                if (/\.(png|jpg|jpeg|gif)$/i.test(fileName)) {
                    imageFiles.push(file);
                    if (!filePairs.has(baseName)) filePairs.set(baseName, {});
                    filePairs.get(baseName).image = file;
                } else if (/\.json$/i.test(fileName)) {
                    jsonFiles.push(file);
                    if (!filePairs.has(baseName)) filePairs.set(baseName, {});
                    filePairs.get(baseName).json = file;
                } else {
                    otherFiles.push(file);
                }
            });

            // 统计配对情况
            let pairedCount = 0;
            let unpairedImages = [];
            let unpairedJsons = [];

            filePairs.forEach((pair, baseName) => {
                if (pair.image && pair.json) {
                    pairedCount++;
                } else if (pair.image && !pair.json) {
                    unpairedImages.push(pair.image.name);
                } else if (!pair.image && pair.json) {
                    unpairedJsons.push(pair.json.name);
                }
            });

            // 显示分析结果
            const analysisResult = document.getElementById('analysisResult');
            let html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">文件统计</h6>
                        <ul class="small mb-0">
                            <li>总文件数: <strong>${files.length}</strong></li>
                            <li>图片文件: <strong>${imageFiles.length}</strong></li>
                            <li>JSON文件: <strong>${jsonFiles.length}</strong></li>
                            <li>其他文件: <strong>${otherFiles.length}</strong></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">配对情况</h6>
                        <ul class="small mb-0">
                            <li>成功配对: <strong>${pairedCount}</strong> 对</li>
                            <li>未配对图片: <strong>${unpairedImages.length}</strong> 个</li>
                            <li>未配对JSON: <strong>${unpairedJsons.length}</strong> 个</li>
                        </ul>
                    </div>
                </div>
            `;

            if (unpairedImages.length > 0 || unpairedJsons.length > 0) {
                html += `<hr><h6 class="text-warning">未配对文件</h6>`;
                if (unpairedImages.length > 0) {
                    html += `<div class="small"><strong>未配对图片:</strong> ${unpairedImages.slice(0, 5).join(', ')}`;
                    if (unpairedImages.length > 5) html += ` 等${unpairedImages.length}个`;
                    html += `</div>`;
                }
                if (unpairedJsons.length > 0) {
                    html += `<div class="small"><strong>未配对JSON:</strong> ${unpairedJsons.slice(0, 5).join(', ')}`;
                    if (unpairedJsons.length > 5) html += ` 等${unpairedJsons.length}个`;
                    html += `</div>`;
                }
            }

            // 显示文件路径（如果是文件夹选择）
            if (files.length > 0 && files[0].webkitRelativePath) {
                const folders = new Set();
                Array.from(files).forEach(file => {
                    const path = file.webkitRelativePath;
                    const folder = path.substring(0, path.lastIndexOf('/'));
                    if (folder) folders.add(folder);
                });
                
                html += `<hr><h6 class="text-info">文件夹结构</h6>`;
                html += `<div class="small">根文件夹: <strong>${Array.from(files)[0].webkitRelativePath.split('/')[0]}</strong></div>`;
                if (folders.size > 1) {
                    html += `<div class="small">子文件夹: ${Math.min(folders.size, 5)} 个</div>`;
                }
            }

            analysisResult.innerHTML = html;
            log(`${source}: 分析完成 - ${pairedCount}个配对，${unpairedImages.length + unpairedJsons.length}个未配对`);
        }

        // 页面加载完成后设置事件监听
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始设置事件监听器');

            // 显示浏览器兼容性信息
            const browserInfo = document.getElementById('browserInfo');
            const supportsDirectory = 'webkitdirectory' in document.createElement('input');
            browserInfo.innerHTML = `
                <div class="alert ${supportsDirectory ? 'alert-success' : 'alert-warning'} py-2">
                    <strong>文件夹选择支持:</strong> ${supportsDirectory ? '✅ 支持' : '❌ 不支持'}
                    <br><strong>浏览器:</strong> ${navigator.userAgent.split(' ').pop()}
                    <br><strong>建议:</strong> ${supportsDirectory ? '可以使用文件夹选择功能' : '请使用Chrome、Firefox或Edge浏览器'}
                </div>
            `;

            // 文件夹选择监听
            document.getElementById('folderInput').addEventListener('change', function(e) {
                const files = e.target.files;
                log(`文件夹选择: ${files.length} 个文件`);
                
                document.getElementById('folderResult').innerHTML = 
                    `<span class="text-success">已选择文件夹，包含 ${files.length} 个文件</span>`;
                
                analyzeFiles(files, '文件夹选择');
            });

            // 文件选择监听
            document.getElementById('fileInput').addEventListener('change', function(e) {
                const files = e.target.files;
                log(`文件选择: ${files.length} 个文件`);
                
                document.getElementById('fileResult').innerHTML = 
                    `<span class="text-success">已选择 ${files.length} 个文件</span>`;
                
                analyzeFiles(files, '文件选择');
            });

            // 拖拽功能
            const dropZone = document.getElementById('dropZone');
            
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#0d6efd';
                dropZone.style.backgroundColor = '#e7f3ff';
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.style.borderColor = '#0d6efd';
                dropZone.style.backgroundColor = '#f0f8ff';
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#0d6efd';
                dropZone.style.backgroundColor = '#f0f8ff';
                
                const files = e.dataTransfer.files;
                log(`拖拽: ${files.length} 个文件`);
                
                document.getElementById('dropResult').innerHTML = 
                    `<span class="text-success">已拖拽 ${files.length} 个文件</span>`;
                
                analyzeFiles(files, '拖拽');
            });

            log('所有事件监听器设置完成');
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
