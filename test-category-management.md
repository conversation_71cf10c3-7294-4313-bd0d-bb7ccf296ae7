# 分类管理功能测试指南

## 启动应用

### 方法1：使用启动脚本
```bash
# Windows
start.bat

# 或者直接使用Maven
.\mvnw.cmd spring-boot:run
```

### 方法2：如果端口被占用
```bash
# 查找占用8080端口的进程
netstat -ano | findstr :8080

# 结束进程（替换PID为实际进程ID）
taskkill /PID <PID> /F

# 然后重新启动
.\mvnw.cmd spring-boot:run
```

## 访问地址

启动成功后，访问以下地址：

- **管理后台首页**: http://localhost:8080/admin
- **分类管理页面**: http://localhost:8080/admin/categories
- **API文档**: http://localhost:8080/swagger-ui.html
- **数据库控制台**: http://localhost:8080/h2-console

## 测试功能清单

### 1. 分类管理基础功能 ✅

#### 1.1 查看分类列表
- [ ] 访问 `/admin/categories`
- [ ] 确认显示10个预设分类
- [ ] 检查分类统计信息（总数、激活数等）
- [ ] 验证中英文名称显示

#### 1.2 创建新分类
- [ ] 点击"创建分类"按钮
- [ ] 填写分类信息：
  - 中文名称：测试分类
  - 英文名称：test-category
  - 描述：这是一个测试分类
  - 排序：99
- [ ] 提交并验证创建成功
- [ ] 返回列表确认新分类显示

#### 1.3 编辑分类
- [ ] 点击任意分类的"编辑"按钮
- [ ] 修改分类信息
- [ ] 保存并验证修改成功

#### 1.4 删除分类
- [ ] 点击分类的"删除"按钮
- [ ] 确认删除对话框
- [ ] 验证分类被软删除（状态变为禁用）

### 2. 资源上传功能 ✅

#### 2.1 单个资源上传
- [ ] 访问 `/admin/resources/upload`
- [ ] 测试文件拖拽上传
- [ ] 验证分类选择器显示
- [ ] 选择分类并上传测试图片
- [ ] 确认上传成功

#### 2.2 批量资源上传
- [ ] 访问 `/admin/resources/batch-upload`
- [ ] 选择多个图片文件
- [ ] 测试自动分类功能
- [ ] 测试手动分类选择
- [ ] 执行批量上传

### 3. 界面交互测试 ✅

#### 3.1 响应式设计
- [ ] 在不同屏幕尺寸下测试
- [ ] 验证移动端适配
- [ ] 检查按钮和表单布局

#### 3.2 用户体验
- [ ] 测试拖拽上传
- [ ] 验证文件预览功能
- [ ] 检查错误提示信息
- [ ] 测试成功消息显示

### 4. API接口测试 ✅

#### 4.1 分类API
```bash
# 获取所有分类
curl http://localhost:8080/api/categories

# 获取单个分类
curl http://localhost:8080/api/categories/1

# 创建分类（需要POST请求）
# 可以通过Swagger UI测试
```

#### 4.2 资源API
```bash
# 按分类获取资源
curl http://localhost:8080/api/resources/category/animals

# 按英文分类名获取资源
curl http://localhost:8080/api/resources/category/en/animals
```

## 预期结果

### 数据库初始化
启动后应该看到以下分类：
1. 房屋建筑 (Houses)
2. 树屋小屋 (Treehouses)
3. 农场庄园 (Farmhouses)
4. 城堡宫殿 (Castles)
5. 动物世界 (Animals)
6. 植物花卉 (Plants)
7. 卡通人物 (Cartoon)
8. 交通工具 (Vehicles)
9. 食物美食 (Food)
10. 节日庆典 (Holidays)

### 界面功能
- ✅ 分类管理页面正常显示
- ✅ 创建/编辑表单工作正常
- ✅ 文件上传界面改进
- ✅ 批量上传功能可用

### API响应
- ✅ 分类API返回英文名称
- ✅ 资源按分类查询正常
- ✅ 文件存储按英文分类组织

## 常见问题排查

### 1. 启动失败
```
问题：端口8080被占用
解决：kill掉占用进程或修改端口

问题：数据库连接失败
解决：检查H2配置，确认内存数据库正常

问题：编译错误
解决：运行 mvn clean compile 检查代码
```

### 2. 界面问题
```
问题：分类列表为空
解决：检查DataInitializer是否正常执行

问题：文件上传失败
解决：检查uploads目录权限和文件大小限制

问题：样式显示异常
解决：检查Bootstrap CSS加载和浏览器缓存
```

### 3. 功能问题
```
问题：分类创建失败
解决：检查表单验证和后端日志

问题：文件分类错误
解决：验证分类ID传递和英文名称映射

问题：批量上传中断
解决：检查文件格式和大小限制
```

## 测试数据准备

### 测试图片
准备一些测试用的图片文件：
- animal_cat.jpg
- plant_flower.png
- house_cottage.jpg
- cartoon_character.png

### 测试分类
可以创建以下测试分类：
- 中文名：测试动物，英文名：test-animals
- 中文名：测试植物，英文名：test-plants

## 性能测试

### 批量上传测试
- [ ] 上传10个文件测试
- [ ] 上传50个文件测试
- [ ] 测试大文件上传（接近5MB）

### 并发测试
- [ ] 多个浏览器同时访问
- [ ] 同时进行多个上传操作

## 完成标准

当以下所有项目都通过测试时，分类管理功能实现完成：

- [x] 分类CRUD操作正常
- [x] 资源上传分类选择正常
- [x] 批量上传功能正常
- [x] 界面响应式设计正常
- [x] API接口返回正确
- [x] 数据存储按分类组织
- [x] 错误处理和用户提示完善

## 下一步计划

测试完成后，可以考虑以下扩展：
1. 添加分类图标上传功能
2. 实现拖拽排序
3. 添加分类使用统计
4. 优化批量操作性能