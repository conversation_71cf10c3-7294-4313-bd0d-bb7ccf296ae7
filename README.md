# Dreaming涂色App后端服务

这是一个为Android涂色应用提供资源管理和版本更新服务的后端系统。

## 功能特性

### 🎨 资源管理
- 涂色图片上传、存储和管理
- 支持多种图片格式(JPG, PNG, GIF等)
- 资源分类和难度等级管理
- 资源搜索和筛选功能
- 热门资源和最新资源推荐

### 📱 版本管理
- 应用版本控制和发布管理
- 客户端版本检查和更新通知
- 增量更新支持
- 强制更新控制

### 🔧 技术特性
- RESTful API设计
- 完整的API文档(Swagger)
- 文件上传和下载服务
- 数据库持久化存储
- 全局异常处理
- CORS跨域支持

## 技术栈

- **后端框架**: Spring Boot 3.2.0
- **数据库**: H2(开发) / MySQL(生产)
- **ORM**: Spring Data JPA
- **API文档**: SpringDoc OpenAPI 3
- **构建工具**: Maven
- **Java版本**: 17

## 快速开始

### 环境要求
- Java 17+
- Maven 3.6+
- MySQL 8.0+(生产环境)

### 运行步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd Dreaming
   ```

2. **编译项目**
   ```bash
   mvn clean compile
   ```

3. **运行应用**
   ```bash
   mvn spring-boot:run
   ```

4. **访问服务**
   - 应用地址: http://localhost:8080/api
   - API文档: http://localhost:8080/api/swagger-ui.html
   - H2控制台: http://localhost:8080/api/h2-console

## API接口

### 资源管理接口
- `POST /api/resources/upload` - 上传资源
- `GET /api/resources` - 获取所有资源
- `GET /api/resources/{id}` - 获取单个资源
- `GET /api/resources/category/{category}` - 按分类获取资源
- `GET /api/resources/popular` - 获取热门资源
- `GET /api/resources/latest` - 获取最新资源

### 版本管理接口
- `GET /api/version/check?currentVersion=1.0.0` - 检查版本更新
- `GET /api/version/current` - 获取当前版本
- `POST /api/version` - 创建新版本
- `PUT /api/version/{version}/current` - 设置当前版本

### 文件服务接口
- `GET /api/files/{filename}` - 下载文件
- `GET /api/files/{filename}/info` - 获取文件信息

## 配置说明

### 数据库配置
开发环境默认使用H2内存数据库，生产环境需要配置MySQL：

```yaml
spring:
  datasource:
    url: *********************************************
    username: your_username
    password: your_password
```

### 文件存储配置
```yaml
app:
  file-storage:
    upload-dir: ./uploads  # 文件上传目录
    base-url: http://localhost:8080/api/files  # 文件访问基础URL
```

## 项目结构

```
src/main/java/com/dreaming/
├── controller/          # 控制器层
├── service/            # 服务层
├── repository/         # 数据访问层
├── entity/             # 实体类
├── dto/                # 数据传输对象
├── config/             # 配置类
└── exception/          # 异常处理
```

## 开发指南

### 添加新资源类型
1. 在`ColoringResource`实体中添加新字段
2. 更新`ResourceDTO`和相关服务方法
3. 在控制器中添加对应的API接口

### 扩展版本管理
1. 修改`AppVersion`实体添加新功能
2. 在`VersionService`中实现相关业务逻辑
3. 更新API接口和文档

## 部署说明

### 生产环境部署
1. 修改`application.yml`中的数据库配置
2. 设置合适的文件存储路径
3. 配置日志输出路径
4. 使用`mvn clean package`打包
5. 运行`java -jar target/coloring-app-backend-1.0.0.jar`

### Docker部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/coloring-app-backend-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 常见问题

### Q: 如何上传大文件？
A: 在`application.yml`中调整文件上传限制：
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
```

### Q: 如何备份数据？
A: 使用MySQL的mysqldump工具或者通过API导出数据。

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。
