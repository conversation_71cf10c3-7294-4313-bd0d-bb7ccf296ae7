# 启动脚本使用指南

## 📁 **可用的启动脚本**

项目提供了三个不同版本的启动脚本，适用于不同的使用场景：

### 1. **start.bat** - 标准版本（推荐）
- ✅ **自动端口检查和清理**
- ✅ **简化的用户交互**
- ✅ **基础错误处理**
- ✅ **适合日常开发使用**

### 2. **start-simple.bat** - 简化版本
- ✅ **最基础的功能**
- ✅ **快速启动**
- ✅ **适合熟悉环境的用户**

### 3. **start-enhanced.bat** - 增强版本
- ✅ **详细的系统检查**
- ✅ **管理员权限检测**
- ✅ **完整的用户交互**
- ✅ **详细的错误诊断**
- ✅ **适合生产环境或问题排查**

## 🚀 **主要功能特性**

### 端口占用处理
所有脚本都会自动检查端口8083是否被占用：
- **自动检测**：扫描端口占用情况
- **智能停止**：自动停止占用端口的进程
- **安全确认**：对非Java进程会询问用户确认
- **错误处理**：权限不足时提供解决建议

### 进程清理
- **旧进程检测**：查找可能的旧应用进程
- **智能识别**：通过命令行参数识别相关进程
- **自动清理**：停止冲突的Java进程
- **状态反馈**：显示清理结果

### 环境检查
- **Java环境**：检查Java是否安装及版本
- **项目文件**：验证项目文件完整性
- **目录创建**：自动创建必要的目录
- **权限检测**：检查管理员权限（增强版）

## 💻 **使用方法**

### 基本使用
```batch
# 双击运行或命令行执行
start.bat

# 或者选择其他版本
start-simple.bat
start-enhanced.bat
```

### 命令行使用
```cmd
# 在项目根目录打开命令提示符
cd /d "D:\library\Dreaming"

# 运行启动脚本
start.bat
```

### 管理员模式运行
如果遇到权限问题：
1. 右键点击脚本
2. 选择"以管理员身份运行"
3. 或在管理员命令提示符中执行

## 🔧 **脚本执行流程**

### start.bat 执行步骤：
```
[1/5] 检查Java环境
  ├── 检查Java是否安装
  ├── 验证Java版本
  └── 显示版本信息

[2/5] 检查项目文件
  ├── 验证pom.xml存在
  ├── 检查主类文件
  └── 确认项目完整性

[3/5] 创建必要目录
  ├── 创建uploads目录
  ├── 创建logs目录
  └── 确认目录权限

[4/5] 检查并清理端口占用
  ├── 扫描端口8083占用情况
  ├── 自动停止占用进程
  ├── 清理旧的应用进程
  └── 等待端口释放

[5/5] 启动应用
  ├── 选择Maven或Maven Wrapper
  ├── 设置编码和输出格式
  ├── 启动Spring Boot应用
  └── 显示访问地址
```

## ⚠️ **常见问题及解决方案**

### 1. 端口占用问题
**问题**：端口8083被其他程序占用
**解决**：
- 脚本会自动检测并停止占用进程
- 如果自动停止失败，请以管理员身份运行
- 或手动停止占用端口的程序

### 2. Java环境问题
**问题**：未找到Java环境
**解决**：
- 安装Java 11或更高版本
- 配置JAVA_HOME环境变量
- 确保java命令在PATH中

### 3. 权限不足问题
**问题**：无法停止进程或创建目录
**解决**：
- 以管理员身份运行脚本
- 检查防病毒软件是否阻止
- 确保对项目目录有写权限

### 4. Maven问题
**问题**：Maven命令失败
**解决**：
- 检查网络连接（依赖下载）
- 清理Maven缓存：`mvn clean`
- 使用Maven Wrapper：`mvnw.cmd`

### 5. 内存不足问题
**问题**：应用启动时内存不足
**解决**：
- 关闭其他占用内存的应用
- 增加JVM内存参数
- 检查系统可用内存

## 🎯 **脚本选择建议**

### 日常开发 → **start.bat**
- 功能完整，操作简单
- 自动处理常见问题
- 适合大多数开发场景

### 快速测试 → **start-simple.bat**
- 启动速度最快
- 最少的用户交互
- 适合频繁重启测试

### 问题排查 → **start-enhanced.bat**
- 详细的系统信息
- 完整的错误诊断
- 适合解决复杂问题

### 生产部署 → **start-enhanced.bat**
- 完整的环境检查
- 详细的状态反馈
- 适合服务器部署

## 📊 **性能对比**

| 特性 | start.bat | start-simple.bat | start-enhanced.bat |
|------|-----------|------------------|-------------------|
| 启动速度 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 功能完整性 | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 错误处理 | ⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 用户友好 | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 适用场景 | 日常开发 | 快速测试 | 生产/排查 |

## 🔄 **自定义配置**

### 修改端口号
在脚本中找到并修改：
```batch
set APP_PORT=8083
```

### 添加JVM参数
修改Maven启动命令：
```batch
mvn spring-boot:run -Dfile.encoding=UTF-8 -Xmx1024m -Xms512m
```

### 修改超时时间
调整等待时间：
```batch
timeout /t 3 /nobreak >nul
```

## 📝 **维护建议**

1. **定期更新**：根据项目变化更新脚本
2. **测试验证**：在不同环境下测试脚本功能
3. **日志记录**：保留启动日志用于问题排查
4. **权限管理**：确保脚本有适当的执行权限
5. **备份脚本**：保留工作正常的脚本版本

---

## 🎉 **总结**

通过这些启动脚本，你可以：
- ✅ **自动处理端口占用问题**
- ✅ **简化应用启动流程**
- ✅ **快速诊断和解决问题**
- ✅ **提高开发效率**
- ✅ **确保环境一致性**

选择适合你使用场景的脚本版本，享受更流畅的开发体验！
