<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="layout/base :: layout(~{::title}, ~{::content})">
<head>
    <title th:text="'资源详情 - ' + ${resource.name}">资源详情</title>
</head>
<body>
    <div th:fragment="content">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-image"></i>
                资源详情
            </h2>
            <div>
                <a th:href="@{/admin/resources}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
                <button type="button" 
                        class="btn btn-outline-warning"
                        th:onclick="'toggleStatus(' + ${resource.id} + ')'">
                    <i class="fas fa-power-off"></i>
                    <span th:text="${resource.active} ? '停用' : '激活'">切换状态</span>
                </button>
                <button type="button" 
                        class="btn btn-outline-danger"
                        th:onclick="'deleteResource(' + ${resource.id} + ', \'' + ${resource.name} + '\')'">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        </div>

        <div class="row">
            <!-- 资源图片 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-image"></i>
                            资源图片
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <img th:src="${resource.downloadUrl}" 
                             th:alt="${resource.name}"
                             class="img-fluid rounded shadow"
                             style="max-height: 500px;"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjUwMCIgdmlld0JveD0iMCAwIDUwMCA1MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MDAiIGhlaWdodD0iNTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNTAgMjAwQzI2My4wNyAyMDAgMjc1IDIxMy4wNyAyNzUgMjI1QzI3NSAyMzYuOTMgMjYzLjA3IDI1MCAyNTAgMjUwQzIzNi45MyAyNTAgMjI1IDIzNi45MyAyMjUgMjI1QzIyNSAyMTMuMDcgMjM2LjkzIDIwMCAyNTAgMjAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMzUwIDM1MEgxNTBMMjAwIDI1MEwyNTAgMzAwTDMwMCAyMDBMMzUwIDM1MFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+Cg=='">
                        
                        <div class="mt-3">
                            <a th:href="${resource.downloadUrl}" 
                               target="_blank" 
                               class="btn btn-primary"
                               th:onclick="'recordDownload(' + ${resource.id} + ')'">
                                <i class="fas fa-download"></i> 下载原图
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 资源信息 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i>
                            基本信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" style="width: 30%;">资源名称:</td>
                                <td th:text="${resource.name}">资源名称</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">资源描述:</td>
                                <td th:text="${resource.description ?: '无描述'}">资源描述</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">分类:</td>
                                <td>
                                    <span class="badge bg-primary" th:text="${resource.category}">分类</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">难度等级:</td>
                                <td>
                                    <span class="badge bg-warning">
                                        <span th:text="${resource.difficulty}">1</span> 级
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">版本:</td>
                                <td>
                                    <span class="badge bg-info" th:text="${resource.version}">1.0.0</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">状态:</td>
                                <td>
                                    <span th:if="${resource.active}" class="badge bg-success">激活</span>
                                    <span th:unless="${resource.active}" class="badge bg-secondary">停用</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">下载次数:</td>
                                <td>
                                    <span class="badge bg-dark" th:text="${resource.downloadCount}">0</span> 次
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">排序顺序:</td>
                                <td th:text="${resource.sortOrder}">0</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 文件信息 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file"></i>
                            文件信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold" style="width: 30%;">文件大小:</td>
                                <td th:text="${#numbers.formatDecimal(resource.fileSize / 1024, 1, 2)} + ' KB'">0 KB</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">文件类型:</td>
                                <td th:text="${resource.contentType}">image/png</td>
                            </tr>
                            <tr th:if="${resource.width != null and resource.height != null}">
                                <td class="fw-bold">图片尺寸:</td>
                                <td>
                                    <span th:text="${resource.width}">0</span> × 
                                    <span th:text="${resource.height}">0</span> 像素
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">创建时间:</td>
                                <td th:text="${#temporals.format(resource.createdAt, 'yyyy-MM-dd HH:mm:ss')}">2023-01-01 12:00:00</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">更新时间:</td>
                                <td th:text="${#temporals.format(resource.updatedAt, 'yyyy-MM-dd HH:mm:ss')}">2023-01-01 12:00:00</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 标签信息 -->
                <div th:if="${resource.tags != null and !#lists.isEmpty(resource.tags)}" class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tags"></i>
                            标签
                        </h5>
                    </div>
                    <div class="card-body">
                        <span th:each="tag : ${resource.tags}" 
                              class="badge bg-light text-dark me-2 mb-2"
                              th:text="${tag}">标签</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作历史 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history"></i>
                            操作记录
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">资源创建</h6>
                                    <p class="timeline-text">
                                        资源已成功上传并创建
                                    </p>
                                    <small class="text-muted" 
                                           th:text="${#temporals.format(resource.createdAt, 'yyyy-MM-dd HH:mm:ss')}">
                                        2023-01-01 12:00:00
                                    </small>
                                </div>
                            </div>
                            
                            <div th:if="${resource.updatedAt != resource.createdAt}" class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">资源更新</h6>
                                    <p class="timeline-text">
                                        资源信息已更新
                                    </p>
                                    <small class="text-muted" 
                                           th:text="${#temporals.format(resource.updatedAt, 'yyyy-MM-dd HH:mm:ss')}">
                                        2023-01-01 12:00:00
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除资源 "<span id="deleteResourceName"></span>" 吗？</p>
                    <p class="text-danger">此操作不可恢复，文件也会被永久删除。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <form id="deleteForm" method="post" style="display: inline;">
                        <button type="submit" class="btn btn-danger">确认删除</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #dee2e6;
        }
        .timeline-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #007bff;
        }
        .timeline-title {
            margin-bottom: 5px;
            color: #495057;
        }
        .timeline-text {
            margin-bottom: 5px;
            color: #6c757d;
        }
    </style>

    <script>
        function deleteResource(id, name) {
            document.getElementById('deleteResourceName').textContent = name;
            document.getElementById('deleteForm').action = '/admin/resources/' + id + '/delete';
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function toggleStatus(id) {
            if (confirm('确定要切换资源状态吗？')) {
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/resources/' + id + '/toggle';
                document.body.appendChild(form);
                form.submit();
            }
        }

        function recordDownload(id) {
            // 记录下载次数
            fetch('/api/resources/' + id + '/download', {
                method: 'POST'
            }).catch(error => {
                console.error('记录下载失败:', error);
            });
        }
    </script>
</body>
</html>
