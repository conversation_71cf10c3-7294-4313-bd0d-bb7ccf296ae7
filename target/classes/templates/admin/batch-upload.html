<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: layout(~{::title}, ~{::content})}">
<head>
    <title>资源上传 - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-upload"></i>
                资源上传
            </h2>
            <a th:href="@{/admin/resources}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i>
                资源列表
            </a>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload"></i>
                    批量上传资源
                </h5>
            </div>
            <div class="card-body">
                <form action="/admin/resources/batch-upload" method="post" enctype="multipart/form-data">
                    
                    <!-- 文件选择区域 -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-images"></i>
                            选择文件 <span class="text-danger">*</span>
                        </label>
                        <div class="file-drop-zone" id="dropZone">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>拖拽文件到此处或点击选择</h5>
                            <p class="text-muted">支持图片文件(JPG, PNG, GIF)和JSON配置文件</p>
                            <input type="file" name="files" id="filesInput" class="d-none" 
                                   accept="image/*,.json" multiple required>
                            <button type="button" class="btn btn-primary" id="selectFilesBtn">
                                <i class="fas fa-folder-open"></i>
                                选择文件
                            </button>
                        </div>
                        
                        <!-- 文件列表 -->
                        <div id="filesList" class="mt-3" style="display: none;">
                            <div class="alert alert-light">
                                <h6><i class="fas fa-list"></i> 已选择的文件：</h6>
                                <div id="filesContainer"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 分类选择 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-tags"></i>
                            选择分类
                        </label>
                        
                        <!-- 自动分类选项 -->
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="autoCategory" 
                                   name="autoCategory" onchange="toggleCategoryMode()">
                            <label class="form-check-label" for="autoCategory">
                                <i class="fas fa-magic"></i>
                                根据文件名自动分类
                            </label>
                        </div>

                        <!-- 手动分类选择 -->
                        <div id="manualCategorySection">
                            <div th:if="${categoryDTOs != null and !#lists.isEmpty(categoryDTOs)}">
                                <select class="form-select" name="defaultCategoryId" id="categorySelect" required>
                                    <option value="">请选择分类</option>
                                    <option th:each="category : ${categoryDTOs}" 
                                            th:value="${category.id}" 
                                            th:text="${category.name}">分类名称</option>
                                </select>
                            </div>
                            
                            <!-- 降级方案 -->
                            <div th:if="${categoryDTOs == null or #lists.isEmpty(categoryDTOs)}">
                                <select class="form-select" name="defaultCategory" required>
                                    <option value="">请选择分类</option>
                                    <option th:each="category : ${categories}" th:value="${category}" th:text="${category}">分类</option>
                                </select>
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    请先在 <a th:href="@{/admin/categories}">分类管理</a> 中创建分类
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 难度等级 -->
                        <div class="col-md-6 mb-3">
                            <label for="difficulty" class="form-label">
                                <i class="fas fa-star"></i>
                                难度等级 <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" name="difficulty" id="difficulty" required>
                                <option value="">请选择难度</option>
                                <option value="1">⭐ 简单</option>
                                <option value="2" selected>⭐⭐ 普通</option>
                                <option value="3">⭐⭐⭐ 困难</option>
                                <option value="4">⭐⭐⭐⭐ 专家</option>
                                <option value="5">⭐⭐⭐⭐⭐ 大师</option>
                            </select>
                        </div>

                        <!-- 版本 -->
                        <div class="col-md-6 mb-3">
                            <label for="version" class="form-label">
                                <i class="fas fa-code-branch"></i>
                                版本号
                            </label>
                            <input type="text" class="form-control" name="version" id="version" 
                                   th:value="${currentVersion}" readonly>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-end gap-2">
                        <a th:href="@{/admin/resources}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            取消
                        </a>
                        <button type="submit" class="btn btn-primary" id="uploadBtn" disabled>
                            <i class="fas fa-upload"></i>
                            开始上传
                        </button>
                    </div>
                </form>
            </div>
        </div>

        
        <!-- 使用说明 -->
        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-info-circle"></i> 使用说明</h6>
            <ul class="mb-0">
                <li><strong>文件格式</strong>：支持JPG、PNG、GIF图片文件和JSON配置文件</li>
                <li><strong>自动分类</strong>：勾选后系统根据文件名关键词自动分类（如"animal"→动物分类）</li>
                <li><strong>文件配对</strong>：同名的图片和JSON文件会自动配对（如cat.jpg和cat.json）</li>
                <li><strong>批量处理</strong>：可同时选择多个文件进行批量上传</li>
            </ul>
        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-cogs text-warning"></i> 处理流程</h6>
                            <ol class="small text-muted">
                                <li>选择多个图片文件</li>
                                <li>设置分类和难度</li>
                                <li>点击开始上传</li>
                                <li>系统自动处理每个文件</li>
                                <li>显示上传结果</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- 上传进度 -->
                <div class="card mt-3" id="progressCard" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-progress-bar"></i>
                            上传进度
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-2">
                            <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="progressText" class="text-center small">准备上传...</div>
                    </div>
                </div>
            </div>
        </div>

        <style>
            .file-drop-zone {
                border: 3px dashed #007bff;
                border-radius: 10px;
                padding: 40px;
                text-align: center;
                background-color: #f8f9fa;
                transition: all 0.3s ease;
                cursor: pointer;
            }
            .file-drop-zone:hover {
                border-color: #0056b3;
                background-color: #e3f2fd;
            }
            .file-drop-zone.dragover {
                border-color: #28a745;
                background-color: #d4edda;
            }
            .file-list {
                max-height: 300px;
                overflow-y: auto;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                background: white;
            }
            .file-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            .file-item:last-child {
                border-bottom: none;
            }
            .form-check-label {
                cursor: pointer;
                padding: 0.5rem;
                border-radius: 0.375rem;
                transition: background-color 0.15s ease-in-out;
            }
            .form-check-label:hover {
                background-color: #f8f9fa;
            }
            .form-check-input:checked + .form-check-label {
                background-color: #e3f2fd;
            }
        </style>

        <script>
            let selectedFiles = [];

            document.addEventListener('DOMContentLoaded', function() {
                const dropZone = document.getElementById('dropZone');
                const filesInput = document.getElementById('filesInput');
                const filesList = document.getElementById('filesList');
                const filesContainer = document.getElementById('filesContainer');
                const uploadBtn = document.getElementById('uploadBtn');
                const selectFilesBtn = document.getElementById('selectFilesBtn');

                // 点击选择文件按钮
                selectFilesBtn.addEventListener('click', function() {
                    filesInput.click();
                });

                // 点击拖拽区域选择文件
                dropZone.addEventListener('click', function(e) {
                    if (e.target === dropZone || e.target.closest('.file-drop-zone') === dropZone) {
                        filesInput.click();
                    }
                });

                // 拖拽功能
                dropZone.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    dropZone.classList.add('dragover');
                });

                dropZone.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    dropZone.classList.remove('dragover');
                });

                dropZone.addEventListener('drop', function(e) {
                    e.preventDefault();
                    dropZone.classList.remove('dragover');
                    
                    const files = Array.from(e.dataTransfer.files);
                    handleFiles(files);
                });

                // 文件选择
                filesInput.addEventListener('change', function(e) {
                    const files = Array.from(e.target.files);
                    handleFiles(files);
                });

                function handleFiles(files) {
                    selectedFiles = files.filter(file => 
                        file.type.startsWith('image/') || file.name.endsWith('.json')
                    );
                    
                    updateFilesList();
                    updateUploadButton();
                }

                function updateFilesList() {
                    if (selectedFiles.length === 0) {
                        filesList.style.display = 'none';
                        return;
                    }

                    filesList.style.display = 'block';
                    filesContainer.innerHTML = '';

                    selectedFiles.forEach((file, index) => {
                        const fileItem = document.createElement('div');
                        fileItem.className = 'd-flex justify-content-between align-items-center py-2 border-bottom';
                        
                        const isImage = file.type.startsWith('image/');
                        const icon = isImage ? 'fa-image text-primary' : 'fa-file-code text-info';
                        
                        fileItem.innerHTML = `
                            <div class="d-flex align-items-center">
                                <i class="fas ${icon} me-2"></i>
                                <div>
                                    <div class="fw-bold">${file.name}</div>
                                    <small class="text-muted">${formatFileSize(file.size)}</small>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                    onclick="removeFile(${index})">
                                <i class="fas fa-times"></i>
                            </button>
                        `;
                        filesContainer.appendChild(fileItem);
                    });
                }

                function updateUploadButton() {
                    const fileCount = selectedFiles.length;
                    uploadBtn.disabled = fileCount === 0;
                    
                    if (fileCount > 0) {
                        uploadBtn.innerHTML = `
                            <i class="fas fa-upload"></i>
                            上传 ${fileCount} 个文件
                        `;
                    } else {
                        uploadBtn.innerHTML = `
                            <i class="fas fa-upload"></i>
                            开始上传
                        `;
                    }
                }

                // 全局函数
                window.removeFile = function(index) {
                    selectedFiles.splice(index, 1);
                    updateFilesList();
                    updateUploadButton();
                };

                window.toggleCategoryMode = function() {
                    const autoCategory = document.getElementById('autoCategory');
                    const manualSection = document.getElementById('manualCategorySection');
                    const categorySelect = document.getElementById('categorySelect');
                    
                    if (autoCategory.checked) {
                        manualSection.style.opacity = '0.5';
                        if (categorySelect) {
                            categorySelect.required = false;
                            categorySelect.disabled = true;
                        }
                    } else {
                        manualSection.style.opacity = '1';
                        if (categorySelect) {
                            categorySelect.required = true;
                            categorySelect.disabled = false;
                        }
                    }
                };

                function formatFileSize(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                }
            });
        </script>
    </div>
</body>
</html>
        }
        .file-icon {
            width: 20px;
            text-align: center;
        }
        .progress-container {
            display: none;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar p-0">
                <div class="list-group list-group-flush">
                    <a href="/admin" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-tachometer-alt"></i> 仪表盘
                    </a>
                    <a href="/admin/resources" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-images"></i> 资源管理
                    </a>
                    <a href="/admin/resources/upload" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-upload"></i> 上传资源
                    </a>
                    <a href="/admin/resources/batch-upload" class="list-group-item list-group-item-action bg-transparent text-white active">
                        <i class="fas fa-folder-open"></i> 批量上传
                    </a>
                    <a href="/admin/versions" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-code-branch"></i> 版本管理
                    </a>
                    <a href="/admin/export" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-download"></i> 数据导出
                    </a>
                    <a href="/admin/settings" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-cog"></i> 系统设置
                    </a>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10">
                <div class="container-fluid py-4">
                    <!-- 页面标题 -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>
                            <i class="fas fa-folder-open"></i>
                            批量上传资源
                        </h2>
                        <div>
                            <a href="/admin/resources/upload" class="btn btn-outline-primary me-2">
                                <i class="fas fa-upload"></i> 单个上传
                            </a>
                            <a href="/admin/resources" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> 返回资源列表
                            </a>
                        </div>
                    </div>

                    <!-- 成功/错误消息 -->
                    <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i>
                        <span th:text="${successMessage}"></span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    
                    <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle"></i>
                        <span th:text="${errorMessage}"></span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>

                    <div class="row">
                        <div class="col-lg-8">
                            <!-- 批量上传表单 -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-folder-open"></i>
                                        选择文件夹或多个文件
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form method="post" enctype="multipart/form-data" action="/admin/resources/batch-upload" id="batchUploadForm">
                                        
                                        <!-- 文件选择区域 -->
                                        <div class="file-drop-zone" id="dropZone" onclick="document.getElementById('fileInput').click()">
                                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                            <h4>拖拽文件到此处或点击选择</h4>
                                            <p class="text-muted">支持选择文件夹或多个文件<br>
                                            支持 PNG, JPG, JSON 格式</p>
                                            <input type="file" 
                                                   id="fileInput" 
                                                   name="files" 
                                                   multiple 
                                                   webkitdirectory
                                                   style="display: none;"
                                                   onchange="handleFileSelect(this.files)">
                                        </div>

                                        <!-- 文件选择模式切换 -->
                                        <div class="mt-3 text-center">
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-outline-primary" onclick="selectFolder()">
                                                    <i class="fas fa-folder"></i> 选择文件夹
                                                </button>
                                                <button type="button" class="btn btn-outline-primary" onclick="selectFiles()">
                                                    <i class="fas fa-file"></i> 选择多个文件
                                                </button>
                                            </div>
                                        </div>

                                        <!-- 已选择的文件列表 -->
                                        <div class="mt-4" id="fileListContainer" style="display: none;">
                                            <h6>已选择的文件：</h6>
                                            <div class="file-list" id="fileList"></div>
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    <span id="fileCount">0</span> 个文件，
                                                    <span id="groupCount">0</span> 组资源
                                                </small>
                                            </div>
                                        </div>

                                        <!-- 批量设置 -->
                                        <div class="row mt-4">
                                            <!-- 自动分类 -->
                                            <div class="col-md-6 mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="autoCategory" name="autoCategory" checked>
                                                    <label class="form-check-label" for="autoCategory">
                                                        <strong>智能自动分类</strong>
                                                    </label>
                                                </div>
                                                <small class="text-muted">根据文件名自动识别分类</small>
                                            </div>

                                            <!-- 默认分类 -->
                                            <div class="col-md-6 mb-3">
                                                <label for="defaultCategory" class="form-label">默认分类</label>
                                                <div class="input-group">
                                                    <input type="text" 
                                                           class="form-control" 
                                                           id="defaultCategory" 
                                                           name="defaultCategory" 
                                                           readonly
                                                           placeholder="自动分类失败时使用">
                                                    <input type="hidden" id="defaultCategoryId" name="defaultCategoryId" value="1">
                                                    <button type="button" class="btn btn-outline-primary" onclick="openCategorySelector()">
                                                        <i class="fas fa-list"></i> 选择
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <!-- 难度等级 -->
                                            <div class="col-md-6 mb-3">
                                                <label for="difficulty" class="form-label">默认难度等级 <span class="text-danger">*</span></label>
                                                <select class="form-select" id="difficulty" name="difficulty" required>
                                                    <option value="">请选择难度</option>
                                                    <option value="1">1 - 非常简单</option>
                                                    <option value="2">2 - 简单</option>
                                                    <option value="3" selected>3 - 中等</option>
                                                    <option value="4">4 - 困难</option>
                                                    <option value="5">5 - 非常困难</option>
                                                </select>
                                            </div>

                                            <!-- 版本号 -->
                                            <div class="col-md-6 mb-3">
                                                <label for="version" class="form-label">版本号 <span class="text-danger">*</span></label>
                                                <input type="text" 
                                                       class="form-control" 
                                                       id="version" 
                                                       name="version" 
                                                       required
                                                       th:value="${currentVersion}"
                                                       placeholder="请输入版本号">
                                                <div class="form-text">建议使用当前版本号</div>
                                            </div>
                                        </div>

                                        <!-- 提交按钮 -->
                                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                            <button type="button" class="btn btn-secondary me-md-2" onclick="clearFiles()">
                                                <i class="fas fa-times"></i> 清空文件
                                            </button>
                                            <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                                <i class="fas fa-upload"></i> 开始批量上传
                                            </button>
                                        </div>

                                        <!-- 上传进度 -->
                                        <div class="progress-container" id="progressContainer">
                                            <div class="progress">
                                                <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                                            </div>
                                            <div class="mt-2 text-center">
                                                <small id="progressText">准备上传...</small>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <!-- 使用说明 -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle"></i>
                                        使用说明
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <h6>📁 文件夹结构示例：</h6>
                                    <pre class="small">
material_production/
├── output/
│   └── result/
│       ├── house_outline.png
│       ├── house_data.json
│       ├── cat_outline.png
│       ├── cat_data.json
│       └── ...
                                    </pre>
                                    
                                    <h6 class="mt-3">🎯 智能分类规则：</h6>
                                    <ul class="small">
                                        <li><strong>动物</strong>: cat, dog, animal, 猫, 狗, 动物</li>
                                        <li><strong>植物</strong>: flower, plant, tree, 花, 植物, 树</li>
                                        <li><strong>房屋</strong>: house, building, home, 房, 屋, 建筑</li>
                                        <li><strong>交通</strong>: car, vehicle, truck, 汽车, 车, 交通</li>
                                        <li><strong>卡通</strong>: cartoon, character, 人物, 卡通</li>
                                    </ul>

                                    <h6 class="mt-3">📋 文件配对规则：</h6>
                                    <ul class="small">
                                        <li>相同基础名称的文件会自动配对</li>
                                        <li>支持 _outline, _data, -outline, -data 后缀</li>
                                        <li>图片格式: PNG, JPG, JPEG, GIF</li>
                                        <li>数据格式: JSON</li>
                                    </ul>
                                </div>
                            </div>

                            <!-- 处理状态 -->
                            <div class="card mt-3" id="statusCard" style="display: none;">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-tasks"></i>
                                        处理状态
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="statusList"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedFiles = [];
        let fileGroups = {};

        // 文件选择模式切换
        function selectFolder() {
            const input = document.getElementById('fileInput');
            input.webkitdirectory = true;
            input.multiple = true;
            input.click();
        }

        function selectFiles() {
            const input = document.getElementById('fileInput');
            input.webkitdirectory = false;
            input.multiple = true;
            input.click();
        }

        // 拖拽功能
        const dropZone = document.getElementById('dropZone');
        
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            handleFileSelect(e.dataTransfer.files);
        });

        // 处理文件选择
        function handleFileSelect(files) {
            selectedFiles = Array.from(files);
            groupFiles();
            displayFiles();
            updateSubmitButton();
        }

        // 文件分组
        function groupFiles() {
            fileGroups = {};
            
            selectedFiles.forEach(file => {
                const baseName = extractBaseName(file.name);
                if (!fileGroups[baseName]) {
                    fileGroups[baseName] = [];
                }
                fileGroups[baseName].push(file);
            });
        }

        // 提取基础文件名
        function extractBaseName(filename) {
            const lastDot = filename.lastIndexOf('.');
            let nameWithoutExt = lastDot > 0 ? filename.substring(0, lastDot) : filename;
            
            return nameWithoutExt.replace(/_outline$|_coloring$|_data$|-outline$|-coloring$|-data$/g, '');
        }

        // 显示文件列表
        function displayFiles() {
            const container = document.getElementById('fileListContainer');
            const fileList = document.getElementById('fileList');
            const fileCount = document.getElementById('fileCount');
            const groupCount = document.getElementById('groupCount');
            
            if (selectedFiles.length === 0) {
                container.style.display = 'none';
                return;
            }
            
            container.style.display = 'block';
            fileList.innerHTML = '';
            
            Object.entries(fileGroups).forEach(([baseName, files]) => {
                const groupDiv = document.createElement('div');
                groupDiv.className = 'mb-2 p-2 border rounded';
                groupDiv.innerHTML = `
                    <strong>${baseName}</strong>
                    <div class="ms-3">
                        ${files.map(file => `
                            <div class="file-item">
                                <span>
                                    <i class="fas ${getFileIcon(file.name)} file-icon"></i>
                                    ${file.name}
                                </span>
                                <small class="text-muted">${formatFileSize(file.size)}</small>
                            </div>
                        `).join('')}
                    </div>
                `;
                fileList.appendChild(groupDiv);
            });
            
            fileCount.textContent = selectedFiles.length;
            groupCount.textContent = Object.keys(fileGroups).length;
        }

        // 获取文件图标
        function getFileIcon(filename) {
            const ext = filename.toLowerCase().split('.').pop();
            switch (ext) {
                case 'png':
                case 'jpg':
                case 'jpeg':
                case 'gif':
                    return 'fa-image';
                case 'json':
                    return 'fa-file-code';
                default:
                    return 'fa-file';
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 清空文件
        function clearFiles() {
            selectedFiles = [];
            fileGroups = {};
            document.getElementById('fileInput').value = '';
            displayFiles();
            updateSubmitButton();
        }

        // 更新提交按钮状态
        function updateSubmitButton() {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = selectedFiles.length === 0;
        }

        // 分类选择器
        function openCategorySelector() {
            const popup = window.open('/admin/category-selector', 'categorySelector', 
                'width=800,height=600,scrollbars=yes,resizable=yes');
            
            window.addEventListener('message', function(event) {
                if (event.data.type === 'categorySelected') {
                    const category = event.data.category;
                    document.getElementById('defaultCategory').value = category.name;
                    document.getElementById('defaultCategoryId').value = category.id;
                }
            });
        }

        // 表单提交处理
        document.getElementById('batchUploadForm').addEventListener('submit', function(e) {
            if (selectedFiles.length === 0) {
                e.preventDefault();
                alert('请先选择文件');
                return;
            }

            // 显示进度条
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('submitBtn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> 上传中...';
        });

        // 页面加载时设置默认分类
        window.onload = function() {
            document.getElementById('defaultCategory').value = '房屋建筑';
            document.getElementById('defaultCategoryId').value = '1';
        };
    </script>
</body>
</html>
