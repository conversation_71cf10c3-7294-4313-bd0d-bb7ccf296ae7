<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类选择器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .category-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .category-card.selected {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .category-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #007bff, #6610f2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin: 0 auto 15px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">选择资源分类</h2>
                
                <!-- 分类网格 -->
                <div class="row" id="categoryGrid">
                    <!-- 分类卡片将通过JavaScript动态加载 -->
                </div>
                
                <!-- 选中的分类信息 -->
                <div class="mt-4" id="selectedCategory" style="display: none;">
                    <div class="alert alert-info">
                        <h5>已选择分类：</h5>
                        <p id="selectedCategoryInfo"></p>
                        <input type="hidden" id="selectedCategoryId" name="categoryId">
                        <input type="hidden" id="selectedCategoryName" name="categoryName">
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="mt-4">
                    <button type="button" class="btn btn-primary" id="confirmBtn" disabled onclick="confirmSelection()">
                        确认选择
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="window.close()">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedCategory = null;

        // 页面加载时获取分类列表
        document.addEventListener('DOMContentLoaded', function() {
            loadCategories();
        });

        // 加载分类列表
        async function loadCategories() {
            try {
                const response = await fetch('/api/categories');
                const categories = await response.json();
                
                const grid = document.getElementById('categoryGrid');
                grid.innerHTML = '';
                
                categories.forEach(category => {
                    const categoryCard = createCategoryCard(category);
                    grid.appendChild(categoryCard);
                });
            } catch (error) {
                console.error('加载分类失败:', error);
                alert('加载分类失败，请刷新页面重试');
            }
        }

        // 创建分类卡片
        function createCategoryCard(category) {
            const col = document.createElement('div');
            col.className = 'col-md-3 col-sm-4 col-6 mb-3';
            
            col.innerHTML = `
                <div class="card category-card h-100" onclick="selectCategory(${category.id}, '${category.name}', '${category.nameEn}', '${category.description}')">
                    <div class="card-body text-center">
                        <div class="category-icon">
                            ${getIconForCategory(category.nameEn)}
                        </div>
                        <h6 class="card-title">${category.name}</h6>
                        <small class="text-muted">${category.nameEn}</small>
                        <p class="card-text small mt-2">${category.description}</p>
                    </div>
                </div>
            `;
            
            return col;
        }

        // 获取分类图标
        function getIconForCategory(nameEn) {
            const icons = {
                'Houses': '🏠',
                'Treehouses': '🌳',
                'Farmhouses': '🚜',
                'Castles': '🏰',
                'Animals': '🐾',
                'Plants': '🌸',
                'Cartoon': '🎭',
                'Vehicles': '🚗',
                'Food': '🍎',
                'Holidays': '🎉'
            };
            return icons[nameEn] || '📁';
        }

        // 选择分类
        function selectCategory(id, name, nameEn, description) {
            // 移除之前的选中状态
            document.querySelectorAll('.category-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 添加选中状态
            event.currentTarget.classList.add('selected');
            
            // 保存选中的分类
            selectedCategory = { id, name, nameEn, description };
            
            // 显示选中信息
            document.getElementById('selectedCategoryInfo').innerHTML = `
                <strong>${name}</strong> (${nameEn})<br>
                <small>${description}</small>
            `;
            document.getElementById('selectedCategory').style.display = 'block';
            document.getElementById('selectedCategoryId').value = id;
            document.getElementById('selectedCategoryName').value = name;
            
            // 启用确认按钮
            document.getElementById('confirmBtn').disabled = false;
        }

        // 确认选择
        function confirmSelection() {
            if (selectedCategory) {
                // 通过postMessage向父窗口发送选中的分类信息
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'categorySelected',
                        category: selectedCategory
                    }, '*');
                    window.close();
                } else {
                    // 如果不是弹窗，则可以通过其他方式处理
                    alert('分类选择完成：' + selectedCategory.name);
                }
            }
        }
    </script>
</body>
</html>
