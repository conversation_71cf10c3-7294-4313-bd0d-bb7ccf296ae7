<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="layout/base :: layout(~{::title}, ~{::content})">
<head>
    <title>创建版本 - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>创建新版本</h2>
            <a th:href="@{/admin/versions}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回版本列表
            </a>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <!-- 版本创建表单 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-plus"></i>
                            版本信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" th:action="@{/admin/versions/create}">
                            <!-- 版本号 -->
                            <div class="mb-3">
                                <label for="version" class="form-label">版本号 <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control" 
                                       id="version" 
                                       name="version" 
                                       required
                                       pattern="^\d+\.\d+\.\d+$"
                                       placeholder="例如: 1.1.0">
                                <div class="form-text">请使用语义化版本号格式 (主版本.次版本.修订版本)</div>
                            </div>

                            <!-- 版本名称 -->
                            <div class="mb-3">
                                <label for="versionName" class="form-label">版本名称 <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control" 
                                       id="versionName" 
                                       name="versionName" 
                                       required
                                       placeholder="例如: 春季更新">
                                <div class="form-text">给版本起一个有意义的名称</div>
                            </div>

                            <!-- 版本描述 -->
                            <div class="mb-3">
                                <label for="description" class="form-label">版本描述 <span class="text-danger">*</span></label>
                                <textarea class="form-control" 
                                          id="description" 
                                          name="description" 
                                          rows="5"
                                          required
                                          placeholder="请详细描述此版本的更新内容..."></textarea>
                                <div class="form-text">详细描述此版本的新功能、改进和修复</div>
                            </div>

                            <!-- 强制更新 -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="forceUpdate" 
                                           name="forceUpdate" 
                                           value="true">
                                    <label class="form-check-label" for="forceUpdate">
                                        <strong>强制更新</strong>
                                    </label>
                                </div>
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    启用后，客户端必须更新到此版本才能继续使用应用
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="reset" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> 创建版本
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- 版本号规范 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i>
                            版本号规范
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">使用语义化版本号 (Semantic Versioning):</p>
                        <div class="mb-3">
                            <strong>格式: 主版本.次版本.修订版本</strong>
                        </div>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <span class="badge bg-danger">主版本</span>
                                不兼容的API修改
                            </li>
                            <li class="mb-2">
                                <span class="badge bg-warning">次版本</span>
                                向下兼容的功能性新增
                            </li>
                            <li class="mb-2">
                                <span class="badge bg-success">修订版本</span>
                                向下兼容的问题修正
                            </li>
                        </ul>
                        <hr>
                        <p class="mb-0"><strong>示例:</strong></p>
                        <ul class="list-unstyled small text-muted">
                            <li>1.0.0 - 初始版本</li>
                            <li>1.0.1 - 修复bug</li>
                            <li>1.1.0 - 新增功能</li>
                            <li>2.0.0 - 重大更新</li>
                        </ul>
                    </div>
                </div>

                <!-- 更新类型说明 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-download"></i>
                            更新类型
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6 class="text-success">
                                <i class="fas fa-check-circle"></i>
                                可选更新
                            </h6>
                            <p class="small text-muted">
                                用户可以选择是否更新，应用仍可正常使用。适用于新功能添加、性能优化等。
                            </p>
                        </div>
                        <div>
                            <h6 class="text-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                强制更新
                            </h6>
                            <p class="small text-muted">
                                用户必须更新才能继续使用应用。适用于重要安全修复、API变更等。
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 创建提示 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb"></i>
                            创建提示
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                确保版本号唯一
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                详细描述更新内容
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                谨慎使用强制更新
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success"></i>
                                创建后需手动设置为当前版本
                            </li>
                            <li>
                                <i class="fas fa-check text-success"></i>
                                统计信息会自动计算
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const versionInput = document.getElementById('version');
            const versionNameInput = document.getElementById('versionName');
            const descriptionInput = document.getElementById('description');

            // 验证版本号格式
            const versionPattern = /^\d+\.\d+\.\d+$/;
            if (!versionPattern.test(versionInput.value)) {
                alert('版本号格式不正确，请使用 x.y.z 格式（例如: 1.0.0）');
                versionInput.focus();
                e.preventDefault();
                return;
            }

            // 验证版本名称
            if (!versionNameInput.value.trim()) {
                alert('请输入版本名称');
                versionNameInput.focus();
                e.preventDefault();
                return;
            }

            // 验证描述
            if (!descriptionInput.value.trim()) {
                alert('请输入版本描述');
                descriptionInput.focus();
                e.preventDefault();
                return;
            }

            if (descriptionInput.value.trim().length < 10) {
                alert('版本描述至少需要10个字符');
                descriptionInput.focus();
                e.preventDefault();
                return;
            }

            // 强制更新确认
            const forceUpdateCheckbox = document.getElementById('forceUpdate');
            if (forceUpdateCheckbox.checked) {
                if (!confirm('您选择了强制更新，这意味着用户必须更新到此版本才能继续使用应用。\n\n确定要继续吗？')) {
                    e.preventDefault();
                    return;
                }
            }

            // 显示创建进度
            const submitBtn = document.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 创建中...';
            submitBtn.disabled = true;
        });

        // 版本号输入提示
        document.getElementById('version').addEventListener('input', function(e) {
            const value = e.target.value;
            const pattern = /^\d+\.\d+\.\d+$/;
            
            if (value && !pattern.test(value)) {
                e.target.classList.add('is-invalid');
            } else {
                e.target.classList.remove('is-invalid');
            }
        });

        // 自动生成版本描述模板
        document.getElementById('versionName').addEventListener('blur', function(e) {
            const descriptionTextarea = document.getElementById('description');
            if (!descriptionTextarea.value && e.target.value) {
                const template = `${e.target.value}

更新内容：
- 新增功能：
- 功能改进：
- 问题修复：
- 性能优化：

注意事项：
- 
`;
                descriptionTextarea.value = template;
            }
        });
    </script>
</body>
</html>
