<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="layout/base :: layout(~{::title}, ~{::content})">
<head>
    <title>数据导出 - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-download"></i>
                数据导出与备份
            </h2>
        </div>

        <!-- 导出选项 -->
        <div class="row">
            <!-- 资源导出 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-images"></i>
                            资源数据导出
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">导出涂色资源的详细信息，包括名称、分类、难度等。</p>
                        
                        <div class="d-grid gap-2">
                            <a href="/admin/export/resources/json" 
                               class="btn btn-outline-primary"
                               onclick="showDownloadProgress(this)">
                                <i class="fas fa-file-code"></i>
                                导出所有资源 (JSON)
                            </a>
                            
                            <a href="/admin/export/resources/csv" 
                               class="btn btn-outline-success"
                               onclick="showDownloadProgress(this)">
                                <i class="fas fa-file-csv"></i>
                                导出所有资源 (CSV)
                            </a>
                            
                            <button type="button" 
                                    class="btn btn-outline-info"
                                    data-bs-toggle="modal" 
                                    data-bs-target="#categoryExportModal">
                                <i class="fas fa-filter"></i>
                                按分类导出 (JSON)
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 版本导出 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-code-branch"></i>
                            版本数据导出
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">导出应用版本信息，包括版本号、描述、发布时间等。</p>
                        
                        <div class="d-grid gap-2">
                            <a href="/admin/export/versions/json" 
                               class="btn btn-outline-info"
                               onclick="showDownloadProgress(this)">
                                <i class="fas fa-file-code"></i>
                                导出所有版本 (JSON)
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计报告和备份 -->
        <div class="row">
            <!-- 统计报告 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar"></i>
                            统计报告
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">生成详细的资源统计报告，包括分类统计、下载统计等。</p>
                        
                        <div class="d-grid gap-2">
                            <a href="/admin/export/report/json" 
                               class="btn btn-outline-warning"
                               onclick="showDownloadProgress(this)">
                                <i class="fas fa-file-alt"></i>
                                下载统计报告 (JSON)
                            </a>
                            
                            <button type="button" 
                                    class="btn btn-outline-secondary"
                                    onclick="previewReport()">
                                <i class="fas fa-eye"></i>
                                在线预览报告
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据备份 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-database"></i>
                            完整数据备份
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">备份所有数据，包括资源、版本和统计信息。</p>
                        
                        <div class="d-grid gap-2">
                            <a href="/admin/export/backup/json" 
                               class="btn btn-danger"
                               onclick="showDownloadProgress(this)">
                                <i class="fas fa-download"></i>
                                完整备份 (JSON)
                            </a>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                建议定期备份数据以防数据丢失
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导出说明 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i>
                            导出说明
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>文件格式说明</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <span class="badge bg-primary">JSON</span>
                                        结构化数据，适合程序处理和数据迁移
                                    </li>
                                    <li class="mb-2">
                                        <span class="badge bg-success">CSV</span>
                                        表格格式，适合Excel打开和数据分析
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>使用建议</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success"></i>
                                        定期备份重要数据
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success"></i>
                                        导出前确保数据完整性
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success"></i>
                                        妥善保存导出文件
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类导出模态框 -->
    <div class="modal fade" id="categoryExportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">按分类导出资源</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="categorySelect" class="form-label">选择分类</label>
                        <select class="form-select" id="categorySelect">
                            <option value="">请选择分类...</option>
                        </select>
                    </div>
                    <div class="form-text">
                        选择要导出的资源分类，只会导出该分类下的资源。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="exportByCategory()">
                        <i class="fas fa-download"></i> 导出
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 报告预览模态框 -->
    <div class="modal fade" id="reportPreviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">统计报告预览</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="reportContent">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在生成报告...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <a href="/admin/export/report/json" class="btn btn-primary">
                        <i class="fas fa-download"></i> 下载完整报告
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时获取分类列表
        document.addEventListener('DOMContentLoaded', function() {
            loadCategories();
        });

        // 加载分类列表
        function loadCategories() {
            fetch('/api/resources/categories')
                .then(response => response.json())
                .then(categories => {
                    const select = document.getElementById('categorySelect');
                    select.innerHTML = '<option value="">请选择分类...</option>';
                    categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category;
                        option.textContent = category;
                        select.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('加载分类失败:', error);
                });
        }

        // 按分类导出
        function exportByCategory() {
            const category = document.getElementById('categorySelect').value;
            if (!category) {
                alert('请选择要导出的分类');
                return;
            }
            
            const url = '/admin/export/resources/category/' + encodeURIComponent(category) + '/json';
            window.open(url, '_blank');
            
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('categoryExportModal')).hide();
        }

        // 预览报告
        function previewReport() {
            const modal = new bootstrap.Modal(document.getElementById('reportPreviewModal'));
            modal.show();
            
            fetch('/admin/export/report/preview')
                .then(response => response.json())
                .then(data => {
                    displayReport(data);
                })
                .catch(error => {
                    document.getElementById('reportContent').innerHTML = 
                        '<div class="alert alert-danger">加载报告失败: ' + error.message + '</div>';
                });
        }

        // 显示报告内容
        function displayReport(data) {
            const content = document.getElementById('reportContent');
            const summary = data.summary;
            
            content.innerHTML = `
                <div class="row mb-4">
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary">${summary.totalResources}</h4>
                        <small class="text-muted">总资源数</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success">${summary.totalCategories}</h4>
                        <small class="text-muted">分类数量</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning">${summary.totalDownloads}</h4>
                        <small class="text-muted">总下载次数</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-info">${(summary.totalSize / 1024 / 1024).toFixed(2)} MB</h4>
                        <small class="text-muted">总文件大小</small>
                    </div>
                </div>
                
                <h6>分类统计</h6>
                <div class="mb-3">
                    ${Object.entries(data.categoryStats).map(([category, count]) => 
                        `<span class="badge bg-primary me-2 mb-1">${category}: ${count}</span>`
                    ).join('')}
                </div>
                
                <h6>难度统计</h6>
                <div class="mb-3">
                    ${Object.entries(data.difficultyStats).map(([difficulty, count]) => 
                        `<span class="badge bg-warning me-2 mb-1">难度${difficulty}: ${count}</span>`
                    ).join('')}
                </div>
                
                <h6>热门资源 (前5名)</h6>
                <div class="list-group">
                    ${data.popularResources.slice(0, 5).map(resource => 
                        `<div class="list-group-item d-flex justify-content-between align-items-center">
                            <span>${resource.name}</span>
                            <span class="badge bg-primary">${resource.downloadCount} 次下载</span>
                        </div>`
                    ).join('')}
                </div>
            `;
        }

        // 显示下载进度
        function showDownloadProgress(element) {
            const originalText = element.innerHTML;
            element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 准备下载...';
            element.classList.add('disabled');
            
            // 2秒后恢复按钮状态
            setTimeout(() => {
                element.innerHTML = originalText;
                element.classList.remove('disabled');
            }, 2000);
        }
    </script>
</body>
</html>
