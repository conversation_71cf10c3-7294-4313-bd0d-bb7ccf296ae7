server:
  port: 8083
  address: 0.0.0.0  # 允许所有IP访问

spring:
  application:
    name: dreaming-coloring-backend
  
  # 数据库配置
  datasource:
    # H2内存数据库 (开发环境，当前使用)
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password

    # MySQL数据库配置 (生产环境，需要先设置MySQL)
    # url: ***********************************************************************************************************************************************************
    # driver-class-name: com.mysql.cj.jdbc.Driver
    # username: dreaming_user
    # password: Dreaming2024!

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop  # H2开发环境自动创建表
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  # H2控制台配置 (开发环境)
  h2:
    console:
      enabled: true
      path: /h2-console

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      enabled: true

  # Thymeleaf配置
  thymeleaf:
    cache: false  # 开发环境关闭缓存
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html

  # Force UTF-8 encoding for all text output
  web:
    locale: zh_CN
  messages:
    basename: messages
    encoding: UTF-8
    fallback-to-system-locale: false

  # HTTP encoding configuration
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# 自定义配置
app:
  # 文件存储路径
  file-storage:
    upload-dir: ./uploads
    base-url: http://localhost:8083/api/files
  
  # 资源版本配置
  version:
    current: "1.0.0"
    check-interval: 3600  # 版本检查间隔(秒)

# API文档配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method

# Logging configuration (English output)
logging:
  level:
    com.dreaming: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.springframework.boot: INFO
    root: INFO
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/dreaming-coloring.log
  charset:
    console: UTF-8
    file: UTF-8


