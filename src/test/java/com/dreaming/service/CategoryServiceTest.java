package com.dreaming.service;

import com.dreaming.dto.CategoryDTO;
import com.dreaming.entity.Category;
import com.dreaming.repository.CategoryRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 分类服务测试类
 * 测试英文名称优先返回的功能
 */
@ExtendWith(MockitoExtension.class)
class CategoryServiceTest {

    @Mock
    private CategoryRepository categoryRepository;

    @InjectMocks
    private CategoryService categoryService;

    private Category categoryWithEnglishName;
    private Category categoryWithoutEnglishName;

    @BeforeEach
    void setUp() {
        // 创建有英文名称的分类
        categoryWithEnglishName = new Category();
        categoryWithEnglishName.setId(1L);
        categoryWithEnglishName.setName("动物");
        categoryWithEnglishName.setNameEn("animals");
        categoryWithEnglishName.setDescription("动物类涂色资源");
        categoryWithEnglishName.setSortOrder(1);
        categoryWithEnglishName.setIsActive(true);
        categoryWithEnglishName.setCreatedAt(LocalDateTime.now());

        // 创建没有英文名称的分类
        categoryWithoutEnglishName = new Category();
        categoryWithoutEnglishName.setId(2L);
        categoryWithoutEnglishName.setName("其他");
        categoryWithoutEnglishName.setNameEn(null);
        categoryWithoutEnglishName.setDescription("其他类涂色资源");
        categoryWithoutEnglishName.setSortOrder(2);
        categoryWithoutEnglishName.setIsActive(true);
        categoryWithoutEnglishName.setCreatedAt(LocalDateTime.now());
    }

    @Test
    void testGetAllActiveCategories_ShouldReturnEnglishNamesWhenAvailable() {
        // Given
        List<Category> categories = Arrays.asList(categoryWithEnglishName, categoryWithoutEnglishName);
        when(categoryRepository.findByIsActiveTrueOrderBySortOrderAsc()).thenReturn(categories);

        // When
        List<CategoryDTO> result = categoryService.getAllActiveCategories();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 第一个分类应该返回英文名称
        CategoryDTO firstCategory = result.get(0);
        assertEquals("animals", firstCategory.getName());
        assertEquals("animals", firstCategory.getNameEn());
        
        // 第二个分类没有英文名称，应该返回中文名称
        CategoryDTO secondCategory = result.get(1);
        assertEquals("其他", secondCategory.getName());
        assertNull(secondCategory.getNameEn());
        
        verify(categoryRepository, times(1)).findByIsActiveTrueOrderBySortOrderAsc();
    }

    @Test
    void testGetAllActiveCategories_ShouldHandleEmptyEnglishName() {
        // Given
        Category categoryWithEmptyEnglishName = new Category();
        categoryWithEmptyEnglishName.setId(3L);
        categoryWithEmptyEnglishName.setName("测试");
        categoryWithEmptyEnglishName.setNameEn(""); // 空字符串
        categoryWithEmptyEnglishName.setIsActive(true);
        categoryWithEmptyEnglishName.setSortOrder(1);
        categoryWithEmptyEnglishName.setCreatedAt(LocalDateTime.now());

        when(categoryRepository.findByIsActiveTrueOrderBySortOrderAsc())
                .thenReturn(Arrays.asList(categoryWithEmptyEnglishName));

        // When
        List<CategoryDTO> result = categoryService.getAllActiveCategories();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        CategoryDTO category = result.get(0);
        assertEquals("测试", category.getName()); // 应该返回中文名称，因为英文名称为空
        assertEquals("", category.getNameEn());
    }

    @Test
    void testGetAllActiveCategories_ShouldHandleWhitespaceEnglishName() {
        // Given
        Category categoryWithWhitespaceEnglishName = new Category();
        categoryWithWhitespaceEnglishName.setId(4L);
        categoryWithWhitespaceEnglishName.setName("测试2");
        categoryWithWhitespaceEnglishName.setNameEn("   "); // 只有空格
        categoryWithWhitespaceEnglishName.setIsActive(true);
        categoryWithWhitespaceEnglishName.setSortOrder(1);
        categoryWithWhitespaceEnglishName.setCreatedAt(LocalDateTime.now());

        when(categoryRepository.findByIsActiveTrueOrderBySortOrderAsc())
                .thenReturn(Arrays.asList(categoryWithWhitespaceEnglishName));

        // When
        List<CategoryDTO> result = categoryService.getAllActiveCategories();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        CategoryDTO category = result.get(0);
        assertEquals("测试2", category.getName()); // 应该返回中文名称，因为英文名称只有空格
        assertEquals("   ", category.getNameEn());
    }
}
