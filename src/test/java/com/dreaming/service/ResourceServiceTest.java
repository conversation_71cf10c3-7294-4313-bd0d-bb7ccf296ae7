package com.dreaming.service;

import com.dreaming.dto.ResourceDTO;
import com.dreaming.entity.Category;
import com.dreaming.entity.ColoringResource;
import com.dreaming.repository.CategoryRepository;
import com.dreaming.repository.ColoringResourceRepository;
import com.dreaming.service.impl.ResourceServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 资源服务测试类
 * 测试英文分类名称相关功能
 */
@ExtendWith(MockitoExtension.class)
class ResourceServiceTest {

    @Mock
    private ColoringResourceRepository resourceRepository;

    @Mock
    private CategoryRepository categoryRepository;

    @Mock
    private FileStorageService fileStorageService;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private ResourceServiceImpl resourceService;

    private Category animalsCategory;
    private ColoringResource resource1;
    private ColoringResource resource2;

    @BeforeEach
    void setUp() {
        // 创建动物分类
        animalsCategory = new Category();
        animalsCategory.setId(1L);
        animalsCategory.setName("动物");
        animalsCategory.setNameEn("animals");
        animalsCategory.setIsActive(true);
        animalsCategory.setSortOrder(1);
        animalsCategory.setCreatedAt(LocalDateTime.now());

        // 创建资源1
        resource1 = new ColoringResource();
        resource1.setId(1L);
        resource1.setName("小猫涂色");
        resource1.setCategory("动物");
        resource1.setCategoryId(1L);
        resource1.setDifficulty(2);
        resource1.setActive(true);
        resource1.setCreatedAt(LocalDateTime.now());

        // 创建资源2
        resource2 = new ColoringResource();
        resource2.setId(2L);
        resource2.setName("小狗涂色");
        resource2.setCategory("动物");
        resource2.setCategoryId(1L);
        resource2.setDifficulty(3);
        resource2.setActive(true);
        resource2.setCreatedAt(LocalDateTime.now());
    }

    @Test
    void testGetResourcesByEnglishCategory_ShouldFindResourcesByEnglishName() {
        // Given
        String englishCategoryName = "animals";
        List<ColoringResource> expectedResources = Arrays.asList(resource1, resource2);
        
        when(categoryRepository.findByNameEn(englishCategoryName)).thenReturn(animalsCategory);
        when(resourceRepository.findByCategoryIdAndActiveTrue(1L)).thenReturn(expectedResources);

        // When
        List<ResourceDTO> result = resourceService.getResourcesByEnglishCategory(englishCategoryName);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        verify(categoryRepository, times(1)).findByNameEn(englishCategoryName);
        verify(resourceRepository, times(1)).findByCategoryIdAndActiveTrue(1L);
        verify(resourceRepository, never()).findByCategoryAndActiveTrue(anyString());
    }

    @Test
    void testGetResourcesByEnglishCategory_ShouldFallbackToStringMatch() {
        // Given
        String categoryName = "unknown_category";
        List<ColoringResource> expectedResources = Arrays.asList(resource1);
        
        when(categoryRepository.findByNameEn(categoryName)).thenReturn(null);
        when(resourceRepository.findByCategoryAndActiveTrue(categoryName)).thenReturn(expectedResources);

        // When
        List<ResourceDTO> result = resourceService.getResourcesByEnglishCategory(categoryName);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        verify(categoryRepository, times(1)).findByNameEn(categoryName);
        verify(resourceRepository, times(1)).findByCategoryAndActiveTrue(categoryName);
        verify(resourceRepository, never()).findByCategoryIdAndActiveTrue(anyLong());
    }

    @Test
    void testGetAllCategories_ShouldReturnEnglishNames() {
        // Given
        Category category1 = new Category();
        category1.setName("动物");
        category1.setNameEn("animals");
        category1.setIsActive(true);
        category1.setSortOrder(1);

        Category category2 = new Category();
        category2.setName("植物");
        category2.setNameEn("plants");
        category2.setIsActive(true);
        category2.setSortOrder(2);

        Category category3 = new Category();
        category3.setName("其他");
        category3.setNameEn(null); // 没有英文名称
        category3.setIsActive(true);
        category3.setSortOrder(3);

        List<Category> categories = Arrays.asList(category1, category2, category3);
        when(categoryRepository.findByIsActiveTrueOrderBySortOrderAsc()).thenReturn(categories);

        // When
        List<String> result = resourceService.getAllCategories();

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("animals", result.get(0)); // 应该返回英文名称
        assertEquals("plants", result.get(1));  // 应该返回英文名称
        assertEquals("其他", result.get(2));     // 没有英文名称，返回中文名称
        
        verify(categoryRepository, times(1)).findByIsActiveTrueOrderBySortOrderAsc();
    }

    @Test
    void testGetResourcesByCategoryId_ShouldReturnCorrectResources() {
        // Given
        Long categoryId = 1L;
        List<ColoringResource> expectedResources = Arrays.asList(resource1, resource2);
        
        when(resourceRepository.findByCategoryIdAndActiveTrue(categoryId)).thenReturn(expectedResources);

        // When
        List<ResourceDTO> result = resourceService.getResourcesByCategoryId(categoryId);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        verify(resourceRepository, times(1)).findByCategoryIdAndActiveTrue(categoryId);
    }
}
