package com.dreaming.controller;

import com.dreaming.dto.ResourceDTO;
import com.dreaming.service.ResourceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 资源管理控制器
 * 提供涂色资源的CRUD操作API
 */
@RestController
@RequestMapping("/api/resources")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "资源管理", description = "涂色资源的增删改查接口")
public class ResourceController {

    private final ResourceService resourceService;

    @Operation(summary = "上传资源", description = "上传新的涂色资源文件")
    @PostMapping("/upload")
    public ResponseEntity<ResourceDTO> uploadResource(
            @Parameter(description = "资源文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "资源名称") @RequestParam("name") String name,
            @Parameter(description = "资源描述") @RequestParam(value = "description", required = false) String description,
            @Parameter(description = "资源分类") @RequestParam("category") String category,
            @Parameter(description = "难度等级(1-5)") @RequestParam("difficulty") Integer difficulty,
            @Parameter(description = "版本号") @RequestParam("version") String version) {
        
        log.info("接收到资源上传请求: name={}, category={}, difficulty={}", name, category, difficulty);
        
        ResourceDTO resource = resourceService.uploadResource(file, name, description, category, difficulty, version);
        return ResponseEntity.ok(resource);
    }

    @Operation(summary = "获取所有激活资源", description = "获取所有激活状态的涂色资源")
    @GetMapping
    public ResponseEntity<List<ResourceDTO>> getAllResources() {
        List<ResourceDTO> resources = resourceService.getAllActiveResources();
        return ResponseEntity.ok(resources);
    }

    @Operation(summary = "分页获取资源", description = "分页获取涂色资源列表")
    @GetMapping("/page")
    public ResponseEntity<Page<ResourceDTO>> getResourcesPage(
            @Parameter(description = "页码(从0开始)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<ResourceDTO> resourcePage = resourceService.getResourcesPage(pageable);
        return ResponseEntity.ok(resourcePage);
    }

    @Operation(summary = "根据ID获取资源", description = "根据资源ID获取单个资源详情")
    @GetMapping("/{id}")
    public ResponseEntity<ResourceDTO> getResourceById(
            @Parameter(description = "资源ID") @PathVariable Long id) {
        
        return resourceService.getResourceById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "根据分类获取资源", description = "根据分类名称获取涂色资源列表（支持英文和中文分类名）")
    @GetMapping("/category/{category}")
    public ResponseEntity<List<ResourceDTO>> getResourcesByCategory(
            @Parameter(description = "资源分类（英文或中文名称）") @PathVariable String category) {

        List<ResourceDTO> resources = resourceService.getResourcesByCategory(category);
        return ResponseEntity.ok(resources);
    }

    @Operation(summary = "根据分类ID获取资源", description = "根据分类ID获取涂色资源列表")
    @GetMapping("/category-id/{categoryId}")
    public ResponseEntity<List<ResourceDTO>> getResourcesByCategoryId(
            @Parameter(description = "分类ID") @PathVariable Long categoryId) {

        List<ResourceDTO> resources = resourceService.getResourcesByCategoryId(categoryId);
        return ResponseEntity.ok(resources);
    }

    @Operation(summary = "根据英文分类名获取资源", description = "根据英文分类名获取涂色资源列表，支持分页")
    @GetMapping("/category/en/{categoryNameEn}")
    public ResponseEntity<List<ResourceDTO>> getResourcesByEnglishCategory(
            @Parameter(description = "英文分类名称") @PathVariable String categoryNameEn) {

        List<ResourceDTO> resources = resourceService.getResourcesByEnglishCategory(categoryNameEn);
        return ResponseEntity.ok(resources);
    }

    @Operation(summary = "根据难度获取资源", description = "根据难度等级获取涂色资源列表")
    @GetMapping("/difficulty/{difficulty}")
    public ResponseEntity<List<ResourceDTO>> getResourcesByDifficulty(
            @Parameter(description = "难度等级(1-5)") @PathVariable Integer difficulty) {
        
        List<ResourceDTO> resources = resourceService.getResourcesByDifficulty(difficulty);
        return ResponseEntity.ok(resources);
    }

    @Operation(summary = "搜索资源", description = "根据关键词搜索涂色资源")
    @GetMapping("/search")
    public ResponseEntity<List<ResourceDTO>> searchResources(
            @Parameter(description = "搜索关键词") @RequestParam String keyword) {
        
        List<ResourceDTO> resources = resourceService.searchResources(keyword);
        return ResponseEntity.ok(resources);
    }

    @Operation(summary = "获取热门资源", description = "获取下载次数最多的热门资源")
    @GetMapping("/popular")
    public ResponseEntity<List<ResourceDTO>> getPopularResources(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<ResourceDTO> resources = resourceService.getPopularResources(limit);
        return ResponseEntity.ok(resources);
    }

    @Operation(summary = "获取最新资源", description = "获取最新上传的涂色资源")
    @GetMapping("/latest")
    public ResponseEntity<List<ResourceDTO>> getLatestResources(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<ResourceDTO> resources = resourceService.getLatestResources(limit);
        return ResponseEntity.ok(resources);
    }

    @Operation(summary = "获取所有分类", description = "获取所有资源分类列表（优先返回英文名称）")
    @GetMapping("/categories")
    public ResponseEntity<List<String>> getAllCategories() {
        List<String> categories = resourceService.getAllCategories();
        return ResponseEntity.ok(categories);
    }

    @Operation(summary = "更新资源信息", description = "更新指定资源的基本信息")
    @PutMapping("/{id}")
    public ResponseEntity<ResourceDTO> updateResource(
            @Parameter(description = "资源ID") @PathVariable Long id,
            @RequestBody Map<String, Object> updateData) {
        
        String name = (String) updateData.get("name");
        String description = (String) updateData.get("description");
        String category = (String) updateData.get("category");
        Integer difficulty = (Integer) updateData.get("difficulty");
        
        ResourceDTO resource = resourceService.updateResource(id, name, description, category, difficulty);
        return ResponseEntity.ok(resource);
    }

    @Operation(summary = "删除资源", description = "删除指定的涂色资源")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteResource(
            @Parameter(description = "资源ID") @PathVariable Long id) {
        
        resourceService.deleteResource(id);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "切换资源状态", description = "激活或停用指定资源")
    @PatchMapping("/{id}/toggle")
    public ResponseEntity<Void> toggleResourceStatus(
            @Parameter(description = "资源ID") @PathVariable Long id) {
        
        resourceService.toggleResourceStatus(id);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "增加下载次数", description = "记录资源下载，增加下载计数")
    @PostMapping("/{id}/download")
    public ResponseEntity<Void> recordDownload(
            @Parameter(description = "资源ID") @PathVariable Long id) {
        
        resourceService.incrementDownloadCount(id);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "获取资源统计", description = "获取资源总数等统计信息")
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getResourceStats() {
        long totalCount = resourceService.getTotalResourceCount();
        List<String> categories = resourceService.getAllCategories();
        
        Map<String, Object> stats = Map.of(
                "totalCount", totalCount,
                "categoryCount", categories.size(),
                "categories", categories
        );
        
        return ResponseEntity.ok(stats);
    }
}
