package com.dreaming.controller;

import com.dreaming.entity.Category;
import com.dreaming.entity.ColoringResource;
import com.dreaming.repository.CategoryRepository;
import com.dreaming.repository.ColoringResourceRepository;
import com.dreaming.service.TestDataGeneratorService;
import com.dreaming.service.UploadedFileProcessorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMethod;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 测试数据控制器
 * 用于创建测试数据
 */
@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "测试数据", description = "创建和管理测试数据")
public class TestDataController {

    private final ColoringResourceRepository resourceRepository;
    private final CategoryRepository categoryRepository;
    private final TestDataGeneratorService testDataGeneratorService;
    private final UploadedFileProcessorService uploadedFileProcessorService;

    /**
     * 基于现有文件创建测试数据
     */
    @Operation(summary = "基于文件创建测试数据", description = "基于uploads/test目录中的文件创建测试资源")
    @RequestMapping(value = "/create-data-from-files", method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<String> createTestDataFromFiles() {
        try {
            log.info("开始基于现有文件创建测试数据...");
            String result = testDataGeneratorService.generateTestData();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("基于文件创建测试数据失败", e);
            return ResponseEntity.ok("基于文件创建测试数据失败: " + e.getMessage());
        }
    }

    /**
     * 创建测试数据（原方法）
     */
    @Operation(summary = "创建测试数据", description = "为每个分类创建测试资源")
    @RequestMapping(value = "/create-data", method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<String> createTestData() {
        try {
            log.info("开始创建测试数据...");
            
            List<Category> categories = categoryRepository.findAll();
            Random random = new Random();
            int totalCreated = 0;

            // 为每个分类创建2-3个测试资源
            for (Category category : categories) {
                int resourceCount = 2 + random.nextInt(2); // 2-3个资源
                
                for (int i = 1; i <= resourceCount; i++) {
                    ColoringResource resource = createTestResource(category, i, random);
                    resourceRepository.save(resource);
                    totalCreated++;
                    log.info("创建测试资源: {} - {}", category.getName(), resource.getName());
                }
            }
            
            log.info("测试数据创建完成！总共创建了 {} 个资源", totalCreated);
            return ResponseEntity.ok("成功创建 " + totalCreated + " 个测试资源");
            
        } catch (Exception e) {
            log.error("创建测试数据失败", e);
            return ResponseEntity.ok("创建测试数据失败: " + e.getMessage());
        }
    }

    /**
     * 处理已上传的文件
     */
    @Operation(summary = "处理已上传文件", description = "处理uploads目录中已存在但未创建数据库记录的文件")
    @RequestMapping(value = "/process-uploaded-files", method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<String> processUploadedFiles() {
        try {
            log.info("开始处理已上传的文件...");
            String result = uploadedFileProcessorService.processUploadedFiles();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("处理已上传文件失败", e);
            return ResponseEntity.ok("处理已上传文件失败: " + e.getMessage());
        }
    }

    /**
     * 清空测试数据
     */
    @Operation(summary = "清空测试数据", description = "删除所有测试资源")
    @DeleteMapping("/clear-data")
    public ResponseEntity<String> clearTestData() {
        try {
            long count = resourceRepository.count();
            resourceRepository.deleteAll();
            log.info("已清空 {} 个资源", count);
            return ResponseEntity.ok("成功清空 " + count + " 个资源");
        } catch (Exception e) {
            log.error("清空测试数据失败", e);
            return ResponseEntity.ok("清空测试数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据统计
     */
    @Operation(summary = "获取数据统计", description = "获取当前数据库中的数据统计")
    @GetMapping("/stats")
    public ResponseEntity<String> getStats() {
        try {
            long categoryCount = categoryRepository.count();
            long resourceCount = resourceRepository.count();

            String stats = String.format(
                "数据统计:\n分类数量: %d\n资源数量: %d",
                categoryCount, resourceCount
            );

            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            return ResponseEntity.ok("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 列出上传的文件
     */
    @Operation(summary = "列出上传文件", description = "列出uploads目录中的所有文件")
    @GetMapping("/list-uploads")
    public ResponseEntity<List<String>> listUploads() {
        try {
            Path uploadsDir = Paths.get("uploads");
            if (!Files.exists(uploadsDir)) {
                return ResponseEntity.ok(new ArrayList<>());
            }

            List<String> files = new ArrayList<>();
            Files.walk(uploadsDir)
                    .filter(Files::isRegularFile)
                    .forEach(path -> {
                        String relativePath = uploadsDir.relativize(path).toString().replace("\\", "/");
                        files.add(relativePath);
                    });

            files.sort(String::compareTo);
            return ResponseEntity.ok(files);
        } catch (Exception e) {
            log.error("列出上传文件失败", e);
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    /**
     * 清理重复资源
     */
    @Operation(summary = "清理重复资源", description = "删除重复的资源，保留最新的")
    @RequestMapping(value = "/cleanup-duplicates", method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<String> cleanupDuplicates() {
        try {
            log.info("开始清理重复资源...");

            List<ColoringResource> allResources = resourceRepository.findAll();
            Map<String, List<ColoringResource>> resourcesByName = new HashMap<>();

            // 按名称分组
            for (ColoringResource resource : allResources) {
                resourcesByName.computeIfAbsent(resource.getName(), k -> new ArrayList<>()).add(resource);
            }

            int deletedCount = 0;

            // 处理重复资源
            for (Map.Entry<String, List<ColoringResource>> entry : resourcesByName.entrySet()) {
                List<ColoringResource> resources = entry.getValue();
                if (resources.size() > 1) {
                    // 按创建时间排序，保留最新的
                    resources.sort((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()));

                    // 删除除第一个（最新）之外的所有资源
                    for (int i = 1; i < resources.size(); i++) {
                        ColoringResource toDelete = resources.get(i);
                        log.info("删除重复资源: {} (ID: {})", toDelete.getName(), toDelete.getId());
                        resourceRepository.delete(toDelete);
                        deletedCount++;
                    }
                }
            }

            log.info("重复资源清理完成！删除了 {} 个重复资源", deletedCount);
            return ResponseEntity.ok("成功清理 " + deletedCount + " 个重复资源");

        } catch (Exception e) {
            log.error("清理重复资源失败", e);
            return ResponseEntity.ok("清理重复资源失败: " + e.getMessage());
        }
    }

    private ColoringResource createTestResource(Category category, int index, Random random) {
        ColoringResource resource = new ColoringResource();
        
        // 基础信息
        resource.setName(generateResourceName(category.getName(), index));
        resource.setDescription(generateResourceDescription(category.getName(), index));
        resource.setCategory(category.getName());
        resource.setCategoryId(category.getId());
        
        // 难度和时间
        resource.setDifficulty(1 + random.nextInt(5)); // 1-5
        resource.setEstimatedTime(10 + random.nextInt(50)); // 10-60分钟
        
        // 区域和颜色
        resource.setRegionsCount(5 + random.nextInt(20)); // 5-25个区域
        resource.setColorsCount(3 + random.nextInt(10)); // 3-12种颜色
        
        // 文件信息
        resource.setOriginalFilename(generateFilename(category.getNameEn(), index));
        resource.setStoredFilename(generateFilename(category.getNameEn(), index));
        resource.setFilePath("/uploads/" + generateFilename(category.getNameEn(), index));
        resource.setFileSize(50000L + random.nextInt(200000)); // 50KB-250KB
        resource.setContentType("image/png");
        
        // 统计信息
        resource.setDownloadCount((long) random.nextInt(1000));
        resource.setCompletionCount((long) random.nextInt(500));
        resource.setRating(3.0 + random.nextDouble() * 2.0); // 3.0-5.0
        resource.setRatingCount(random.nextInt(100));
        
        // 状态
        resource.setActive(true);
        resource.setIsPremium(random.nextBoolean() && random.nextDouble() < 0.3); // 30%概率为付费
        resource.setIsFeatured(random.nextBoolean() && random.nextDouble() < 0.2); // 20%概率为精选
        resource.setStatus("ACTIVE");
        
        // 时间信息
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime createdTime = now.minusDays(random.nextInt(30)); // 最近30天内创建
        resource.setCreatedAt(createdTime);
        resource.setUpdatedAt(createdTime);
        resource.setCreatedBy("system");
        resource.setUpdatedBy("system");
        
        // 版本
        resource.setVersion("1.0.0");
        
        // 额外信息
        resource.setAgeGroup(generateAgeGroup(resource.getDifficulty()));
        resource.setTheme(category.getName());
        resource.setTags(generateTags(category.getName()));
        
        // URLs
        resource.setColoringDataUrl("/api/files/" + generateJsonFilename(category.getNameEn(), index));
        resource.setPreviewImageUrl("/api/files/preview_" + generateFilename(category.getNameEn(), index));
        
        return resource;
    }

    private String generateResourceName(String categoryName, int index) {
        String[] prefixes = {"可爱的", "美丽的", "精致的", "有趣的", "梦幻的", "温馨的", "神奇的", "迷人的"};
        String[] suffixes = {"图案", "设计", "作品", "画作", "图画", "插画"};
        
        Random random = new Random();
        String prefix = prefixes[random.nextInt(prefixes.length)];
        String suffix = suffixes[random.nextInt(suffixes.length)];
        
        return prefix + categoryName + suffix + " " + index;
    }

    private String generateResourceDescription(String categoryName, int index) {
        return String.format("这是一个精美的%s主题涂色作品，适合各个年龄段的用户。通过涂色可以放松心情，培养专注力和创造力。", categoryName);
    }

    private String generateFilename(String categoryEn, int index) {
        return categoryEn.toLowerCase() + "_" + index + ".png";
    }

    private String generateJsonFilename(String categoryEn, int index) {
        return categoryEn.toLowerCase() + "_" + index + "_data.json";
    }

    private String generateAgeGroup(int difficulty) {
        switch (difficulty) {
            case 1: return "3-6岁";
            case 2: return "5-8岁";
            case 3: return "7-12岁";
            case 4: return "10-16岁";
            case 5: return "成人";
            default: return "全年龄";
        }
    }

    private String generateTags(String categoryName) {
        // 简化的标签生成，实际应该根据分类生成相关标签
        return String.format("[\"%s\", \"涂色\", \"创意\", \"放松\"]", categoryName);
    }
}
