package com.dreaming.controller;

import com.dreaming.dto.CategoryDTO;
import com.dreaming.service.CategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 分类管理控制器
 */
@RestController
@RequestMapping("/api/categories")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "分类管理", description = "涂色资源分类相关接口")
public class CategoryController {

    private final CategoryService categoryService;

    /**
     * 获取所有激活分类
     */
    @GetMapping
    @Operation(summary = "获取所有分类", description = "获取所有激活状态的分类列表，按排序顺序返回（优先使用英文名称）")
    public ResponseEntity<List<CategoryDTO>> getAllCategories() {
        log.info("API调用: 获取所有分类");

        List<CategoryDTO> categories = categoryService.getAllActiveCategories();

        log.info("返回 {} 个分类", categories.size());
        return ResponseEntity.ok(categories);
    }

    /**
     * 获取分类及资源数量
     */
    @GetMapping("/with-count")
    @Operation(summary = "获取分类及资源数量", description = "获取所有分类及其包含的资源数量")
    public ResponseEntity<List<CategoryDTO>> getCategoriesWithCount() {
        log.info("API调用: 获取分类及资源数量");
        
        List<CategoryDTO> categories = categoryService.getCategoriesWithResourceCount();
        
        log.info("返回 {} 个分类（含资源数量）", categories.size());
        return ResponseEntity.ok(categories);
    }

    /**
     * 根据ID获取分类
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取单个分类", description = "根据分类ID获取分类详情")
    public ResponseEntity<CategoryDTO> getCategoryById(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("API调用: 获取分类详情, ID: {}", id);
        
        return categoryService.getCategoryById(id)
                .map(category -> {
                    log.info("找到分类: {}", category.getName());
                    return ResponseEntity.ok(category);
                })
                .orElseGet(() -> {
                    log.warn("分类不存在: {}", id);
                    return ResponseEntity.notFound().build();
                });
    }

    /**
     * 创建分类
     */
    @PostMapping
    @Operation(summary = "创建分类", description = "创建新的资源分类")
    public ResponseEntity<CategoryDTO> createCategory(
            @Valid @RequestBody CategoryDTO categoryDTO) {
        log.info("API调用: 创建分类, 名称: {}", categoryDTO.getName());
        
        try {
            CategoryDTO createdCategory = categoryService.createCategory(categoryDTO);
            log.info("分类创建成功: {}", createdCategory.getId());
            return ResponseEntity.ok(createdCategory);
        } catch (Exception e) {
            log.error("创建分类失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 更新分类
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新分类", description = "更新指定分类的信息")
    public ResponseEntity<CategoryDTO> updateCategory(
            @Parameter(description = "分类ID") @PathVariable Long id,
            @Valid @RequestBody CategoryDTO categoryDTO) {
        log.info("API调用: 更新分类, ID: {}", id);
        
        return categoryService.updateCategory(id, categoryDTO)
                .map(updatedCategory -> {
                    log.info("分类更新成功: {}", id);
                    return ResponseEntity.ok(updatedCategory);
                })
                .orElseGet(() -> {
                    log.warn("分类不存在，无法更新: {}", id);
                    return ResponseEntity.notFound().build();
                });
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除分类", description = "软删除指定分类（设置为非激活状态）")
    public ResponseEntity<Void> deleteCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("API调用: 删除分类, ID: {}", id);
        
        boolean deleted = categoryService.deleteCategory(id);
        if (deleted) {
            log.info("分类删除成功: {}", id);
            return ResponseEntity.ok().build();
        } else {
            log.warn("分类不存在，无法删除: {}", id);
            return ResponseEntity.notFound().build();
        }
    }
}
