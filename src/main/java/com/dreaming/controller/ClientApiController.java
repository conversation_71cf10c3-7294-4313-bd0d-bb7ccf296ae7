package com.dreaming.controller;

import com.dreaming.dto.*;
import com.dreaming.service.ClientApiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 客户端API控制器
 * 为Android客户端提供所有必要的API接口
 */
@RestController
@RequestMapping("/api/client")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "客户端API", description = "为Android客户端提供的API接口")
public class ClientApiController {

    private final ClientApiService clientApiService;

    /**
     * 获取项目列表
     */
    @Operation(summary = "获取项目列表", description = "获取分页的项目列表，支持分类筛选和搜索")
    @GetMapping("/projects")
    public ResponseEntity<ApiResponse<ProjectListResponse>> getProjects(
            @Parameter(description = "页码，从1开始") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size,
            @Parameter(description = "分类筛选") @RequestParam(required = false) String category,
            @Parameter(description = "难度筛选") @RequestParam(required = false) String difficulty,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String search,
            @Parameter(description = "排序方式") @RequestParam(defaultValue = "latest") String sort) {
        
        log.info("获取项目列表: page={}, size={}, category={}, difficulty={}, search={}, sort={}", 
                page, size, category, difficulty, search, sort);
        
        try {
            ProjectListResponse response = clientApiService.getProjects(page, size, category, difficulty, search, sort);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("获取项目列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取项目列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取项目详情
     */
    @Operation(summary = "获取项目详情", description = "根据项目ID获取详细信息")
    @GetMapping("/projects/{id}")
    public ResponseEntity<ApiResponse<ProjectDTO>> getProjectDetail(
            @Parameter(description = "项目ID") @PathVariable String id) {
        
        log.info("获取项目详情: id={}", id);
        
        try {
            ProjectDTO project = clientApiService.getProjectDetail(id);
            if (project != null) {
                return ResponseEntity.ok(ApiResponse.success(project));
            } else {
                return ResponseEntity.ok(ApiResponse.error("PROJECT_NOT_FOUND", "项目不存在"));
            }
        } catch (Exception e) {
            log.error("获取项目详情失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error("获取项目详情失败: " + e.getMessage()));
        }
    }

    /**
     * 获取分类列表
     */
    @Operation(summary = "获取分类列表", description = "获取所有分类及其项目数量")
    @GetMapping("/categories")
    public ResponseEntity<ApiResponse<CategoriesResponse>> getCategories() {
        log.info("获取分类列表");
        
        try {
            CategoriesResponse response = clientApiService.getCategories();
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("获取分类列表失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取分类列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取每日推荐
     */
    @Operation(summary = "获取每日推荐", description = "获取今日挑战、精选项目和热门项目")
    @GetMapping("/daily")
    public ResponseEntity<ApiResponse<DailyResponse>> getDailyRecommendations() {
        log.info("获取每日推荐");
        
        try {
            DailyResponse response = clientApiService.getDailyRecommendations();
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("获取每日推荐失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取每日推荐失败: " + e.getMessage()));
        }
    }

    /**
     * 检查更新
     */
    @Operation(summary = "检查更新", description = "检查应用和内容更新")
    @GetMapping("/updates")
    public ResponseEntity<ApiResponse<UpdateResponse>> checkUpdates(
            @Parameter(description = "当前应用版本") @RequestParam String currentVersion,
            @Parameter(description = "最后内容更新时间") @RequestParam(required = false) String lastContentUpdate) {
        
        log.info("检查更新: currentVersion={}, lastContentUpdate={}", currentVersion, lastContentUpdate);
        
        try {
            UpdateResponse response = clientApiService.checkUpdates(currentVersion, lastContentUpdate);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("检查更新失败", e);
            return ResponseEntity.ok(ApiResponse.error("检查更新失败: " + e.getMessage()));
        }
    }

    /**
     * 记录下载
     */
    @Operation(summary = "记录下载", description = "记录项目下载统计")
    @PostMapping("/projects/{id}/download")
    public ResponseEntity<ApiResponse<String>> recordDownload(
            @Parameter(description = "项目ID") @PathVariable String id) {
        
        log.info("记录下载: id={}", id);
        
        try {
            clientApiService.recordDownload(id);
            return ResponseEntity.ok(ApiResponse.success("下载记录成功"));
        } catch (Exception e) {
            log.error("记录下载失败: id={}", id, e);
            return ResponseEntity.ok(ApiResponse.error("记录下载失败: " + e.getMessage()));
        }
    }

    /**
     * 健康检查
     */
    @Operation(summary = "健康检查", description = "检查API服务状态")
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<String>> health() {
        return ResponseEntity.ok(ApiResponse.success("API服务正常"));
    }
}
