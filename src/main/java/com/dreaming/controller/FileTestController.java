package com.dreaming.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 文件测试控制器
 * 用于测试路径映射
 */
@RestController
@RequestMapping("/api/file-test")
@Slf4j
public class FileTestController {

    @GetMapping("/{path1}/{path2}/{path3}/{filename:.+}")
    public ResponseEntity<String> testPath(
            @PathVariable String path1,
            @PathVariable String path2,
            @PathVariable String path3,
            @PathVariable String filename) {
        
        log.info("收到文件测试请求: {}/{}/{}/{}", path1, path2, path3, filename);
        
        String response = String.format("路径测试成功: %s/%s/%s/%s", path1, path2, path3, filename);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/simple/{filename:.+}")
    public ResponseEntity<String> testSimple(@PathVariable String filename) {
        log.info("收到简单文件测试请求: {}", filename);
        return ResponseEntity.ok("简单路径测试成功: " + filename);
    }
}
