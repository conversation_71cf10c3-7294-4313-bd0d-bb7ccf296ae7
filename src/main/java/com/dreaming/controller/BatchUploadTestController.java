package com.dreaming.controller;

import com.dreaming.service.ResourceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 批量上传测试控制器
 */
@RestController
@RequestMapping("/api/test/batch-upload")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "批量上传测试", description = "测试批量上传功能")
public class BatchUploadTestController {

    private final ResourceService resourceService;

    /**
     * 测试批量上传功能
     */
    @Operation(summary = "测试批量上传", description = "测试批量上传资源功能")
    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> testBatchUpload(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam(value = "autoCategory", defaultValue = "true") boolean autoCategory,
            @RequestParam(value = "defaultCategoryId", required = false) Long defaultCategoryId,
            @RequestParam(value = "defaultCategory", defaultValue = "测试分类") String defaultCategory,
            @RequestParam(value = "difficulty", defaultValue = "3") Integer difficulty,
            @RequestParam(value = "version", defaultValue = "1.0.0") String version) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始测试批量上传: {} 个文件", files.length);
            
            // 记录文件信息
            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                log.info("文件 {}: {} (大小: {} bytes, 类型: {})", 
                    i + 1, file.getOriginalFilename(), file.getSize(), file.getContentType());
            }
            
            // 执行批量上传
            int successCount = resourceService.batchUploadResources(
                files, autoCategory, defaultCategoryId, defaultCategory, difficulty, version);
            
            result.put("status", "success");
            result.put("message", "批量上传测试完成");
            result.put("totalFiles", files.length);
            result.put("successCount", successCount);
            result.put("failedCount", files.length - successCount);
            
            log.info("批量上传测试完成: 成功 {} 个，失败 {} 个", successCount, files.length - successCount);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("批量上传测试失败", e);
            
            result.put("status", "error");
            result.put("message", "批量上传测试失败: " + e.getMessage());
            result.put("totalFiles", files.length);
            result.put("successCount", 0);
            result.put("error", e.getClass().getSimpleName());
            
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 检查已上传的文件
     */
    @Operation(summary = "检查上传文件", description = "检查uploads目录中的文件")
    @GetMapping("/check-files")
    public ResponseEntity<Map<String, Object>> checkUploadedFiles() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里可以添加检查uploads目录的逻辑
            result.put("status", "success");
            result.put("message", "文件检查功能待实现");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("检查上传文件失败", e);
            
            result.put("status", "error");
            result.put("message", "检查失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 清理测试数据
     */
    @Operation(summary = "清理测试数据", description = "清理批量上传的测试数据")
    @DeleteMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupTestData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里可以添加清理逻辑
            result.put("status", "success");
            result.put("message", "清理功能待实现");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("清理测试数据失败", e);
            
            result.put("status", "error");
            result.put("message", "清理失败: " + e.getMessage());
            
            return ResponseEntity.ok(result);
        }
    }
}
