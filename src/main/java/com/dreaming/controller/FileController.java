package com.dreaming.controller;

import com.dreaming.service.FileStorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件下载控制器
 * 提供文件下载服务
 */
@RestController
@RequestMapping("/api/files")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "文件服务", description = "文件下载和访问接口")
public class FileController {

    private final FileStorageService fileStorageService;

    @Operation(summary = "下载文件", description = "根据文件名下载文件")
    @GetMapping("/**")
    public ResponseEntity<Resource> downloadFile(HttpServletRequest request) {

        try {
            // 从请求URI中提取文件路径
            String requestURI = request.getRequestURI();
            String filename = requestURI.substring("/api/files/".length());

            log.info("File download request: {}", filename);
            
            // 检查文件是否存在
            if (!fileStorageService.fileExists(filename)) {
                log.warn("File not found, returning placeholder: {}", filename);
                return getPlaceholderResponse(filename);
            }
            
            // 获取文件路径
            String filePath = fileStorageService.getFilePath(filename);
            Path path = Paths.get(filePath);
            Resource resource = new UrlResource(path.toUri());
            
            if (!resource.exists() || !resource.isReadable()) {
                log.error("文件不可读: {}", filename);
                return ResponseEntity.notFound().build();
            }
            
            // 确定内容类型
            String contentType = determineContentType(filename);
            
            log.info("File download successful: {}", filename);
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + getOriginalFilename(filename) + "\"")
                    .body(resource);
                    
        } catch (MalformedURLException e) {
            String requestURI = request.getRequestURI();
            String filename = requestURI.substring("/api/files/".length());
            log.error("Invalid file path format: {}", filename, e);
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            String requestURI = request.getRequestURI();
            String filename = requestURI.substring("/api/files/".length());
            log.error("File download failed: {}", filename, e);
            return getPlaceholderResponse(filename);
        }
    }

    @Operation(summary = "获取文件信息", description = "获取文件的基本信息")
    @GetMapping("/{filename:.+}/info")
    public ResponseEntity<Object> getFileInfo(
            @Parameter(description = "文件名") @PathVariable String filename) {
        
        if (!fileStorageService.fileExists(filename)) {
            return ResponseEntity.notFound().build();
        }
        
        long fileSize = fileStorageService.getFileSize(filename);
        String contentType = determineContentType(filename);
        
        Object fileInfo = new Object() {
            public final String name = filename;
            public final long size = fileSize;
            public final String type = contentType;
            public final String url = fileStorageService.getFileUrl(filename);
        };
        
        return ResponseEntity.ok(fileInfo);
    }

    /**
     * 确定文件的内容类型
     */
    private String determineContentType(String filename) {
        String extension = getFileExtension(filename).toLowerCase();
        
        switch (extension) {
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".bmp":
                return "image/bmp";
            case ".webp":
                return "image/webp";
            case ".svg":
                return "image/svg+xml";
            case ".pdf":
                return "application/pdf";
            case ".zip":
                return "application/zip";
            case ".json":
                return "application/json";
            case ".xml":
                return "application/xml";
            case ".txt":
                return "text/plain";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex);
    }

    /**
     * 从存储文件名中提取原始文件名
     */
    private String getOriginalFilename(String storedFilename) {
        // 存储的文件名格式: yyyyMMdd_HHmmss_uuid.ext
        // 这里简化处理，直接返回存储文件名
        String[] parts = storedFilename.split("/");
        return parts[parts.length - 1];
    }

    /**
     * 获取占位响应
     */
    private ResponseEntity<Resource> getPlaceholderResponse(String filename) {
        try {
            if (filename.endsWith(".json")) {
                return getPlaceholderJson(filename);
            } else {
                return getPlaceholderImage(filename);
            }
        } catch (Exception e) {
            log.error("生成占位响应失败: {}", filename, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 获取占位图片
     */
    private ResponseEntity<Resource> getPlaceholderImage(String filename) {
        String category = extractCategoryFromFilename(filename);
        String svg = String.format(
            "<svg width=\"400\" height=\"300\" xmlns=\"http://www.w3.org/2000/svg\">" +
            "<rect width=\"100%%\" height=\"100%%\" fill=\"#f8f9fa\" stroke=\"#dee2e6\" stroke-width=\"2\"/>" +
            "<circle cx=\"200\" cy=\"120\" r=\"40\" fill=\"#e9ecef\" stroke=\"#adb5bd\" stroke-width=\"2\"/>" +
            "<text x=\"200\" y=\"180\" text-anchor=\"middle\" font-family=\"Arial\" font-size=\"16\" fill=\"#495057\">%s</text>" +
            "<text x=\"200\" y=\"200\" text-anchor=\"middle\" font-family=\"Arial\" font-size=\"12\" fill=\"#6c757d\">涂色图片</text>" +
            "<text x=\"200\" y=\"220\" text-anchor=\"middle\" font-family=\"Arial\" font-size=\"10\" fill=\"#adb5bd\">%s</text>" +
            "</svg>", category, filename);

        return ResponseEntity.ok()
                .contentType(MediaType.valueOf("image/svg+xml"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filename + "\"")
                .body(new org.springframework.core.io.ByteArrayResource(svg.getBytes()));
    }

    /**
     * 获取占位JSON数据
     */
    private ResponseEntity<Resource> getPlaceholderJson(String filename) {
        String category = extractCategoryFromFilename(filename);
        String json = String.format(
            "{\n" +
            "  \"meta\": {\n" +
            "    \"version\": \"2.0\",\n" +
            "    \"name\": \"%s\",\n" +
            "    \"regions\": 8,\n" +
            "    \"colors\": 6,\n" +
            "    \"size\": [400, 300],\n" +
            "    \"difficulty\": 3,\n" +
            "    \"estimated_time\": 25\n" +
            "  },\n" +
            "  \"palette\": [\n" +
            "    {\"id\": 1, \"hex\": \"#ff6b6b\", \"name\": \"红色\"},\n" +
            "    {\"id\": 2, \"hex\": \"#4ecdc4\", \"name\": \"青色\"},\n" +
            "    {\"id\": 3, \"hex\": \"#45b7d1\", \"name\": \"蓝色\"},\n" +
            "    {\"id\": 4, \"hex\": \"#96ceb4\", \"name\": \"绿色\"},\n" +
            "    {\"id\": 5, \"hex\": \"#feca57\", \"name\": \"黄色\"},\n" +
            "    {\"id\": 6, \"hex\": \"#ff9ff3\", \"name\": \"粉色\"}\n" +
            "  ],\n" +
            "  \"regions\": [\n" +
            "    {\"id\": 1, \"name\": \"区域1\", \"recommended_color_id\": 1},\n" +
            "    {\"id\": 2, \"name\": \"区域2\", \"recommended_color_id\": 2},\n" +
            "    {\"id\": 3, \"name\": \"区域3\", \"recommended_color_id\": 3},\n" +
            "    {\"id\": 4, \"name\": \"区域4\", \"recommended_color_id\": 4},\n" +
            "    {\"id\": 5, \"name\": \"区域5\", \"recommended_color_id\": 5},\n" +
            "    {\"id\": 6, \"name\": \"区域6\", \"recommended_color_id\": 6},\n" +
            "    {\"id\": 7, \"name\": \"区域7\", \"recommended_color_id\": 1},\n" +
            "    {\"id\": 8, \"name\": \"区域8\", \"recommended_color_id\": 2}\n" +
            "  ],\n" +
            "  \"tags\": [\"%s\", \"涂色\", \"创意\"],\n" +
            "  \"age_group\": \"全年龄\",\n" +
            "  \"theme\": \"%s\"\n" +
            "}", category, category, category);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filename + "\"")
                .body(new org.springframework.core.io.ByteArrayResource(json.getBytes()));
    }

    /**
     * 从文件名提取分类
     */
    private String extractCategoryFromFilename(String filename) {
        if (filename.contains("houses")) return "房屋建筑";
        if (filename.contains("treehouses")) return "树屋小屋";
        if (filename.contains("farmhouses")) return "农场庄园";
        if (filename.contains("castles")) return "城堡宫殿";
        if (filename.contains("animals")) return "动物世界";
        if (filename.contains("plants")) return "植物花卉";
        if (filename.contains("cartoon")) return "卡通人物";
        if (filename.contains("vehicles")) return "交通工具";
        if (filename.contains("food")) return "食物美食";
        if (filename.contains("holidays")) return "节日庆典";
        return "涂色图片";
    }
}
