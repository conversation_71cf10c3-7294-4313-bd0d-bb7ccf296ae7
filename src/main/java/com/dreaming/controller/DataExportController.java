package com.dreaming.controller;

import com.dreaming.service.DataExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 数据导出控制器
 */
@Controller
@RequestMapping("/api/admin/export")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "数据导出", description = "数据导出和备份接口")
public class DataExportController {

    private final DataExportService dataExportService;

    /**
     * 数据导出页面
     */
    @GetMapping
    public String exportPage(Model model) {
        model.addAttribute("pageTitle", "数据导出");
        return "admin/export";
    }

    /**
     * 导出所有资源为JSON
     */
    @GetMapping("/resources/json")
    @Operation(summary = "导出资源JSON", description = "导出所有激活资源为JSON格式")
    public ResponseEntity<String> exportResourcesJson() {
        try {
            String jsonData = dataExportService.exportResourcesAsJson();
            String filename = "resources_" + getCurrentTimestamp() + ".json";
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(jsonData);
        } catch (Exception e) {
            log.error("导出资源JSON失败", e);
            return ResponseEntity.internalServerError()
                    .body("{\"error\":\"导出失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 导出指定分类的资源为JSON
     */
    @GetMapping("/resources/category/{category}/json")
    @Operation(summary = "按分类导出资源JSON", description = "导出指定分类的资源为JSON格式")
    public ResponseEntity<String> exportResourcesByCategoryJson(
            @Parameter(description = "分类名称") @PathVariable String category) {
        try {
            String jsonData = dataExportService.exportResourcesByCategoryAsJson(category);
            String filename = "resources_" + category + "_" + getCurrentTimestamp() + ".json";
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(jsonData);
        } catch (Exception e) {
            log.error("导出分类资源JSON失败: {}", category, e);
            return ResponseEntity.internalServerError()
                    .body("{\"error\":\"导出失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 导出资源为CSV
     */
    @GetMapping("/resources/csv")
    @Operation(summary = "导出资源CSV", description = "导出所有激活资源为CSV格式")
    public ResponseEntity<String> exportResourcesCsv() {
        try {
            String csvData = dataExportService.exportResourcesAsCsv();
            String filename = "resources_" + getCurrentTimestamp() + ".csv";
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.parseMediaType("text/csv; charset=UTF-8"))
                    .body("\uFEFF" + csvData); // 添加BOM以支持Excel中文显示
        } catch (Exception e) {
            log.error("导出资源CSV失败", e);
            return ResponseEntity.internalServerError()
                    .body("导出失败: " + e.getMessage());
        }
    }

    /**
     * 导出版本信息为JSON
     */
    @GetMapping("/versions/json")
    @Operation(summary = "导出版本JSON", description = "导出所有版本信息为JSON格式")
    public ResponseEntity<String> exportVersionsJson() {
        try {
            String jsonData = dataExportService.exportVersionsAsJson();
            String filename = "versions_" + getCurrentTimestamp() + ".json";
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(jsonData);
        } catch (Exception e) {
            log.error("导出版本JSON失败", e);
            return ResponseEntity.internalServerError()
                    .body("{\"error\":\"导出失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 生成资源统计报告
     */
    @GetMapping("/report/json")
    @Operation(summary = "生成统计报告", description = "生成详细的资源统计报告")
    public ResponseEntity<String> generateReport() {
        try {
            String reportData = dataExportService.generateResourceReport();
            String filename = "report_" + getCurrentTimestamp() + ".json";
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(reportData);
        } catch (Exception e) {
            log.error("生成统计报告失败", e);
            return ResponseEntity.internalServerError()
                    .body("{\"error\":\"生成报告失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 备份所有数据
     */
    @GetMapping("/backup/json")
    @Operation(summary = "备份所有数据", description = "备份所有资源和版本数据")
    public ResponseEntity<String> backupAllData() {
        try {
            String backupData = dataExportService.backupAllData();
            String filename = "backup_" + getCurrentTimestamp() + ".json";
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(backupData);
        } catch (Exception e) {
            log.error("备份数据失败", e);
            return ResponseEntity.internalServerError()
                    .body("{\"error\":\"备份失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 在线预览统计报告
     */
    @GetMapping("/report/preview")
    public ResponseEntity<String> previewReport() {
        try {
            String reportData = dataExportService.generateResourceReport();
            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(reportData);
        } catch (Exception e) {
            log.error("预览统计报告失败", e);
            return ResponseEntity.internalServerError()
                    .body("{\"error\":\"预览失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 获取当前时间戳字符串
     */
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    }
}
