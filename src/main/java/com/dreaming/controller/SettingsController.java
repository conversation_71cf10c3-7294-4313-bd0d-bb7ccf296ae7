package com.dreaming.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统设置控制器
 */
@Controller
@RequestMapping("/admin/settings")
@RequiredArgsConstructor
@Slf4j
public class SettingsController {

    @Value("${app.file-storage.upload-dir}")
    private String uploadDir;

    @Value("${app.file-storage.base-url}")
    private String baseUrl;

    @Value("${server.port}")
    private String serverPort;

    @Value("${spring.datasource.url}")
    private String datasourceUrl;

    /**
     * 系统设置页面
     */
    @GetMapping
    public String settingsPage(Model model) {
        // 获取系统信息
        Map<String, Object> systemInfo = getSystemInfo();
        model.addAttribute("systemInfo", systemInfo);
        model.addAttribute("pageTitle", "系统设置");
        
        return "admin/settings";
    }

    /**
     * 清理临时文件
     */
    @PostMapping("/cleanup")
    public String cleanupTempFiles(RedirectAttributes redirectAttributes) {
        try {
            // 这里可以实现清理逻辑
            // 例如清理过期的临时文件、日志文件等
            
            redirectAttributes.addFlashAttribute("successMessage", "临时文件清理完成");
            log.info("管理员执行了临时文件清理操作");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "清理失败: " + e.getMessage());
            log.error("临时文件清理失败", e);
        }
        
        return "redirect:/admin/settings";
    }

    /**
     * 重新计算统计信息
     */
    @PostMapping("/recalculate-stats")
    public String recalculateStats(RedirectAttributes redirectAttributes) {
        try {
            // 这里可以实现重新计算统计信息的逻辑
            // 例如重新计算下载次数、文件大小等
            
            redirectAttributes.addFlashAttribute("successMessage", "统计信息重新计算完成");
            log.info("管理员执行了统计信息重新计算操作");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "重新计算失败: " + e.getMessage());
            log.error("统计信息重新计算失败", e);
        }
        
        return "redirect:/admin/settings";
    }

    /**
     * 获取系统信息
     */
    private Map<String, Object> getSystemInfo() {
        Map<String, Object> info = new HashMap<>();
        
        // 基本信息
        info.put("javaVersion", System.getProperty("java.version"));
        info.put("osName", System.getProperty("os.name"));
        info.put("osVersion", System.getProperty("os.version"));
        info.put("serverPort", serverPort);
        info.put("uploadDir", uploadDir);
        info.put("baseUrl", baseUrl);
        info.put("datasourceUrl", datasourceUrl);
        
        // 内存信息
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        info.put("maxMemory", formatBytes(maxMemory));
        info.put("totalMemory", formatBytes(totalMemory));
        info.put("usedMemory", formatBytes(usedMemory));
        info.put("freeMemory", formatBytes(freeMemory));
        info.put("memoryUsagePercent", Math.round((double) usedMemory / totalMemory * 100));
        
        // 磁盘信息
        File uploadDirFile = new File(uploadDir);
        if (uploadDirFile.exists()) {
            long totalSpace = uploadDirFile.getTotalSpace();
            long freeSpace = uploadDirFile.getFreeSpace();
            long usedSpace = totalSpace - freeSpace;
            
            info.put("diskTotal", formatBytes(totalSpace));
            info.put("diskUsed", formatBytes(usedSpace));
            info.put("diskFree", formatBytes(freeSpace));
            info.put("diskUsagePercent", Math.round((double) usedSpace / totalSpace * 100));
        }
        
        // 上传目录信息
        info.put("uploadDirExists", uploadDirFile.exists());
        info.put("uploadDirWritable", uploadDirFile.canWrite());
        
        return info;
    }

    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }
}
