package com.dreaming.controller;

import com.dreaming.dto.VersionCheckResponse;
import com.dreaming.entity.AppVersion;
import com.dreaming.service.VersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 版本管理控制器
 * 提供应用版本管理和更新检查API
 */
@RestController
@RequestMapping("/api/version")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "版本管理", description = "应用版本管理和更新检查接口")
public class VersionController {

    private final VersionService versionService;

    @Operation(summary = "检查版本更新", description = "Android客户端检查是否有新版本可用")
    @GetMapping("/check")
    public ResponseEntity<VersionCheckResponse> checkVersion(
            @Parameter(description = "客户端当前版本号") @RequestParam String currentVersion) {
        
        log.info("客户端版本检查请求: {}", currentVersion);
        
        VersionCheckResponse response = versionService.checkVersion(currentVersion);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "获取当前版本", description = "获取服务器当前版本信息")
    @GetMapping("/current")
    public ResponseEntity<AppVersion> getCurrentVersion() {
        return versionService.getCurrentVersion()
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "获取所有版本", description = "获取所有版本列表")
    @GetMapping("/all")
    public ResponseEntity<List<AppVersion>> getAllVersions() {
        List<AppVersion> versions = versionService.getAllVersions();
        return ResponseEntity.ok(versions);
    }

    @Operation(summary = "根据版本号获取版本信息", description = "根据版本号获取特定版本的详细信息")
    @GetMapping("/{version}")
    public ResponseEntity<AppVersion> getVersionByNumber(
            @Parameter(description = "版本号") @PathVariable String version) {
        
        return versionService.getVersionByNumber(version)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "创建新版本", description = "创建新的应用版本")
    @PostMapping
    public ResponseEntity<AppVersion> createVersion(@RequestBody Map<String, Object> versionData) {
        String version = (String) versionData.get("version");
        String versionName = (String) versionData.get("versionName");
        String description = (String) versionData.get("description");
        Boolean forceUpdate = (Boolean) versionData.getOrDefault("forceUpdate", false);
        
        log.info("创建新版本请求: {}", version);
        
        AppVersion appVersion = versionService.createVersion(version, versionName, description, forceUpdate);
        return ResponseEntity.ok(appVersion);
    }

    @Operation(summary = "设置当前版本", description = "将指定版本设置为当前版本")
    @PutMapping("/{version}/current")
    public ResponseEntity<Void> setCurrentVersion(
            @Parameter(description = "版本号") @PathVariable String version) {
        
        log.info("设置当前版本请求: {}", version);
        
        versionService.setCurrentVersion(version);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "删除版本", description = "删除指定版本(不能删除当前版本)")
    @DeleteMapping("/{version}")
    public ResponseEntity<Void> deleteVersion(
            @Parameter(description = "版本号") @PathVariable String version) {
        
        log.info("删除版本请求: {}", version);
        
        versionService.deleteVersion(version);
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "更新版本统计", description = "重新计算指定版本的统计信息")
    @PutMapping("/{version}/stats")
    public ResponseEntity<Void> updateVersionStats(
            @Parameter(description = "版本号") @PathVariable String version) {
        
        log.info("更新版本统计请求: {}", version);
        
        versionService.updateVersionStats(version);
        return ResponseEntity.ok().build();
    }
}
