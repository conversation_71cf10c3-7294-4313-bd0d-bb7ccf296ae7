package com.dreaming.controller;

import com.dreaming.dto.ResourceDTO;
import com.dreaming.dto.CategoryDTO;
import com.dreaming.entity.AppVersion;
import com.dreaming.service.ResourceService;
import com.dreaming.service.VersionService;
import com.dreaming.service.CategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 管理后台控制器
 * 提供Web界面管理功能
 */
@Controller
@RequestMapping("/admin")
@RequiredArgsConstructor
@Slf4j
public class AdminController {

    private final ResourceService resourceService;
    private final VersionService versionService;
    private final CategoryService categoryService;
    private final com.dreaming.service.ResourceMigrationService migrationService;

    /**
     * 测试页面
     */
    @GetMapping("/test")
    public String test() {
        return "admin/test";
    }

    /**
     * 管理后台首页
     */
    @GetMapping
    public String dashboard(Model model) {
        // 获取统计信息
        long totalResources = resourceService.getTotalResourceCount();
        List<String> categories = resourceService.getAllCategories();
        Optional<AppVersion> currentVersion = versionService.getCurrentVersion();
        List<ResourceDTO> latestResources = resourceService.getLatestResources(5);
        List<ResourceDTO> popularResources = resourceService.getPopularResources(5);

        model.addAttribute("totalResources", totalResources);
        model.addAttribute("categoryCount", categories.size());
        model.addAttribute("categories", categories);
        model.addAttribute("currentVersion", currentVersion.orElse(null));
        model.addAttribute("latestResources", latestResources);
        model.addAttribute("popularResources", popularResources);

        return "admin/dashboard";
    }

    /**
     * 资源管理页面 - 重定向到批量上传
     */
    @GetMapping("/resources")
    public String resources() {
        return "redirect:/admin/resources/batch-upload";
    }

    /**
     * 资源列表页面（保留原功能，通过新路径访问）
     */
    @GetMapping("/resources/list")
    public String resourceList(@RequestParam(defaultValue = "0") int page,
                              @RequestParam(defaultValue = "20") int size,
                              @RequestParam(required = false) String category,
                              Model model) {

        Pageable pageable = PageRequest.of(page, size);
        Page<ResourceDTO> resourcePage;

        if (category != null && !category.isEmpty()) {
            // 按分类筛选
            resourcePage = resourceService.getResourcesByCategory(category, pageable);
        } else {
            resourcePage = resourceService.getResourcesPage(pageable);
        }

        List<String> categories = resourceService.getAllCategories();

        model.addAttribute("resources", resourcePage);
        model.addAttribute("categories", categories);
        model.addAttribute("selectedCategory", category);
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", resourcePage.getTotalPages());

        return "admin/resources";
    }

    /**
     * 资源上传页面 - 重定向到批量上传
     */
    @GetMapping("/resources/upload")
    public String uploadForm() {
        return "redirect:/admin/resources/batch-upload";
    }

    /**
     * 批量上传页面（文件夹版本，推荐）
     */
    @GetMapping("/resources/batch-upload")
    public String batchUploadPage(Model model) {
        List<CategoryDTO> categoryDTOs = categoryService.getAllActiveCategories();
        Optional<AppVersion> currentVersion = versionService.getCurrentVersion();

        model.addAttribute("categoryDTOs", categoryDTOs);
        model.addAttribute("currentVersion", currentVersion.map(AppVersion::getVersion).orElse("1.0.0"));
        model.addAttribute("pageTitle", "文件夹批量上传");

        return "admin/batch-upload-folder";
    }

    /**
     * 批量上传页面（简化版）
     */
    @GetMapping("/resources/batch-upload-simple")
    public String batchUploadSimplePage(Model model) {
        List<CategoryDTO> categoryDTOs = categoryService.getAllActiveCategories();
        Optional<AppVersion> currentVersion = versionService.getCurrentVersion();

        model.addAttribute("categoryDTOs", categoryDTOs);
        model.addAttribute("currentVersion", currentVersion.map(AppVersion::getVersion).orElse("1.0.0"));
        model.addAttribute("pageTitle", "批量上传");

        return "admin/batch-upload-simple";
    }

    /**
     * 批量上传测试页面
     */
    @GetMapping("/resources/batch-upload-test")
    public String batchUploadTestPage(Model model) {
        List<CategoryDTO> categoryDTOs = categoryService.getAllActiveCategories();
        Optional<AppVersion> currentVersion = versionService.getCurrentVersion();

        model.addAttribute("categoryDTOs", categoryDTOs);
        model.addAttribute("currentVersion", currentVersion.map(AppVersion::getVersion).orElse("1.0.0"));
        model.addAttribute("pageTitle", "批量上传测试");

        return "admin/batch-upload-test";
    }

    /**
     * 批量上传最小版页面
     */
    @GetMapping("/resources/batch-upload-minimal")
    public String batchUploadMinimalPage(Model model) {
        List<CategoryDTO> categoryDTOs = categoryService.getAllActiveCategories();
        Optional<AppVersion> currentVersion = versionService.getCurrentVersion();

        model.addAttribute("categoryDTOs", categoryDTOs);
        model.addAttribute("currentVersion", currentVersion.map(AppVersion::getVersion).orElse("1.0.0"));
        model.addAttribute("pageTitle", "批量上传（最小版）");

        return "admin/batch-upload-minimal";
    }

    /**
     * 批量上传调试页面
     */
    @GetMapping("/resources/debug/batch-upload")
    public String batchUploadDebugPage(Model model) {
        List<CategoryDTO> categoryDTOs = categoryService.getAllActiveCategories();
        Optional<AppVersion> currentVersion = versionService.getCurrentVersion();

        model.addAttribute("categoryDTOs", categoryDTOs);
        model.addAttribute("currentVersion", currentVersion.map(AppVersion::getVersion).orElse("1.0.0"));
        model.addAttribute("pageTitle", "批量上传调试");

        return "admin/batch-upload-debug";
    }



    /**
     * 处理批量资源上传（简化版，带文件配对校验）
     */
    @PostMapping("/resources/batch-upload")
    public String batchUploadResources(@RequestParam("files") MultipartFile[] files,
                                     @RequestParam("categoryId") Long categoryId,
                                     @RequestParam("difficulty") Integer difficulty,
                                     @RequestParam("version") String version,
                                     RedirectAttributes redirectAttributes) {
        try {
            // 调用改进的批量上传方法
            Map<String, Object> result = resourceService.batchUploadResourcesWithValidation(files, categoryId, difficulty, version);

            int successCount = (Integer) result.get("successCount");
            int totalPairs = (Integer) result.get("totalPairs");
            @SuppressWarnings("unchecked")
            List<String> warnings = (List<String>) result.get("warnings");

            String message = String.format("批量上传完成: 成功处理 %d/%d 个文件对", successCount, totalPairs);
            if (!warnings.isEmpty()) {
                message += "，有 " + warnings.size() + " 个警告";
            }

            redirectAttributes.addFlashAttribute("successMessage", message);
            if (!warnings.isEmpty()) {
                redirectAttributes.addFlashAttribute("warningMessages", warnings);
            }

            log.info("管理员批量上传资源完成: 成功 {}/{} 个文件对", successCount, totalPairs);
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "批量上传失败: " + e.getMessage());
            log.error("管理员批量上传资源失败", e);
        }

        return "redirect:/admin/resources/list";
    }

    /**
     * 资源详情页面
     */
    @GetMapping("/resources/{id}")
    public String resourceDetail(@PathVariable Long id, Model model) {
        Optional<ResourceDTO> resource = resourceService.getResourceById(id);
        if (resource.isPresent()) {
            model.addAttribute("resource", resource.get());
            return "admin/resource-detail";
        } else {
            return "redirect:/admin/resources/list";
        }
    }

    /**
     * 删除资源
     */
    @PostMapping("/resources/{id}/delete")
    public String deleteResource(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            Optional<ResourceDTO> resource = resourceService.getResourceById(id);
            if (resource.isPresent()) {
                resourceService.deleteResource(id);
                redirectAttributes.addFlashAttribute("successMessage", "资源删除成功: " + resource.get().getName());
                log.info("管理员删除资源: {}", resource.get().getName());
            }
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "删除失败: " + e.getMessage());
            log.error("管理员删除资源失败", e);
        }
        
        return "redirect:/admin/resources/list";
    }

    /**
     * 切换资源状态
     */
    @PostMapping("/resources/{id}/toggle")
    public String toggleResourceStatus(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            resourceService.toggleResourceStatus(id);
            redirectAttributes.addFlashAttribute("successMessage", "资源状态切换成功");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "状态切换失败: " + e.getMessage());
        }
        
        return "redirect:/admin/resources/list";
    }

    /**
     * 版本管理页面
     */
    @GetMapping("/versions")
    public String versionList(Model model) {
        List<AppVersion> versions = versionService.getAllVersions();
        Optional<AppVersion> currentVersion = versionService.getCurrentVersion();
        
        model.addAttribute("versions", versions);
        model.addAttribute("currentVersion", currentVersion.orElse(null));
        
        return "admin/versions";
    }

    /**
     * 创建版本页面
     */
    @GetMapping("/versions/create")
    public String createVersionForm() {
        return "admin/create-version";
    }

    /**
     * 处理版本创建
     */
    @PostMapping("/versions/create")
    public String createVersion(@RequestParam("version") String version,
                               @RequestParam("versionName") String versionName,
                               @RequestParam("description") String description,
                               @RequestParam(value = "forceUpdate", defaultValue = "false") boolean forceUpdate,
                               RedirectAttributes redirectAttributes) {
        try {
            AppVersion appVersion = versionService.createVersion(version, versionName, description, forceUpdate);
            redirectAttributes.addFlashAttribute("successMessage", "版本创建成功: " + appVersion.getVersion());
            log.info("管理员创建版本: {}", appVersion.getVersion());
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "版本创建失败: " + e.getMessage());
            log.error("管理员创建版本失败", e);
        }
        
        return "redirect:/admin/versions";
    }

    /**
     * 设置当前版本
     */
    @PostMapping("/versions/{version}/current")
    public String setCurrentVersion(@PathVariable String version, RedirectAttributes redirectAttributes) {
        try {
            versionService.setCurrentVersion(version);
            redirectAttributes.addFlashAttribute("successMessage", "当前版本设置成功: " + version);
            log.info("管理员设置当前版本: {}", version);
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "设置当前版本失败: " + e.getMessage());
            log.error("管理员设置当前版本失败", e);
        }
        
        return "redirect:/admin/versions";
    }

    /**
     * 删除版本
     */
    @PostMapping("/versions/{version}/delete")
    public String deleteVersion(@PathVariable String version, RedirectAttributes redirectAttributes) {
        try {
            versionService.deleteVersion(version);
            redirectAttributes.addFlashAttribute("successMessage", "版本删除成功: " + version);
            log.info("管理员删除版本: {}", version);
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "版本删除失败: " + e.getMessage());
            log.error("管理员删除版本失败", e);
        }
        
        return "redirect:/admin/versions";
    }

    /**
     * 分类管理页面
     */
    @GetMapping("/categories")
    public String categoryList(Model model) {
        List<CategoryDTO> categories = categoryService.getAllActiveCategories();
        
        model.addAttribute("categories", categories);
        model.addAttribute("pageTitle", "分类管理");
        
        return "admin/categories";
    }

    /**
     * 分类创建页面
     */
    @GetMapping("/categories/create")
    public String createCategoryForm(Model model) {
        model.addAttribute("category", new CategoryDTO());
        model.addAttribute("pageTitle", "创建分类");
        model.addAttribute("isEdit", false);
        
        return "admin/category-form";
    }

    /**
     * 分类编辑页面
     */
    @GetMapping("/categories/{id}/edit")
    public String editCategoryForm(@PathVariable Long id, Model model) {
        Optional<CategoryDTO> category = categoryService.getCategoryById(id);
        if (category.isPresent()) {
            model.addAttribute("category", category.get());
            model.addAttribute("pageTitle", "编辑分类");
            model.addAttribute("isEdit", true);
            return "admin/category-form";
        } else {
            return "redirect:/admin/categories";
        }
    }

    /**
     * 处理分类创建
     */
    @PostMapping("/categories/create")
    public String createCategory(@ModelAttribute CategoryDTO categoryDTO, 
                                RedirectAttributes redirectAttributes) {
        try {
            CategoryDTO createdCategory = categoryService.createCategory(categoryDTO);
            redirectAttributes.addFlashAttribute("successMessage", 
                "分类创建成功: " + createdCategory.getName());
            log.info("管理员创建分类: {}", createdCategory.getName());
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "创建分类失败: " + e.getMessage());
            log.error("管理员创建分类失败", e);
        }
        
        return "redirect:/admin/categories";
    }

    /**
     * 处理分类更新
     */
    @PostMapping("/categories/{id}/update")
    public String updateCategory(@PathVariable Long id, 
                                @ModelAttribute CategoryDTO categoryDTO,
                                RedirectAttributes redirectAttributes) {
        try {
            Optional<CategoryDTO> updatedCategory = categoryService.updateCategory(id, categoryDTO);
            if (updatedCategory.isPresent()) {
                redirectAttributes.addFlashAttribute("successMessage", 
                    "分类更新成功: " + updatedCategory.get().getName());
                log.info("管理员更新分类: {}", id);
            } else {
                redirectAttributes.addFlashAttribute("errorMessage", "分类不存在");
            }
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "更新分类失败: " + e.getMessage());
            log.error("管理员更新分类失败", e);
        }
        
        return "redirect:/admin/categories";
    }

    /**
     * 删除分类
     */
    @PostMapping("/categories/{id}/delete")
    public String deleteCategory(@PathVariable Long id, RedirectAttributes redirectAttributes) {
        try {
            boolean deleted = categoryService.deleteCategory(id);
            if (deleted) {
                redirectAttributes.addFlashAttribute("successMessage", "分类删除成功");
                log.info("管理员删除分类: {}", id);
            } else {
                redirectAttributes.addFlashAttribute("errorMessage", "分类不存在");
            }
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "删除分类失败: " + e.getMessage());
            log.error("管理员删除分类失败", e);
        }
        
        return "redirect:/admin/categories";
    }

    /**
     * 迁移资源到正确的分类文件夹
     */
    @PostMapping("/resources/migrate")
    public String migrateResources(RedirectAttributes redirectAttributes) {
        try {
            migrationService.migrateResourcesToCorrectFolders();
            redirectAttributes.addFlashAttribute("successMessage", "资源迁移完成！文件已移动到正确的分类文件夹");
            log.info("管理员触发资源迁移完成");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "资源迁移失败: " + e.getMessage());
            log.error("管理员触发资源迁移失败", e);
        }
        
        return "redirect:/admin/resources/list";
    }
}
