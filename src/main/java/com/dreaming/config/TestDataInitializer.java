package com.dreaming.config;

import com.dreaming.entity.Category;
import com.dreaming.entity.ColoringResource;
import com.dreaming.repository.CategoryRepository;
import com.dreaming.repository.ColoringResourceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

/**
 * 测试数据初始化器
 * 为客户端API创建测试数据
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Order(3) // 在DataInitializer之后执行
public class TestDataInitializer implements CommandLineRunner {

    private final ColoringResourceRepository resourceRepository;
    private final CategoryRepository categoryRepository;

    @Override
    public void run(String... args) throws Exception {
        // 强制创建测试数据
        log.info("开始创建测试数据...");
        createTestResources();
        log.info("测试数据创建完成！");
    }

    private void createTestResources() {
        List<Category> categories = categoryRepository.findAll();
        Random random = new Random();

        // 为每个分类创建2-3个测试资源
        for (Category category : categories) {
            int resourceCount = 2 + random.nextInt(2); // 2-3个资源
            
            for (int i = 1; i <= resourceCount; i++) {
                ColoringResource resource = createTestResource(category, i, random);
                resourceRepository.save(resource);
                log.info("创建测试资源: {} - {}", category.getName(), resource.getName());
            }
        }
    }

    private ColoringResource createTestResource(Category category, int index, Random random) {
        ColoringResource resource = new ColoringResource();
        
        // 基础信息
        resource.setName(generateResourceName(category.getName(), index));
        resource.setDescription(generateResourceDescription(category.getName(), index));
        resource.setCategory(category.getName());
        resource.setCategoryId(category.getId());
        
        // 难度和时间
        resource.setDifficulty(1 + random.nextInt(5)); // 1-5
        resource.setEstimatedTime(10 + random.nextInt(50)); // 10-60分钟
        
        // 区域和颜色
        resource.setRegionsCount(5 + random.nextInt(20)); // 5-25个区域
        resource.setColorsCount(3 + random.nextInt(10)); // 3-12种颜色
        
        // 文件信息
        resource.setOriginalFilename(generateFilename(category.getNameEn(), index));
        resource.setStoredFilename(generateFilename(category.getNameEn(), index));
        resource.setFilePath("/uploads/" + generateFilename(category.getNameEn(), index));
        resource.setFileSize(50000L + random.nextInt(200000)); // 50KB-250KB
        resource.setContentType("image/png");
        
        // 统计信息
        resource.setDownloadCount((long) random.nextInt(1000));
        resource.setCompletionCount((long) random.nextInt(500));
        resource.setRating(3.0 + random.nextDouble() * 2.0); // 3.0-5.0
        resource.setRatingCount(random.nextInt(100));
        
        // 状态
        resource.setActive(true);
        resource.setIsPremium(random.nextBoolean() && random.nextDouble() < 0.3); // 30%概率为付费
        resource.setIsFeatured(random.nextBoolean() && random.nextDouble() < 0.2); // 20%概率为精选
        resource.setStatus("ACTIVE");
        
        // 时间信息
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime createdTime = now.minusDays(random.nextInt(30)); // 最近30天内创建
        resource.setCreatedAt(createdTime);
        resource.setUpdatedAt(createdTime);
        resource.setCreatedBy("system");
        resource.setUpdatedBy("system");
        
        // 版本
        resource.setVersion("1.0.0");
        
        // 额外信息
        resource.setAgeGroup(generateAgeGroup(resource.getDifficulty()));
        resource.setTheme(category.getName());
        resource.setTags(generateTags(category.getName()));
        
        // URLs
        resource.setColoringDataUrl("/api/files/" + generateJsonFilename(category.getNameEn(), index));
        resource.setPreviewImageUrl("/api/files/preview_" + generateFilename(category.getNameEn(), index));
        
        return resource;
    }

    private String generateResourceName(String categoryName, int index) {
        String[] prefixes = {"可爱的", "美丽的", "精致的", "有趣的", "梦幻的", "温馨的", "神奇的", "迷人的"};
        String[] suffixes = {"图案", "设计", "作品", "画作", "图画", "插画"};
        
        Random random = new Random();
        String prefix = prefixes[random.nextInt(prefixes.length)];
        String suffix = suffixes[random.nextInt(suffixes.length)];
        
        return prefix + categoryName + suffix + " " + index;
    }

    private String generateResourceDescription(String categoryName, int index) {
        return String.format("这是一个精美的%s主题涂色作品，适合各个年龄段的用户。通过涂色可以放松心情，培养专注力和创造力。", categoryName);
    }

    private String generateFilename(String categoryEn, int index) {
        return categoryEn.toLowerCase() + "_" + index + ".png";
    }

    private String generateJsonFilename(String categoryEn, int index) {
        return categoryEn.toLowerCase() + "_" + index + "_data.json";
    }

    private String generateAgeGroup(int difficulty) {
        switch (difficulty) {
            case 1: return "3-6岁";
            case 2: return "5-8岁";
            case 3: return "7-12岁";
            case 4: return "10-16岁";
            case 5: return "成人";
            default: return "全年龄";
        }
    }

    private String generateTags(String categoryName) {
        // 简化的标签生成，实际应该根据分类生成相关标签
        return String.format("[\"%s\", \"涂色\", \"创意\", \"放松\"]", categoryName);
    }
}
