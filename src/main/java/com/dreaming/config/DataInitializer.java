package com.dreaming.config;

import com.dreaming.entity.AppVersion;
import com.dreaming.entity.Category;
import com.dreaming.repository.AppVersionRepository;
import com.dreaming.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 数据初始化器
 * 在应用启动时初始化基础数据
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {

    private final AppVersionRepository versionRepository;
    private final CategoryRepository categoryRepository;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化数据...");
        initializeCategories();
        initializeVersionData();
        log.info("数据初始化完成");
    }

    /**
     * 初始化分类数据
     */
    private void initializeCategories() {
        if (categoryRepository.count() > 0) {
            log.info("分类数据已存在，跳过初始化");
            return;
        }

        log.info("初始化分类数据...");

        List<Category> categories = Arrays.asList(
            createCategory("房屋建筑", "Houses", "/icons/houses.png", "各种房屋、建筑物涂色图", 1),
            createCategory("树屋小屋", "Treehouses", "/icons/treehouses.png", "童话般的树屋和小屋", 2),
            createCategory("农场庄园", "Farmhouses", "/icons/farmhouses.png", "乡村农场和庄园建筑", 3),
            createCategory("城堡宫殿", "Castles", "/icons/castles.png", "梦幻城堡和宫殿", 4),
            createCategory("动物世界", "Animals", "/icons/animals.png", "可爱的动物朋友们", 5),
            createCategory("植物花卉", "Plants", "/icons/plants.png", "美丽的花朵和植物", 6),
            createCategory("卡通人物", "Cartoon", "/icons/cartoon.png", "有趣的卡通角色", 7),
            createCategory("交通工具", "Vehicles", "/icons/vehicles.png", "汽车、飞机、船只等", 8),
            createCategory("食物美食", "Food", "/icons/food.png", "美味的食物和饮品", 9),
            createCategory("节日庆典", "Holidays", "/icons/holidays.png", "节日主题涂色图", 10)
        );

        categoryRepository.saveAll(categories);
        log.info("成功初始化 {} 个分类", categories.size());
    }

    /**
     * 创建分类对象
     */
    private Category createCategory(String name, String nameEn, String iconUrl, String description, int sortOrder) {
        Category category = new Category();
        category.setName(name);
        category.setNameEn(nameEn);
        category.setIconUrl(iconUrl);
        category.setDescription(description);
        category.setSortOrder(sortOrder);
        category.setIsActive(true);
        category.setCreatedAt(LocalDateTime.now());
        category.setUpdatedAt(LocalDateTime.now());
        return category;
    }

    /**
     * 初始化版本数据
     */
    private void initializeVersionData() {
        // 检查是否已有版本数据
        if (versionRepository.count() == 0) {
            log.info("初始化版本数据...");
            
            AppVersion initialVersion = new AppVersion();
            initialVersion.setVersion("1.0.0");
            initialVersion.setVersionName("初始版本");
            initialVersion.setDescription("Dreaming涂色App的初始版本，包含基础功能和示例资源。");
            initialVersion.setIsCurrent(true);
            initialVersion.setForceUpdate(false);
            initialVersion.setResourceCount(0);
            initialVersion.setTotalSize(0L);
            initialVersion.setReleaseTime(LocalDateTime.now());
            initialVersion.setCreatedBy("system");
            initialVersion.setChangeLog("- 初始版本发布\n- 基础涂色功能\n- 资源管理系统");
            
            versionRepository.save(initialVersion);
            log.info("版本数据初始化完成: {}", initialVersion.getVersion());
        } else {
            log.info("版本数据已存在，跳过初始化");
        }
    }
}
