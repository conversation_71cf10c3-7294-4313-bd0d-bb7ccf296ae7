package com.dreaming.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * 应用版本实体类
 * 管理资源包的版本信息
 */
@Entity
@Table(name = "app_versions")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 版本号
     */
    @Column(nullable = false, unique = true, length = 20)
    private String version;

    /**
     * 版本名称
     */
    @Column(nullable = false, length = 100)
    private String versionName;

    /**
     * 版本描述
     */
    @Column(length = 1000)
    private String description;

    /**
     * 是否为当前版本
     */
    @Column(name = "is_current", nullable = false)
    private Boolean isCurrent = false;

    /**
     * 是否强制更新
     */
    @Column(name = "force_update", nullable = false)
    private Boolean forceUpdate = false;

    /**
     * 资源总数
     */
    @Column(name = "resource_count")
    private Integer resourceCount = 0;

    /**
     * 版本大小(字节)
     */
    @Column(name = "total_size")
    private Long totalSize = 0L;

    /**
     * 发布时间
     */
    @Column(name = "release_time")
    private LocalDateTime releaseTime;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 创建者
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * 更新日志
     */
    @Column(name = "change_log", columnDefinition = "TEXT")
    private String changeLog;
}
