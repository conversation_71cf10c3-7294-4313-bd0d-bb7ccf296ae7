package com.dreaming.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 涂色资源分类实体
 */
@Entity
@Table(name = "categories")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Category {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 分类名称
     */
    @Column(nullable = false, length = 50)
    private String name;

    /**
     * 英文名称
     */
    @Column(length = 50)
    private String nameEn;

    /**
     * 分类图标URL
     */
    @Column(length = 200)
    private String iconUrl;

    /**
     * 分类描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 排序顺序
     */
    @Column(columnDefinition = "INT DEFAULT 0")
    private Integer sortOrder = 0;

    /**
     * 是否激活
     */
    @Column(columnDefinition = "BOOLEAN DEFAULT TRUE")
    private Boolean isActive = true;

    /**
     * 创建时间
     */
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
