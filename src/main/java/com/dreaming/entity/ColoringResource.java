package com.dreaming.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 涂色资源实体类
 * 存储涂色图片的基本信息
 */
@Entity
@Table(name = "coloring_resources")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ColoringResource {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 资源名称
     */
    @Column(nullable = false, length = 100)
    private String name;

    /**
     * 资源描述
     */
    @Column(length = 500)
    private String description;

    /**
     * 资源分类 (保持向后兼容)
     */
    @Column(nullable = false, length = 50)
    private String category;

    /**
     * 分类ID (新增字段，关联Category表)
     */
    @Column(name = "category_id")
    private Long categoryId;

    /**
     * 难度等级 (1-5)
     */
    @Column(nullable = false)
    private Integer difficulty;

    /**
     * 原始图片文件名
     */
    @Column(name = "original_filename", nullable = false)
    private String originalFilename;

    /**
     * 存储的文件名
     */
    @Column(name = "stored_filename", nullable = false)
    private String storedFilename;

    /**
     * 文件路径
     */
    @Column(name = "file_path", nullable = false)
    private String filePath;

    /**
     * 文件大小(字节)
     */
    @Column(name = "file_size")
    private Long fileSize;

    /**
     * 文件类型
     */
    @Column(name = "content_type", length = 100)
    private String contentType;

    /**
     * 图片宽度
     */
    private Integer width;

    /**
     * 图片高度
     */
    private Integer height;

    /**
     * 资源版本
     */
    @Column(nullable = false, length = 20)
    private String version;

    /**
     * 是否激活
     */
    @Column(nullable = false)
    private Boolean active = true;

    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 下载次数
     */
    @Column(name = "download_count")
    private Long downloadCount = 0L;

    /**
     * 标签 (JSON格式存储)
     */
    @Column(columnDefinition = "TEXT")
    private String tags;

    // 涂色App专用字段
    /**
     * 子分类
     */
    @Column(length = 50)
    private String subcategory;

    /**
     * 复杂度评分
     */
    @Column(name = "complexity_score")
    private Integer complexityScore;

    /**
     * 预计完成时间(分钟)
     */
    @Column(name = "estimated_time")
    private Integer estimatedTime;

    /**
     * 预览图URL
     */
    @Column(name = "preview_image_url", length = 500)
    private String previewImageUrl;

    /**
     * 上色示例URL
     */
    @Column(name = "colored_sample_url", length = 500)
    private String coloredSampleUrl;

    /**
     * 涂色数据JSON文件URL
     */
    @Column(name = "coloring_data_url", length = 500)
    private String coloringDataUrl;

    /**
     * 涂色区域数量
     */
    @Column(name = "regions_count")
    private Integer regionsCount;

    /**
     * 推荐颜色数量
     */
    @Column(name = "colors_count")
    private Integer colorsCount;

    /**
     * 适合年龄段
     */
    @Column(name = "age_group", length = 20)
    private String ageGroup;

    /**
     * 主题
     */
    @Column(length = 50)
    private String theme;

    /**
     * 完成次数
     */
    @Column(name = "completion_count")
    private Long completionCount = 0L;

    /**
     * 评分
     */
    @Column(precision = 3, scale = 2)
    private Double rating = 0.0;

    /**
     * 评分人数
     */
    @Column(name = "rating_count")
    private Integer ratingCount = 0;

    /**
     * 是否付费内容
     */
    @Column(name = "is_premium")
    private Boolean isPremium = false;

    /**
     * 是否精选
     */
    @Column(name = "is_featured")
    private Boolean isFeatured = false;

    /**
     * 状态
     */
    @Column(length = 20)
    private String status = "ACTIVE";

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
}
