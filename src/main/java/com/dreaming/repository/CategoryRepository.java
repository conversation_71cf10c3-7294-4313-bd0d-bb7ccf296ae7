package com.dreaming.repository;

import com.dreaming.entity.Category;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 分类数据访问层
 */
@Repository
public interface CategoryRepository extends JpaRepository<Category, Long> {

    /**
     * 查找所有激活的分类，按排序顺序
     */
    List<Category> findByIsActiveTrueOrderBySortOrderAsc();

    /**
     * 根据名称查找分类
     */
    Category findByName(String name);

    /**
     * 根据英文名称查找分类
     */
    Category findByNameEn(String nameEn);

    // 暂时注释掉，等ColoringResource实体添加categoryId字段后再启用
    // @Query("SELECT c.id, c.name, c.nameEn, c.iconUrl, c.description, c.sortOrder, c.isActive, c.createdAt, " +
    //        "COUNT(r.id) as resourceCount " +
    //        "FROM Category c LEFT JOIN ColoringResource r ON c.id = r.categoryId " +
    //        "WHERE c.isActive = true " +
    //        "GROUP BY c.id " +
    //        "ORDER BY c.sortOrder ASC")
    // List<Object[]> findCategoriesWithResourceCount();
}
