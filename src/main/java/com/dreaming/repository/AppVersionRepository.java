package com.dreaming.repository;

import com.dreaming.entity.AppVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 应用版本数据访问层
 */
@Repository
public interface AppVersionRepository extends JpaRepository<AppVersion, Long> {

    /**
     * 获取当前版本
     */
    Optional<AppVersion> findByIsCurrentTrue();

    /**
     * 根据版本号查找
     */
    Optional<AppVersion> findByVersion(String version);

    /**
     * 获取最新版本
     */
    @Query("SELECT v FROM AppVersion v ORDER BY v.createdAt DESC")
    List<AppVersion> findLatestVersions();

    /**
     * 检查版本是否存在
     */
    boolean existsByVersion(String version);
}
