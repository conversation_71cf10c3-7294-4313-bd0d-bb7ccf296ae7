package com.dreaming.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 版本检查响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VersionCheckResponse {

    /**
     * 是否有更新
     */
    private Boolean hasUpdate;

    /**
     * 当前版本
     */
    private String currentVersion;

    /**
     * 最新版本
     */
    private String latestVersion;

    /**
     * 是否强制更新
     */
    private Boolean forceUpdate;

    /**
     * 更新描述
     */
    private String updateDescription;

    /**
     * 新增资源列表
     */
    private List<ResourceDTO> newResources;

    /**
     * 更新资源列表
     */
    private List<ResourceDTO> updatedResources;

    /**
     * 总资源数量
     */
    private Integer totalResourceCount;

    /**
     * 更新包大小
     */
    private Long updateSize;

    /**
     * 发布时间
     */
    private LocalDateTime releaseTime;
}
