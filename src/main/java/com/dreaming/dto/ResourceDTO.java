package com.dreaming.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 资源数据传输对象
 * 用于API响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResourceDTO {

    private Long id;
    private String name;
    private String description;
    private String category;
    private Integer difficulty;
    private String downloadUrl;
    private Long fileSize;
    private String contentType;
    private Integer width;
    private Integer height;
    private String version;
    private Boolean active;
    private Integer sortOrder;
    private Long downloadCount;
    private List<String> tags;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // 新增字段 - 涂色App专用
    private Long categoryId;
    private String categoryName;
    private String subcategory;
    private Integer complexityScore;
    private Integer estimatedTime; // 预计完成时间(分钟)
    private String previewImageUrl; // 预览图URL
    private String coloredSampleUrl; // 上色示例URL
    private String coloringDataUrl; // 涂色数据JSON文件URL
    private Integer regionsCount; // 涂色区域数量
    private Integer colorsCount; // 推荐颜色数量
    private String ageGroup; // 适合年龄段
    private String theme; // 主题
    private Long completionCount; // 完成次数
    private Double rating; // 评分
    private Integer ratingCount; // 评分人数
    private Boolean isPremium; // 是否付费内容
    private Boolean isFeatured; // 是否精选
    private String status; // 状态
}
