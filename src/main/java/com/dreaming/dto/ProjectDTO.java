package com.dreaming.dto;

import lombok.Data;
import java.util.List;

/**
 * 项目DTO - 客户端API使用
 */
@Data
public class ProjectDTO {
    private String id;
    private String name;
    private String displayName;
    private String description;
    private String category;
    private String difficulty;
    private Integer totalRegions;
    private Integer totalColors;
    private Integer estimatedTimeMinutes;
    private String version;
    private Long fileSize;
    private String thumbnailUrl;
    private String previewUrl;
    private List<String> tags;
    private String releaseDate;
    private Integer popularity;
    private Double rating;
    private Long downloadCount;
    private ProjectFilesDTO files;
}


