package com.dreaming.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 今日挑战DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TodayChallengeDTO {
    private String id;
    private String name;
    private String displayName;
    private String description;
    private String category;
    private String difficulty;
    private Integer totalRegions;
    private Integer estimatedTimeMinutes;
    private String thumbnailUrl;
    private String previewUrl;
    private List<String> tags;
    private String challengeReason;
}
