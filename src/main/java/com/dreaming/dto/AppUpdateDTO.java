package com.dreaming.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 应用更新信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppUpdateDTO {
    private String latestVersion;
    private String currentVersion;
    private String downloadUrl;
    private String releaseNotes;
    private Boolean forceUpdate;
    private String releaseDate;
    private Long fileSize;
    private String updateType; // "major", "minor", "patch"
}
