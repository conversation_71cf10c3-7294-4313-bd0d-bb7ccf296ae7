package com.dreaming.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 带项目数量的分类DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CategoryWithCountDTO {
    private String id;
    private String name;
    private String description;
    private String iconUrl;
    private Long projectCount;
    private Integer sortOrder;
}


