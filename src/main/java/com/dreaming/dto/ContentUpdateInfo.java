package com.dreaming.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

/**
 * 内容更新信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContentUpdateInfo {
    private Integer newProjectsCount;
    private Integer updatedProjectsCount;
    private LocalDateTime lastContentUpdate;
    private String updateSummary;
}
