package com.dreaming;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;

import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.InetAddress;

/**
 * Dreaming涂色App后端主应用类
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableScheduling
public class DreamingColoringApplication {

    @Autowired
    private Environment environment;

    public static void main(String[] args) {
        // 设置系统属性
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("spring.output.ansi.enabled", "ALWAYS");

        SpringApplication.run(DreamingColoringApplication.class, args);
    }

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        try {
            String port = environment.getProperty("server.port", "8081");
            String contextPath = environment.getProperty("server.servlet.context-path", "");
            String hostAddress = InetAddress.getLocalHost().getHostAddress();

            System.out.println("\n" +
                "╔══════════════════════════════════════════════════════════════╗\n" +
                "║                  🎨 Dreaming涂色App后端服务                  ║\n" +
                "║                        启动成功！                            ║\n" +
                "╠══════════════════════════════════════════════════════════════╣\n" +
                "║  📱 欢迎页面:    http://localhost:" + port + "                           ║\n" +
                "║  🎛️  管理后台:    http://localhost:" + port + "/admin                    ║\n" +
                "║  📚 API文档:     http://localhost:" + port + "/swagger-ui.html          ║\n" +
                "║  💾 数据库控制台: http://localhost:" + port + "/h2-console               ║\n" +
                "║  ❤️  健康检查:    http://localhost:" + port + "/api/health              ║\n" +
                "╠══════════════════════════════════════════════════════════════╣\n" +
                "║  🌐 局域网访问:   http://" + hostAddress + ":" + port + contextPath + "                ║\n" +
                "║  📁 文件上传目录: ./uploads                                   ║\n" +
                "║  📝 日志文件:     ./logs/dreaming-coloring.log               ║\n" +
                "╚══════════════════════════════════════════════════════════════╝\n" +
                "\n🎉 现在可以开始管理您的涂色资源了！\n");

        } catch (Exception e) {
            System.out.println("\n✅ Dreaming涂色App后端服务启动成功！");
            System.out.println("📱 访问地址: http://localhost:8080");
            System.out.println("🎛️ 管理后台: http://localhost:8080/admin\n");
        }
    }
}
