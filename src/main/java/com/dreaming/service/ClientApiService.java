package com.dreaming.service;

import com.dreaming.dto.*;

/**
 * 客户端API服务接口
 */
public interface ClientApiService {

    /**
     * 获取项目列表
     */
    ProjectListResponse getProjects(Integer page, Integer size, String category, 
                                  String difficulty, String search, String sort);

    /**
     * 获取项目详情
     */
    ProjectDTO getProjectDetail(String id);

    /**
     * 获取分类列表
     */
    CategoriesResponse getCategories();

    /**
     * 获取每日推荐
     */
    DailyResponse getDailyRecommendations();

    /**
     * 检查更新
     */
    UpdateResponse checkUpdates(String currentVersion, String lastContentUpdate);

    /**
     * 记录下载
     */
    void recordDownload(String id);
}
