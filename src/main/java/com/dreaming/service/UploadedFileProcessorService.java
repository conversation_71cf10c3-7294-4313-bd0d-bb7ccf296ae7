package com.dreaming.service;

import com.dreaming.entity.Category;
import com.dreaming.entity.ColoringResource;
import com.dreaming.repository.CategoryRepository;
import com.dreaming.repository.ColoringResourceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Stream;

/**
 * 已上传文件处理服务
 * 处理uploads目录中已存在但未创建数据库记录的文件
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UploadedFileProcessorService {

    private final ColoringResourceRepository resourceRepository;
    private final CategoryRepository categoryRepository;

    /**
     * 处理uploads目录中的所有文件
     */
    @Transactional
    public String processUploadedFiles() {
        try {
            log.info("开始处理uploads目录中的文件...");
            
            Path uploadsPath = Paths.get("uploads");
            if (!Files.exists(uploadsPath)) {
                return "uploads目录不存在";
            }
            
            int processedCount = 0;
            
            // 处理2025/07目录中的文件
            Path dateDir = uploadsPath.resolve("2025/07");
            if (Files.exists(dateDir)) {
                processedCount += processDateDirectory(dateDir);
            }
            
            log.info("文件处理完成，共处理 {} 个文件", processedCount);
            return String.format("成功处理 %d 个文件", processedCount);
            
        } catch (Exception e) {
            log.error("处理上传文件失败", e);
            return "处理失败: " + e.getMessage();
        }
    }

    /**
     * 处理日期目录中的文件
     */
    private int processDateDirectory(Path dateDir) throws IOException {
        int count = 0;
        
        try (Stream<Path> dayDirs = Files.list(dateDir)) {
            for (Path dayDir : dayDirs.toList()) {
                if (Files.isDirectory(dayDir)) {
                    count += processDayDirectory(dayDir);
                }
            }
        }
        
        return count;
    }

    /**
     * 处理具体日期目录中的文件
     */
    private int processDayDirectory(Path dayDir) throws IOException {
        int count = 0;
        
        try (Stream<Path> files = Files.list(dayDir)) {
            for (Path file : files.toList()) {
                if (Files.isRegularFile(file)) {
                    if (processUploadedFile(file)) {
                        count++;
                    }
                }
            }
        }
        
        return count;
    }

    /**
     * 处理单个上传的文件
     */
    private boolean processUploadedFile(Path filePath) {
        try {
            String filename = filePath.getFileName().toString();
            log.info("处理文件: {}", filename);
            
            // 只处理图片文件
            if (!isImageFile(filename)) {
                log.debug("跳过非图片文件: {}", filename);
                return false;
            }
            
            // 检查是否已经存在数据库记录
            // 将绝对路径转换为相对路径 (相对于uploads目录)
            String relativePath = getRelativePath(filePath);
            if (resourceRepository.existsByStoredFilename(relativePath)) {
                log.debug("文件已存在数据库记录: {}", filename);
                return false;
            }
            
            // 创建数据库记录
            ColoringResource resource = createResourceFromFile(filePath);
            resourceRepository.save(resource);
            
            log.info("成功为文件创建数据库记录: {} -> ID: {}", filename, resource.getId());
            return true;
            
        } catch (Exception e) {
            log.error("处理文件失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 获取相对于uploads目录的路径
     */
    private String getRelativePath(Path filePath) {
        // 将绝对路径转换为相对于uploads目录的路径
        Path uploadsPath = Paths.get("uploads").toAbsolutePath().normalize();
        Path absoluteFilePath = filePath.toAbsolutePath().normalize();

        if (absoluteFilePath.startsWith(uploadsPath)) {
            return uploadsPath.relativize(absoluteFilePath).toString().replace("\\", "/");
        } else {
            // 如果不在uploads目录下，直接返回文件名
            return filePath.toString().replace("\\", "/");
        }
    }

    /**
     * 从文件创建资源记录
     */
    private ColoringResource createResourceFromFile(Path filePath) throws IOException {
        String filename = filePath.getFileName().toString();
        String storedFilename = getRelativePath(filePath);
        
        ColoringResource resource = new ColoringResource();
        
        // 基础信息
        resource.setName(generateNameFromFilename(filename));
        resource.setDescription(generateDescriptionFromFilename(filename));
        
        // 分类信息
        CategoryInfo categoryInfo = determineCategoryFromFilename(filename);
        resource.setCategory(categoryInfo.getName());
        resource.setCategoryId(categoryInfo.getId());
        
        // 文件信息
        resource.setOriginalFilename(filename);
        resource.setStoredFilename(storedFilename);
        resource.setFilePath("/" + storedFilename);
        resource.setFileSize(Files.size(filePath));
        resource.setContentType(getContentType(filename));
        
        // 随机生成的属性
        Random random = new Random();
        resource.setDifficulty(1 + random.nextInt(5)); // 1-5
        resource.setEstimatedTime(10 + random.nextInt(50)); // 10-60分钟
        resource.setRegionsCount(5 + random.nextInt(20)); // 5-25个区域
        resource.setColorsCount(3 + random.nextInt(10)); // 3-12种颜色
        
        // 统计信息
        resource.setDownloadCount(0L);
        resource.setCompletionCount(0L);
        resource.setRating(3.0 + random.nextDouble() * 2.0); // 3.0-5.0
        resource.setRatingCount(0);
        
        // 状态
        resource.setActive(true);
        resource.setIsPremium(false);
        resource.setIsFeatured(false);
        resource.setStatus("ACTIVE");
        
        // 时间信息
        LocalDateTime now = LocalDateTime.now();
        resource.setCreatedAt(now);
        resource.setUpdatedAt(now);
        resource.setCreatedBy("system");
        resource.setUpdatedBy("system");
        
        // 版本
        resource.setVersion("1.0.0");
        
        // 额外信息
        resource.setAgeGroup(generateAgeGroup(resource.getDifficulty()));
        resource.setTheme(categoryInfo.getName());
        resource.setTags(generateTags(categoryInfo.getName()));
        
        return resource;
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String filename) {
        String lower = filename.toLowerCase();
        return lower.endsWith(".png") || lower.endsWith(".jpg") || 
               lower.endsWith(".jpeg") || lower.endsWith(".gif");
    }

    /**
     * 从文件名生成资源名称
     */
    private String generateNameFromFilename(String filename) {
        // 去除扩展名和时间戳
        String baseName = filename.replaceAll("\\.[^.]+$", ""); // 去除扩展名
        baseName = baseName.replaceAll("^\\d{8}_\\d{6}_[a-f0-9]+", ""); // 去除时间戳和UUID
        
        if (baseName.isEmpty()) {
            baseName = "上传的图片";
        }
        
        return "精美的" + baseName + "涂色作品";
    }

    /**
     * 从文件名生成描述
     */
    private String generateDescriptionFromFilename(String filename) {
        return "这是一个通过管理后台上传的精美涂色作品，适合各个年龄段的用户使用。";
    }

    /**
     * 从文件名确定分类
     */
    private CategoryInfo determineCategoryFromFilename(String filename) {
        // 默认分类
        List<Category> categories = categoryRepository.findAll();
        if (categories.isEmpty()) {
            return new CategoryInfo(1L, "其他");
        }
        
        // 简单的文件名匹配逻辑
        String lower = filename.toLowerCase();
        for (Category category : categories) {
            if (lower.contains(category.getNameEn().toLowerCase())) {
                return new CategoryInfo(category.getId(), category.getName());
            }
        }
        
        // 如果没有匹配，返回第一个分类
        Category firstCategory = categories.get(0);
        return new CategoryInfo(firstCategory.getId(), firstCategory.getName());
    }

    /**
     * 获取内容类型
     */
    private String getContentType(String filename) {
        String lower = filename.toLowerCase();
        if (lower.endsWith(".png")) return "image/png";
        if (lower.endsWith(".jpg") || lower.endsWith(".jpeg")) return "image/jpeg";
        if (lower.endsWith(".gif")) return "image/gif";
        return "image/png";
    }

    /**
     * 生成年龄组
     */
    private String generateAgeGroup(int difficulty) {
        switch (difficulty) {
            case 1: return "3-6岁";
            case 2: return "5-8岁";
            case 3: return "7-12岁";
            case 4: return "10-16岁";
            case 5: return "成人";
            default: return "全年龄";
        }
    }

    /**
     * 生成标签
     */
    private String generateTags(String categoryName) {
        return String.format("[\"%s\", \"涂色\", \"创意\", \"上传\"]", categoryName);
    }

    /**
     * 分类信息内部类
     */
    private static class CategoryInfo {
        private final Long id;
        private final String name;

        public CategoryInfo(Long id, String name) {
            this.id = id;
            this.name = name;
        }

        public Long getId() { return id; }
        public String getName() { return name; }
    }
}
