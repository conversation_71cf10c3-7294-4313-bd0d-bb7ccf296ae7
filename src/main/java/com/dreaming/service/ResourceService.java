package com.dreaming.service;

import com.dreaming.dto.ResourceDTO;
import com.dreaming.entity.ColoringResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 资源服务接口
 */
public interface ResourceService {

    /**
     * 上传资源 (原方法，保持向后兼容)
     */
    ResourceDTO uploadResource(MultipartFile file, String name, String description,
                              String category, Integer difficulty, String version);

    /**
     * 上传资源和JSON文件 (新方法)
     */
    ResourceDTO uploadResourceWithJson(MultipartFile imageFile, MultipartFile jsonFile, String name, String description,
                                     String category, Long categoryId, Integer difficulty, String version);

    /**
     * 批量上传资源 (支持文件夹)
     */
    int batchUploadResources(MultipartFile[] files, boolean autoCategory, Long defaultCategoryId,
                           String defaultCategory, Integer difficulty, String version);

    /**
     * 批量上传资源（带文件配对校验）
     */
    Map<String, Object> batchUploadResourcesWithValidation(MultipartFile[] files, Long categoryId,
                                                          Integer difficulty, String version);

    /**
     * 获取所有激活的资源
     */
    List<ResourceDTO> getAllActiveResources();

    /**
     * 分页获取资源
     */
    Page<ResourceDTO> getResourcesPage(Pageable pageable);

    /**
     * 根据ID获取资源
     */
    Optional<ResourceDTO> getResourceById(Long id);

    /**
     * 根据分类获取资源 (字符串分类，向后兼容)
     */
    List<ResourceDTO> getResourcesByCategory(String category);

    /**
     * 根据分类分页获取资源
     */
    Page<ResourceDTO> getResourcesByCategory(String category, Pageable pageable);

    /**
     * 根据分类ID获取资源 (新增)
     */
    List<ResourceDTO> getResourcesByCategoryId(Long categoryId);

    /**
     * 根据英文分类名获取资源
     */
    List<ResourceDTO> getResourcesByEnglishCategory(String categoryNameEn);

    /**
     * 根据难度获取资源
     */
    List<ResourceDTO> getResourcesByDifficulty(Integer difficulty);

    /**
     * 搜索资源
     */
    List<ResourceDTO> searchResources(String keyword);

    /**
     * 获取热门资源
     */
    List<ResourceDTO> getPopularResources(int limit);

    /**
     * 获取最新资源
     */
    List<ResourceDTO> getLatestResources(int limit);

    /**
     * 获取所有分类
     */
    List<String> getAllCategories();

    /**
     * 更新资源信息
     */
    ResourceDTO updateResource(Long id, String name, String description, 
                              String category, Integer difficulty);

    /**
     * 删除资源
     */
    void deleteResource(Long id);

    /**
     * 激活/停用资源
     */
    void toggleResourceStatus(Long id);

    /**
     * 增加下载次数
     */
    void incrementDownloadCount(Long id);

    /**
     * 根据版本获取资源
     */
    List<ResourceDTO> getResourcesByVersion(String version);

    /**
     * 实体转DTO
     */
    ResourceDTO convertToDTO(ColoringResource resource);

    /**
     * 获取资源统计信息
     */
    long getTotalResourceCount();
}
