package com.dreaming.service;

import com.dreaming.dto.VersionCheckResponse;
import com.dreaming.entity.AppVersion;

import java.util.List;
import java.util.Optional;

/**
 * 版本管理服务接口
 */
public interface VersionService {

    /**
     * 检查版本更新
     * @param currentVersion 客户端当前版本
     * @return 版本检查结果
     */
    VersionCheckResponse checkVersion(String currentVersion);

    /**
     * 创建新版本
     * @param version 版本号
     * @param versionName 版本名称
     * @param description 版本描述
     * @param forceUpdate 是否强制更新
     * @return 创建的版本
     */
    AppVersion createVersion(String version, String versionName, String description, boolean forceUpdate);

    /**
     * 设置当前版本
     * @param version 版本号
     */
    void setCurrentVersion(String version);

    /**
     * 获取当前版本
     * @return 当前版本
     */
    Optional<AppVersion> getCurrentVersion();

    /**
     * 获取所有版本
     * @return 版本列表
     */
    List<AppVersion> getAllVersions();

    /**
     * 根据版本号获取版本信息
     * @param version 版本号
     * @return 版本信息
     */
    Optional<AppVersion> getVersionByNumber(String version);

    /**
     * 删除版本
     * @param version 版本号
     */
    void deleteVersion(String version);

    /**
     * 更新版本统计信息
     * @param version 版本号
     */
    void updateVersionStats(String version);
}
