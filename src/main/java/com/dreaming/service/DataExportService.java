package com.dreaming.service;

import com.dreaming.dto.ResourceDTO;
import com.dreaming.entity.AppVersion;

import java.util.List;

/**
 * 数据导出服务接口
 */
public interface DataExportService {

    /**
     * 导出所有资源为JSON格式
     * @return JSON字符串
     */
    String exportResourcesAsJson();

    /**
     * 导出指定分类的资源为JSON格式
     * @param category 分类名称
     * @return JSON字符串
     */
    String exportResourcesByCategoryAsJson(String category);

    /**
     * 导出所有版本信息为JSON格式
     * @return JSON字符串
     */
    String exportVersionsAsJson();

    /**
     * 导出资源为CSV格式
     * @return CSV字符串
     */
    String exportResourcesAsCsv();

    /**
     * 生成资源统计报告
     * @return 统计报告JSON
     */
    String generateResourceReport();

    /**
     * 备份所有数据
     * @return 备份数据JSON
     */
    String backupAllData();
}
