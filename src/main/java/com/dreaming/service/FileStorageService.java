package com.dreaming.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * 文件存储服务接口
 */
public interface FileStorageService {

    /**
     * 存储文件
     * @param file 上传的文件
     * @return 存储后的文件名
     */
    String storeFile(MultipartFile file);

    /**
     * 存储文件（使用指定的标识符）
     * @param file 上传的文件
     * @param identifier 文件标识符（用于同一组文件）
     * @return 存储后的文件名
     */
    String storeFileWithIdentifier(MultipartFile file, String identifier);

    /**
     * 存储文件（使用指定的标识符和分类）
     * @param file 上传的文件
     * @param identifier 文件标识符（用于同一组文件）
     * @param category 文件分类
     * @return 存储后的文件名
     */
    String storeFileWithIdentifier(MultipartFile file, String identifier, String category);

    /**
     * 获取文件路径
     * @param filename 文件名
     * @return 文件完整路径
     */
    String getFilePath(String filename);

    /**
     * 获取文件访问URL
     * @param filename 文件名
     * @return 文件访问URL
     */
    String getFileUrl(String filename);

    /**
     * 删除文件
     * @param filename 文件名
     */
    void deleteFile(String filename);

    /**
     * 检查文件是否存在
     * @param filename 文件名
     * @return 是否存在
     */
    boolean fileExists(String filename);

    /**
     * 获取文件大小
     * @param filename 文件名
     * @return 文件大小(字节)
     */
    long getFileSize(String filename);
}
