package com.dreaming.service;

import com.dreaming.dto.CategoryDTO;
import com.dreaming.entity.Category;
import com.dreaming.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 分类服务层
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CategoryService {

    private final CategoryRepository categoryRepository;

    /**
     * 获取所有激活的分类
     */
    @Transactional(readOnly = true)
    public List<CategoryDTO> getAllActiveCategories() {
        log.info("获取所有激活分类");
        
        List<Category> categories = categoryRepository.findByIsActiveTrueOrderBySortOrderAsc();
        return categories.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 获取分类及其资源数量 (暂时返回普通分类列表)
     */
    @Transactional(readOnly = true)
    public List<CategoryDTO> getCategoriesWithResourceCount() {
        log.info("获取分类及资源数量");

        // 暂时返回普通分类列表，等ColoringResource实体扩展后再实现资源计数
        return getAllActiveCategories();
    }

    /**
     * 根据ID获取分类
     */
    @Transactional(readOnly = true)
    public Optional<CategoryDTO> getCategoryById(Long id) {
        log.info("根据ID获取分类: {}", id);
        
        return categoryRepository.findById(id)
                .map(this::convertToDTO);
    }

    /**
     * 创建分类
     */
    public CategoryDTO createCategory(CategoryDTO categoryDTO) {
        log.info("创建分类: {}", categoryDTO.getName());
        
        Category category = convertToEntity(categoryDTO);
        Category savedCategory = categoryRepository.save(category);
        
        log.info("分类创建成功，ID: {}", savedCategory.getId());
        return convertToDTO(savedCategory);
    }

    /**
     * 更新分类
     */
    public Optional<CategoryDTO> updateCategory(Long id, CategoryDTO categoryDTO) {
        log.info("更新分类: {}", id);
        
        return categoryRepository.findById(id)
                .map(existingCategory -> {
                    updateCategoryFields(existingCategory, categoryDTO);
                    Category savedCategory = categoryRepository.save(existingCategory);
                    log.info("分类更新成功: {}", id);
                    return convertToDTO(savedCategory);
                });
    }

    /**
     * 删除分类（软删除）
     */
    public boolean deleteCategory(Long id) {
        log.info("删除分类: {}", id);
        
        return categoryRepository.findById(id)
                .map(category -> {
                    category.setIsActive(false);
                    categoryRepository.save(category);
                    log.info("分类删除成功: {}", id);
                    return true;
                })
                .orElse(false);
    }

    /**
     * 转换为DTO
     * 优先使用英文名称以便于客户端通信
     */
    private CategoryDTO convertToDTO(Category category) {
        CategoryDTO dto = new CategoryDTO();
        dto.setId(category.getId());
        // 优先使用英文名称，如果没有英文名称则使用中文名称
        dto.setName(category.getNameEn() != null && !category.getNameEn().trim().isEmpty()
                    ? category.getNameEn() : category.getName());
        dto.setNameEn(category.getNameEn());
        dto.setIconUrl(category.getIconUrl());
        dto.setDescription(category.getDescription());
        dto.setSortOrder(category.getSortOrder());
        dto.setIsActive(category.getIsActive());
        dto.setCreatedAt(category.getCreatedAt());
        return dto;
    }

    /**
     * 转换为带资源数量的DTO
     */
    private CategoryDTO convertToDTOWithCount(Object[] result) {
        CategoryDTO dto = new CategoryDTO();
        dto.setId((Long) result[0]);
        dto.setName((String) result[1]);
        dto.setNameEn((String) result[2]);
        dto.setIconUrl((String) result[3]);
        dto.setDescription((String) result[4]);
        dto.setSortOrder((Integer) result[5]);
        dto.setIsActive((Boolean) result[6]);
        dto.setCreatedAt((java.time.LocalDateTime) result[7]);
        dto.setResourceCount((Long) result[8]);
        return dto;
    }

    /**
     * 转换为实体
     */
    private Category convertToEntity(CategoryDTO dto) {
        Category category = new Category();
        category.setName(dto.getName());
        category.setNameEn(dto.getNameEn());
        category.setIconUrl(dto.getIconUrl());
        category.setDescription(dto.getDescription());
        category.setSortOrder(dto.getSortOrder());
        category.setIsActive(dto.getIsActive());
        return category;
    }

    /**
     * 更新分类字段
     */
    private void updateCategoryFields(Category category, CategoryDTO dto) {
        if (dto.getName() != null) {
            category.setName(dto.getName());
        }
        if (dto.getNameEn() != null) {
            category.setNameEn(dto.getNameEn());
        }
        if (dto.getIconUrl() != null) {
            category.setIconUrl(dto.getIconUrl());
        }
        if (dto.getDescription() != null) {
            category.setDescription(dto.getDescription());
        }
        if (dto.getSortOrder() != null) {
            category.setSortOrder(dto.getSortOrder());
        }
        if (dto.getIsActive() != null) {
            category.setIsActive(dto.getIsActive());
        }
    }
}
