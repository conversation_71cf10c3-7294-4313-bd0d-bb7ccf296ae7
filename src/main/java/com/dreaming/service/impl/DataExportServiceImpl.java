package com.dreaming.service.impl;

import com.dreaming.dto.ResourceDTO;
import com.dreaming.entity.AppVersion;
import com.dreaming.service.DataExportService;
import com.dreaming.service.ResourceService;
import com.dreaming.service.VersionService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据导出服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DataExportServiceImpl implements DataExportService {

    private final ResourceService resourceService;
    private final VersionService versionService;
    private final ObjectMapper objectMapper;

    @Override
    public String exportResourcesAsJson() {
        try {
            List<ResourceDTO> resources = resourceService.getAllActiveResources();
            
            Map<String, Object> exportData = new HashMap<>();
            exportData.put("exportTime", LocalDateTime.now());
            exportData.put("totalCount", resources.size());
            exportData.put("resources", resources);
            
            return objectMapper.writeValueAsString(exportData);
        } catch (Exception e) {
            log.error("导出资源JSON失败", e);
            throw new RuntimeException("导出资源失败: " + e.getMessage());
        }
    }

    @Override
    public String exportResourcesByCategoryAsJson(String category) {
        try {
            List<ResourceDTO> resources = resourceService.getResourcesByCategory(category);
            
            Map<String, Object> exportData = new HashMap<>();
            exportData.put("exportTime", LocalDateTime.now());
            exportData.put("category", category);
            exportData.put("totalCount", resources.size());
            exportData.put("resources", resources);
            
            return objectMapper.writeValueAsString(exportData);
        } catch (Exception e) {
            log.error("导出分类资源JSON失败: {}", category, e);
            throw new RuntimeException("导出分类资源失败: " + e.getMessage());
        }
    }

    @Override
    public String exportVersionsAsJson() {
        try {
            List<AppVersion> versions = versionService.getAllVersions();
            
            Map<String, Object> exportData = new HashMap<>();
            exportData.put("exportTime", LocalDateTime.now());
            exportData.put("totalCount", versions.size());
            exportData.put("currentVersion", versionService.getCurrentVersion().orElse(null));
            exportData.put("versions", versions);
            
            return objectMapper.writeValueAsString(exportData);
        } catch (Exception e) {
            log.error("导出版本JSON失败", e);
            throw new RuntimeException("导出版本失败: " + e.getMessage());
        }
    }

    @Override
    public String exportResourcesAsCsv() {
        try {
            List<ResourceDTO> resources = resourceService.getAllActiveResources();
            
            StringBuilder csv = new StringBuilder();
            // CSV头部
            csv.append("ID,名称,描述,分类,难度,版本,文件大小(KB),下载次数,创建时间,更新时间\n");
            
            // CSV数据
            for (ResourceDTO resource : resources) {
                csv.append(resource.getId()).append(",")
                   .append(escapeCsv(resource.getName())).append(",")
                   .append(escapeCsv(resource.getDescription())).append(",")
                   .append(escapeCsv(resource.getCategory())).append(",")
                   .append(resource.getDifficulty()).append(",")
                   .append(escapeCsv(resource.getVersion())).append(",")
                   .append(resource.getFileSize() / 1024).append(",")
                   .append(resource.getDownloadCount()).append(",")
                   .append(resource.getCreatedAt()).append(",")
                   .append(resource.getUpdatedAt()).append("\n");
            }
            
            return csv.toString();
        } catch (Exception e) {
            log.error("导出资源CSV失败", e);
            throw new RuntimeException("导出CSV失败: " + e.getMessage());
        }
    }

    @Override
    public String generateResourceReport() {
        try {
            List<ResourceDTO> resources = resourceService.getAllActiveResources();
            List<String> categories = resourceService.getAllCategories();
            
            // 按分类统计
            Map<String, Long> categoryStats = resources.stream()
                    .collect(Collectors.groupingBy(
                            ResourceDTO::getCategory,
                            Collectors.counting()
                    ));
            
            // 按难度统计
            Map<Integer, Long> difficultyStats = resources.stream()
                    .collect(Collectors.groupingBy(
                            ResourceDTO::getDifficulty,
                            Collectors.counting()
                    ));
            
            // 按版本统计
            Map<String, Long> versionStats = resources.stream()
                    .collect(Collectors.groupingBy(
                            ResourceDTO::getVersion,
                            Collectors.counting()
                    ));
            
            // 下载统计
            long totalDownloads = resources.stream()
                    .mapToLong(ResourceDTO::getDownloadCount)
                    .sum();
            
            double avgDownloads = resources.stream()
                    .mapToLong(ResourceDTO::getDownloadCount)
                    .average()
                    .orElse(0.0);
            
            // 文件大小统计
            long totalSize = resources.stream()
                    .mapToLong(ResourceDTO::getFileSize)
                    .sum();
            
            double avgSize = resources.stream()
                    .mapToLong(ResourceDTO::getFileSize)
                    .average()
                    .orElse(0.0);
            
            Map<String, Object> report = new HashMap<>();
            report.put("reportTime", LocalDateTime.now());
            report.put("summary", Map.of(
                    "totalResources", resources.size(),
                    "totalCategories", categories.size(),
                    "totalDownloads", totalDownloads,
                    "avgDownloads", Math.round(avgDownloads * 100.0) / 100.0,
                    "totalSize", totalSize,
                    "avgSize", Math.round(avgSize * 100.0) / 100.0
            ));
            report.put("categoryStats", categoryStats);
            report.put("difficultyStats", difficultyStats);
            report.put("versionStats", versionStats);
            
            // 热门资源
            List<ResourceDTO> popularResources = resourceService.getPopularResources(10);
            report.put("popularResources", popularResources);
            
            return objectMapper.writeValueAsString(report);
        } catch (Exception e) {
            log.error("生成资源报告失败", e);
            throw new RuntimeException("生成报告失败: " + e.getMessage());
        }
    }

    @Override
    public String backupAllData() {
        try {
            Map<String, Object> backup = new HashMap<>();
            backup.put("backupTime", LocalDateTime.now());
            backup.put("version", "1.0.0");
            
            // 备份资源
            List<ResourceDTO> resources = resourceService.getAllActiveResources();
            backup.put("resources", resources);
            
            // 备份版本
            List<AppVersion> versions = versionService.getAllVersions();
            backup.put("versions", versions);
            backup.put("currentVersion", versionService.getCurrentVersion().orElse(null));
            
            // 备份统计信息
            backup.put("statistics", Map.of(
                    "totalResources", resources.size(),
                    "totalVersions", versions.size(),
                    "categories", resourceService.getAllCategories()
            ));
            
            return objectMapper.writeValueAsString(backup);
        } catch (Exception e) {
            log.error("备份数据失败", e);
            throw new RuntimeException("备份失败: " + e.getMessage());
        }
    }

    /**
     * 转义CSV字段
     */
    private String escapeCsv(String value) {
        if (value == null) {
            return "";
        }
        
        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        
        return value;
    }
}
