package com.dreaming.service.impl;

import com.dreaming.dto.ResourceDTO;
import com.dreaming.dto.VersionCheckResponse;
import com.dreaming.entity.AppVersion;
import com.dreaming.repository.AppVersionRepository;
import com.dreaming.repository.ColoringResourceRepository;
import com.dreaming.service.ResourceService;
import com.dreaming.service.VersionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 版本管理服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class VersionServiceImpl implements VersionService {

    private final AppVersionRepository versionRepository;
    private final ColoringResourceRepository resourceRepository;
    private final ResourceService resourceService;

    @Override
    @Transactional(readOnly = true)
    public VersionCheckResponse checkVersion(String currentVersion) {
        log.info("检查版本更新: 当前版本 {}", currentVersion);

        VersionCheckResponse response = new VersionCheckResponse();
        response.setCurrentVersion(currentVersion);

        // 获取最新版本
        Optional<AppVersion> latestVersionOpt = getCurrentVersion();
        if (latestVersionOpt.isEmpty()) {
            log.warn("未找到当前版本信息");
            response.setHasUpdate(false);
            return response;
        }

        AppVersion latestVersion = latestVersionOpt.get();
        response.setLatestVersion(latestVersion.getVersion());
        response.setForceUpdate(latestVersion.getForceUpdate());
        response.setUpdateDescription(latestVersion.getDescription());
        response.setReleaseTime(latestVersion.getReleaseTime());

        // 比较版本
        boolean hasUpdate = !currentVersion.equals(latestVersion.getVersion());
        response.setHasUpdate(hasUpdate);

        if (hasUpdate) {
            // 获取新版本的资源
            List<ResourceDTO> newResources = resourceService.getResourcesByVersion(latestVersion.getVersion());
            response.setNewResources(newResources);
            
            // 计算更新包大小
            long updateSize = newResources.stream()
                    .mapToLong(ResourceDTO::getFileSize)
                    .sum();
            response.setUpdateSize(updateSize);
        }

        // 获取总资源数量
        response.setTotalResourceCount((int) resourceService.getTotalResourceCount());

        log.info("版本检查完成: 有更新={}, 最新版本={}", hasUpdate, latestVersion.getVersion());
        return response;
    }

    @Override
    public AppVersion createVersion(String version, String versionName, String description, boolean forceUpdate) {
        log.info("创建新版本: {}", version);

        // 检查版本是否已存在
        if (versionRepository.existsByVersion(version)) {
            throw new RuntimeException("版本已存在: " + version);
        }

        AppVersion appVersion = new AppVersion();
        appVersion.setVersion(version);
        appVersion.setVersionName(versionName);
        appVersion.setDescription(description);
        appVersion.setForceUpdate(forceUpdate);
        appVersion.setReleaseTime(LocalDateTime.now());
        appVersion.setCreatedBy("admin");

        // 更新版本统计信息
        updateVersionStatsInternal(appVersion);

        AppVersion savedVersion = versionRepository.save(appVersion);
        log.info("版本创建成功: {}", version);
        
        return savedVersion;
    }

    @Override
    public void setCurrentVersion(String version) {
        log.info("设置当前版本: {}", version);

        AppVersion targetVersion = versionRepository.findByVersion(version)
                .orElseThrow(() -> new RuntimeException("版本不存在: " + version));

        // 取消所有版本的当前状态
        List<AppVersion> allVersions = versionRepository.findAll();
        allVersions.forEach(v -> v.setIsCurrent(false));
        versionRepository.saveAll(allVersions);

        // 设置目标版本为当前版本
        targetVersion.setIsCurrent(true);
        versionRepository.save(targetVersion);

        log.info("当前版本设置成功: {}", version);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AppVersion> getCurrentVersion() {
        return versionRepository.findByIsCurrentTrue();
    }

    @Override
    @Transactional(readOnly = true)
    public List<AppVersion> getAllVersions() {
        return versionRepository.findLatestVersions();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AppVersion> getVersionByNumber(String version) {
        return versionRepository.findByVersion(version);
    }

    @Override
    public void deleteVersion(String version) {
        log.info("删除版本: {}", version);

        AppVersion appVersion = versionRepository.findByVersion(version)
                .orElseThrow(() -> new RuntimeException("版本不存在: " + version));

        if (appVersion.getIsCurrent()) {
            throw new RuntimeException("不能删除当前版本: " + version);
        }

        versionRepository.delete(appVersion);
        log.info("版本删除成功: {}", version);
    }

    @Override
    public void updateVersionStats(String version) {
        log.info("更新版本统计信息: {}", version);

        AppVersion appVersion = versionRepository.findByVersion(version)
                .orElseThrow(() -> new RuntimeException("版本不存在: " + version));

        updateVersionStatsInternal(appVersion);
        versionRepository.save(appVersion);

        log.info("版本统计信息更新完成: {}", version);
    }

    /**
     * 内部方法：更新版本统计信息
     */
    private void updateVersionStatsInternal(AppVersion appVersion) {
        String version = appVersion.getVersion();
        
        // 统计该版本的资源数量
        long resourceCount = resourceRepository.countByVersionAndActiveTrue(version);
        appVersion.setResourceCount((int) resourceCount);

        // 计算该版本的总大小
        List<ResourceDTO> resources = resourceService.getResourcesByVersion(version);
        long totalSize = resources.stream()
                .mapToLong(ResourceDTO::getFileSize)
                .sum();
        appVersion.setTotalSize(totalSize);
    }
}
