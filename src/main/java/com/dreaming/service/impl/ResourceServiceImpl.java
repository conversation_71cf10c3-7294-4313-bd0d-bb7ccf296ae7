package com.dreaming.service.impl;

import com.dreaming.dto.ResourceDTO;
import com.dreaming.entity.ColoringResource;
import com.dreaming.entity.Category;
import com.dreaming.repository.ColoringResourceRepository;
import com.dreaming.repository.CategoryRepository;
import com.dreaming.service.ResourceService;
import com.dreaming.service.FileStorageService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资源服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ResourceServiceImpl implements ResourceService {

    private final ColoringResourceRepository resourceRepository;
    private final CategoryRepository categoryRepository;
    private final FileStorageService fileStorageService;
    private final ObjectMapper objectMapper;

    @Override
    public ResourceDTO uploadResource(MultipartFile file, String name, String description,
                                    String category, Integer difficulty, String version) {
        try {
            log.info("开始上传资源: {}", name);

            // 确定用于文件存储的分类名称
            String storageCategory = category;
            Category categoryEntity = null;
            
            // 首先尝试按英文名称查找（因为API返回的是英文名称）
            categoryEntity = categoryRepository.findByNameEn(category);
            if (categoryEntity == null) {
                // 如果没找到，尝试按中文名称查找
                categoryEntity = categoryRepository.findByName(category);
            }
            
            if (categoryEntity != null && categoryEntity.getNameEn() != null && !categoryEntity.getNameEn().trim().isEmpty()) {
                storageCategory = categoryEntity.getNameEn();
                log.info("找到分类实体，使用英文分类名进行文件存储: {} -> {}", category, storageCategory);
            } else {
                log.warn("未找到分类信息: {}, 使用原分类名", category);
                // 如果找不到分类实体，直接使用传入的分类名
                storageCategory = category;
            }

            // 存储文件（使用分类信息）
            String identifier = java.util.UUID.randomUUID().toString().substring(0, 8);
            String storedFilename = fileStorageService.storeFileWithIdentifier(file, identifier, storageCategory);
            String filePath = fileStorageService.getFilePath(storedFilename);
            
            // 创建资源实体
            ColoringResource resource = new ColoringResource();
            resource.setName(name);
            resource.setDescription(description);
            resource.setCategory(category);
            resource.setDifficulty(difficulty);
            resource.setVersion(version);
            resource.setOriginalFilename(file.getOriginalFilename());
            resource.setStoredFilename(storedFilename);
            resource.setFilePath(filePath);
            resource.setFileSize(file.getSize());
            resource.setContentType(file.getContentType());
            resource.setActive(true);
            resource.setCreatedBy("admin"); // 后续可以从认证信息中获取
            
            // 保存到数据库
            ColoringResource savedResource = resourceRepository.save(resource);
            
            log.info("资源上传成功: {}, ID: {}", name, savedResource.getId());
            return convertToDTO(savedResource);
            
        } catch (Exception e) {
            log.error("上传资源失败: {}", name, e);
            throw new RuntimeException("上传资源失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ResourceDTO uploadResourceWithJson(MultipartFile imageFile, MultipartFile jsonFile, String name, String description,
                                            String category, Long categoryId, Integer difficulty, String version) {
        log.info("开始上传资源和JSON文件: {}", name);

        try {
            // 生成基于原始文件名的标识符（去除扩展名）
            String originalName = imageFile.getOriginalFilename();
            String baseName = originalName.substring(0, originalName.lastIndexOf('.'));
            // 清理文件名，移除特殊字符
            String cleanBaseName = baseName.replaceAll("[^a-zA-Z0-9_-]", "_");

            // 检查是否已存在相同名称的资源
            List<ColoringResource> existingResources = resourceRepository.findByNameAndActiveTrue(name);
            if (!existingResources.isEmpty()) {
                log.warn("发现重复资源: {}, 跳过上传", name);
                throw new RuntimeException("资源已存在: " + name);
            }

            // 确定用于文件存储的分类名称（优先使用英文名称）
            String storageCategory = category;
            if (categoryId != null) {
                Category categoryEntity = categoryRepository.findById(categoryId).orElse(null);
                if (categoryEntity != null && categoryEntity.getNameEn() != null && !categoryEntity.getNameEn().trim().isEmpty()) {
                    storageCategory = categoryEntity.getNameEn();
                    log.info("使用英文分类名进行文件存储: {}", storageCategory);
                }
            }

            // 保存图片文件
            String storedImageFilename = fileStorageService.storeFileWithIdentifier(imageFile, cleanBaseName, storageCategory);
            String imageFilePath = fileStorageService.getFilePath(storedImageFilename);

            // 创建资源实体
            ColoringResource resource = new ColoringResource();
            resource.setName(name);
            resource.setDescription(description);
            resource.setCategory(category);
            resource.setCategoryId(categoryId);
            resource.setDifficulty(difficulty);
            resource.setVersion(version);
            resource.setOriginalFilename(imageFile.getOriginalFilename());
            resource.setStoredFilename(storedImageFilename);
            resource.setFilePath(imageFilePath);
            resource.setFileSize(imageFile.getSize());
            resource.setContentType(imageFile.getContentType());
            resource.setActive(true);
            resource.setDownloadCount(0L);
            resource.setCompletionCount(0L);
            resource.setRating(0.0);
            resource.setRatingCount(0);
            resource.setIsPremium(false);
            resource.setIsFeatured(false);
            resource.setStatus("ACTIVE");
            resource.setCreatedBy("admin");

            // 处理JSON文件
            if (jsonFile != null && !jsonFile.isEmpty()) {
                String storedJsonFilename = fileStorageService.storeFileWithIdentifier(jsonFile, cleanBaseName, storageCategory);
                // 使用正确的URL格式，与getFileUrl方法保持一致：/api/files/path
                // storedJsonFilename已经包含了category/filename格式
                resource.setColoringDataUrl("/api/files/" + storedJsonFilename);

                // 解析JSON文件获取元数据
                try {
                    String jsonContent = new String(jsonFile.getBytes(), StandardCharsets.UTF_8);
                    JsonNode jsonData = objectMapper.readTree(jsonContent);

                    // 提取元数据
                    if (jsonData.has("meta")) {
                        JsonNode meta = jsonData.get("meta");
                        if (meta.has("regions")) {
                            resource.setRegionsCount(meta.get("regions").asInt());
                        }
                        if (meta.has("colors")) {
                            resource.setColorsCount(meta.get("colors").asInt());
                        }
                        if (meta.has("size") && meta.get("size").isArray()) {
                            resource.setWidth(meta.get("size").get(0).asInt());
                            resource.setHeight(meta.get("size").get(1).asInt());
                        }
                        if (meta.has("estimated_time")) {
                            resource.setEstimatedTime(meta.get("estimated_time").asInt());
                        }
                    }

                    // 提取标签
                    if (jsonData.has("tags") && jsonData.get("tags").isArray()) {
                        List<String> tags = new ArrayList<>();
                        jsonData.get("tags").forEach(tag -> tags.add(tag.asText()));
                        resource.setTags(objectMapper.writeValueAsString(tags));
                    }

                    // 提取年龄段和主题
                    if (jsonData.has("age_group")) {
                        resource.setAgeGroup(jsonData.get("age_group").asText());
                    }
                    if (jsonData.has("theme")) {
                        resource.setTheme(jsonData.get("theme").asText());
                    }

                    log.info("JSON文件解析成功，提取到 {} 个区域，{} 种颜色",
                            resource.getRegionsCount(), resource.getColorsCount());

                } catch (Exception e) {
                    log.warn("解析JSON文件失败，但继续保存资源: {}", e.getMessage());
                }
            }

            // 保存到数据库
            ColoringResource savedResource = resourceRepository.save(resource);

            log.info("资源和JSON文件上传成功: {} (ID: {})", savedResource.getName(), savedResource.getId());
            return convertToDTO(savedResource);

        } catch (Exception e) {
            log.error("上传资源和JSON文件失败: {}", name, e);
            throw new RuntimeException("上传资源失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public int batchUploadResources(MultipartFile[] files, boolean autoCategory, Long defaultCategoryId,
                                  String defaultCategory, Integer difficulty, String version) {
        log.info("开始批量上传资源: {} 个文件", files.length);

        int successCount = 0;
        Map<String, List<MultipartFile>> groupedFiles = groupFilesByName(files);

        for (Map.Entry<String, List<MultipartFile>> entry : groupedFiles.entrySet()) {
            String baseName = entry.getKey();
            List<MultipartFile> fileGroup = entry.getValue();

            try {
                MultipartFile imageFile = null;
                MultipartFile jsonFile = null;

                // 分离图片和JSON文件
                for (MultipartFile file : fileGroup) {
                    String filename = file.getOriginalFilename().toLowerCase();
                    if (filename.endsWith(".png") || filename.endsWith(".jpg") || filename.endsWith(".jpeg") || filename.endsWith(".gif")) {
                        imageFile = file;
                    } else if (filename.endsWith(".json")) {
                        jsonFile = file;
                    }
                }

                if (imageFile != null) {
                    // 自动分类逻辑
                    Long categoryId = defaultCategoryId;
                    String category = defaultCategory;

                    if (autoCategory) {
                        CategoryInfo categoryInfo = determineCategoryFromFilename(baseName);
                        categoryId = categoryInfo.getId();
                        category = categoryInfo.getName();
                    }

                    // 生成资源名称和描述
                    String resourceName = generateResourceName(baseName);
                    String description = generateResourceDescription(baseName, categoryId);

                    // 上传资源
                    uploadResourceWithJson(imageFile, jsonFile, resourceName, description,
                                         category, categoryId, difficulty, version);

                    successCount++;
                    log.info("成功处理资源组: {} (图片: {}, JSON: {})",
                            baseName, imageFile.getOriginalFilename(),
                            jsonFile != null ? jsonFile.getOriginalFilename() : "无");
                }
            } catch (Exception e) {
                log.error("处理资源组失败: {}", baseName, e);
            }
        }

        log.info("批量上传完成: 成功 {} 个，总计 {} 组", successCount, groupedFiles.size());
        return successCount;
    }

    /**
     * 按文件名基础部分分组文件
     */
    private Map<String, List<MultipartFile>> groupFilesByName(MultipartFile[] files) {
        Map<String, List<MultipartFile>> grouped = new HashMap<>();

        for (MultipartFile file : files) {
            String filename = file.getOriginalFilename();
            if (filename == null) continue;

            // 提取基础文件名（去除扩展名和特殊后缀）
            String baseName = extractBaseName(filename);

            grouped.computeIfAbsent(baseName, k -> new ArrayList<>()).add(file);
        }

        return grouped;
    }

    /**
     * 提取文件的基础名称
     */
    private String extractBaseName(String filename) {
        // 去除扩展名
        int lastDot = filename.lastIndexOf('.');
        String nameWithoutExt = lastDot > 0 ? filename.substring(0, lastDot) : filename;

        // 去除常见后缀
        nameWithoutExt = nameWithoutExt.replaceAll("_outline$", "")
                                     .replaceAll("_coloring$", "")
                                     .replaceAll("_data$", "")
                                     .replaceAll("-outline$", "")
                                     .replaceAll("-coloring$", "")
                                     .replaceAll("-data$", "");

        return nameWithoutExt;
    }

    /**
     * 根据文件名自动确定分类
     */
    private CategoryInfo determineCategoryFromFilename(String filename) {
        String lowerName = filename.toLowerCase();

        // 动物类关键词
        if (lowerName.contains("cat") || lowerName.contains("dog") || lowerName.contains("animal") ||
            lowerName.contains("猫") || lowerName.contains("狗") || lowerName.contains("动物")) {
            return new CategoryInfo(5L, "动物世界");
        }

        // 植物类关键词
        if (lowerName.contains("flower") || lowerName.contains("plant") || lowerName.contains("tree") ||
            lowerName.contains("花") || lowerName.contains("植物") || lowerName.contains("树")) {
            return new CategoryInfo(6L, "植物花卉");
        }

        // 房屋建筑类关键词
        if (lowerName.contains("house") || lowerName.contains("building") || lowerName.contains("home") ||
            lowerName.contains("房") || lowerName.contains("屋") || lowerName.contains("建筑")) {
            return new CategoryInfo(1L, "房屋建筑");
        }

        // 交通工具类关键词
        if (lowerName.contains("car") || lowerName.contains("vehicle") || lowerName.contains("truck") ||
            lowerName.contains("汽车") || lowerName.contains("车") || lowerName.contains("交通")) {
            return new CategoryInfo(8L, "交通工具");
        }

        // 卡通人物类关键词
        if (lowerName.contains("cartoon") || lowerName.contains("character") || lowerName.contains("人物") ||
            lowerName.contains("卡通")) {
            return new CategoryInfo(7L, "卡通人物");
        }

        // 默认分类
        return new CategoryInfo(1L, "房屋建筑");
    }

    /**
     * 生成资源名称
     */
    private String generateResourceName(String baseName) {
        // 将下划线和连字符替换为空格，并进行首字母大写
        return Arrays.stream(baseName.split("[_-]"))
                    .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase())
                    .collect(Collectors.joining(" "));
    }

    /**
     * 生成资源描述
     */
    private String generateResourceDescription(String baseName, Long categoryId) {
        String categoryName = getCategoryNameById(categoryId);
        return String.format("来自批量上传的%s主题涂色资源", categoryName);
    }

    /**
     * 根据分类ID获取分类名称
     */
    private String getCategoryNameById(Long categoryId) {
        if (categoryId == null) return "未分类";

        return categoryRepository.findById(categoryId)
                .map(Category::getName)
                .orElse("未分类");
    }

    /**
     * 分类信息内部类
     */
    private static class CategoryInfo {
        private final Long id;
        private final String name;

        public CategoryInfo(Long id, String name) {
            this.id = id;
            this.name = name;
        }

        public Long getId() { return id; }
        public String getName() { return name; }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ResourceDTO> getAllActiveResources() {
        List<ColoringResource> resources = resourceRepository.findByActiveTrueOrderBySortOrderAsc();
        return resources.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ResourceDTO> getResourcesPage(Pageable pageable) {
        Page<ColoringResource> resourcePage = resourceRepository.findByActiveTrue(pageable);
        return resourcePage.map(this::convertToDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ResourceDTO> getResourceById(Long id) {
        return resourceRepository.findById(id)
                .map(this::convertToDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ResourceDTO> getResourcesByCategory(String category) {
        List<ColoringResource> resources = resourceRepository.findByCategoryAndActiveTrue(category);
        return resources.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ResourceDTO> getResourcesByCategory(String category, Pageable pageable) {
        Page<ColoringResource> resourcePage = resourceRepository.findByCategoryAndActiveTrue(category, pageable);
        return resourcePage.map(this::convertToDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ResourceDTO> getResourcesByCategoryId(Long categoryId) {
        List<ColoringResource> resources = resourceRepository.findByCategoryIdAndActiveTrue(categoryId);
        return resources.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ResourceDTO> getResourcesByEnglishCategory(String categoryNameEn) {
        log.info("根据英文分类名获取资源: {}", categoryNameEn);

        // 首先通过英文名称查找分类
        Category category = categoryRepository.findByNameEn(categoryNameEn);
        if (category != null) {
            // 使用分类ID查找资源
            List<ColoringResource> resources = resourceRepository.findByCategoryIdAndActiveTrue(category.getId());
            log.info("通过英文分类名 {} 找到 {} 个资源", categoryNameEn, resources.size());
            return resources.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } else {
            // 如果没有找到对应的分类，尝试直接用字符串匹配（向后兼容）
            List<ColoringResource> resources = resourceRepository.findByCategoryAndActiveTrue(categoryNameEn);
            log.info("通过字符串匹配分类名 {} 找到 {} 个资源", categoryNameEn, resources.size());
            return resources.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ResourceDTO> getResourcesByDifficulty(Integer difficulty) {
        List<ColoringResource> resources = resourceRepository.findByDifficultyAndActiveTrue(difficulty);
        return resources.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ResourceDTO> searchResources(String keyword) {
        List<ColoringResource> resources = resourceRepository.findByNameContainingAndActiveTrue(keyword);
        return resources.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ResourceDTO> getPopularResources(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<ColoringResource> resources = resourceRepository.findPopularResources(pageable);
        return resources.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ResourceDTO> getLatestResources(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<ColoringResource> resources = resourceRepository.findLatestResources(pageable);
        return resources.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getAllCategories() {
        // 获取所有激活的分类，优先返回英文名称
        List<Category> categories = categoryRepository.findByIsActiveTrueOrderBySortOrderAsc();
        return categories.stream()
                .map(category -> category.getNameEn() != null && !category.getNameEn().trim().isEmpty()
                               ? category.getNameEn() : category.getName())
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public ResourceDTO updateResource(Long id, String name, String description,
                                    String category, Integer difficulty) {
        ColoringResource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("资源不存在: " + id));
        
        resource.setName(name);
        resource.setDescription(description);
        resource.setCategory(category);
        resource.setDifficulty(difficulty);
        resource.setUpdatedBy("admin");
        
        ColoringResource updatedResource = resourceRepository.save(resource);
        return convertToDTO(updatedResource);
    }

    @Override
    public void deleteResource(Long id) {
        ColoringResource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("资源不存在: " + id));
        
        // 删除文件
        fileStorageService.deleteFile(resource.getStoredFilename());
        
        // 删除数据库记录
        resourceRepository.delete(resource);
        
        log.info("删除资源成功: {}", resource.getName());
    }

    @Override
    public void toggleResourceStatus(Long id) {
        ColoringResource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("资源不存在: " + id));
        
        resource.setActive(!resource.getActive());
        resourceRepository.save(resource);
        
        log.info("切换资源状态: {}, 新状态: {}", resource.getName(), resource.getActive());
    }

    @Override
    public void incrementDownloadCount(Long id) {
        ColoringResource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("资源不存在: " + id));
        
        resource.setDownloadCount(resource.getDownloadCount() + 1);
        resourceRepository.save(resource);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ResourceDTO> getResourcesByVersion(String version) {
        List<ColoringResource> resources = resourceRepository.findByVersionAndActiveTrue(version);
        return resources.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public ResourceDTO convertToDTO(ColoringResource resource) {
        ResourceDTO dto = new ResourceDTO();
        dto.setId(resource.getId());
        dto.setName(resource.getName());
        dto.setDescription(resource.getDescription());
        dto.setCategory(resource.getCategory());
        dto.setDifficulty(resource.getDifficulty());
        dto.setDownloadUrl(fileStorageService.getFileUrl(resource.getStoredFilename()));
        dto.setFileSize(resource.getFileSize());
        dto.setContentType(resource.getContentType());
        dto.setWidth(resource.getWidth());
        dto.setHeight(resource.getHeight());
        dto.setVersion(resource.getVersion());
        dto.setActive(resource.getActive());
        dto.setSortOrder(resource.getSortOrder());
        dto.setDownloadCount(resource.getDownloadCount());
        dto.setCreatedAt(resource.getCreatedAt());
        dto.setUpdatedAt(resource.getUpdatedAt());

        // 新增字段
        dto.setCategoryId(resource.getCategoryId());
        dto.setSubcategory(resource.getSubcategory());
        dto.setComplexityScore(resource.getComplexityScore());
        dto.setEstimatedTime(resource.getEstimatedTime());
        dto.setPreviewImageUrl(resource.getPreviewImageUrl());
        dto.setColoredSampleUrl(resource.getColoredSampleUrl());
        dto.setColoringDataUrl(resource.getColoringDataUrl());
        dto.setRegionsCount(resource.getRegionsCount());
        dto.setColorsCount(resource.getColorsCount());
        dto.setAgeGroup(resource.getAgeGroup());
        dto.setTheme(resource.getTheme());
        dto.setCompletionCount(resource.getCompletionCount());
        dto.setRating(resource.getRating());
        dto.setRatingCount(resource.getRatingCount());
        dto.setIsPremium(resource.getIsPremium());
        dto.setIsFeatured(resource.getIsFeatured());
        dto.setStatus(resource.getStatus());

        // 获取分类名称
        if (resource.getCategoryId() != null) {
            categoryRepository.findById(resource.getCategoryId())
                    .ifPresent(category -> dto.setCategoryName(category.getName()));
        }

        // 解析标签
        if (resource.getTags() != null && !resource.getTags().isEmpty()) {
            try {
                List<String> tags = objectMapper.readValue(resource.getTags(), new TypeReference<List<String>>() {});
                dto.setTags(tags);
            } catch (Exception e) {
                log.warn("解析标签失败: {}", resource.getTags(), e);
            }
        }

        return dto;
    }

    @Override
    @Transactional(readOnly = true)
    public long getTotalResourceCount() {
        return resourceRepository.countByActiveTrue();
    }
}
