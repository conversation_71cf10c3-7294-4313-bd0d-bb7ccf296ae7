package com.dreaming.service.impl;

import com.dreaming.service.FileStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 文件存储服务实现类
 */
@Service
@Slf4j
public class FileStorageServiceImpl implements FileStorageService {

    @Value("${app.file-storage.upload-dir}")
    private String uploadDir;

    @Value("${app.file-storage.base-url}")
    private String baseUrl;

    private Path uploadPath;

    /**
     * 初始化上传目录
     */
    private void initUploadDir() {
        if (uploadPath == null) {
            uploadPath = Paths.get(uploadDir).toAbsolutePath().normalize();
            try {
                Files.createDirectories(uploadPath);
                log.info("文件上传目录初始化成功: {}", uploadPath);
            } catch (IOException e) {
                log.error("创建上传目录失败: {}", uploadPath, e);
                throw new RuntimeException("无法创建上传目录", e);
            }
        }
    }

    @Override
    public String storeFile(MultipartFile file) {
        // 生成新的标识符
        String identifier = UUID.randomUUID().toString().substring(0, 8);
        return storeFileWithIdentifier(file, identifier);
    }

    @Override
    public String storeFileWithIdentifier(MultipartFile file, String identifier) {
        return storeFileWithIdentifier(file, identifier, null);
    }

    /**
     * 存储文件（使用指定的标识符和分类）
     */
    public String storeFileWithIdentifier(MultipartFile file, String identifier, String category) {
        initUploadDir();

        // 验证文件
        if (file.isEmpty()) {
            throw new RuntimeException("上传文件为空");
        }

        String originalFilename = StringUtils.cleanPath(file.getOriginalFilename());
        if (originalFilename.contains("..")) {
            throw new RuntimeException("文件名包含非法字符: " + originalFilename);
        }

        try {
            // 使用指定的标识符生成文件名
            String fileExtension = getFileExtension(originalFilename);
            String storedFilename = identifier + fileExtension;

            // 创建按分类分组的子目录
            String categoryDir = "default";
            if (category != null && !category.trim().isEmpty()) {
                // 将中文分类名转换为英文目录名
                categoryDir = getCategoryDirName(category.trim());
            }
            Path categoryPath = uploadPath.resolve(categoryDir);
            Files.createDirectories(categoryPath);

            // 存储文件
            Path targetLocation = categoryPath.resolve(storedFilename);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);

            String relativePath = categoryDir + "/" + storedFilename;
            log.info("文件存储成功: {} -> {}", originalFilename, relativePath);
            
            return relativePath;

        } catch (IOException e) {
            log.error("存储文件失败: {}", originalFilename, e);
            throw new RuntimeException("存储文件失败: " + e.getMessage());
        }
    }

    @Override
    public String getFilePath(String filename) {
        initUploadDir();
        return uploadPath.resolve(filename).toString();
    }

    @Override
    public String getFileUrl(String filename) {
        return baseUrl + "/" + filename.replace("\\", "/");
    }

    @Override
    public void deleteFile(String filename) {
        try {
            initUploadDir();
            Path filePath = uploadPath.resolve(filename);
            Files.deleteIfExists(filePath);
            log.info("删除文件成功: {}", filename);
        } catch (IOException e) {
            log.error("删除文件失败: {}", filename, e);
            throw new RuntimeException("删除文件失败: " + e.getMessage());
        }
    }

    @Override
    public boolean fileExists(String filename) {
        initUploadDir();
        Path filePath = uploadPath.resolve(filename);
        return Files.exists(filePath);
    }

    @Override
    public long getFileSize(String filename) {
        try {
            initUploadDir();
            Path filePath = uploadPath.resolve(filename);
            return Files.size(filePath);
        } catch (IOException e) {
            log.error("获取文件大小失败: {}", filename, e);
            return 0;
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex);
    }

    /**
     * 将分类名转换为英文目录名
     * 优先使用英文名称，如果是中文名称则进行映射
     */
    private String getCategoryDirName(String category) {
        log.info("转换分类名为目录名: {}", category);

        // 如果已经是英文名称，直接转换为小写
        if (category.matches("^[a-zA-Z]+$")) {
            String result = category.toLowerCase();
            log.info("使用英文分类名: {} -> {}", category, result);
            return result;
        }

        // 中文分类名映射（根据DataInitializer中的实际分类名称）
        switch (category) {
            case "房屋建筑": return "houses";
            case "树屋小屋": return "treehouses";
            case "农场庄园": return "farmhouses";
            case "城堡宫殿": return "castles";
            case "动物世界": return "animals";
            case "植物花卉": return "plants";
            case "卡通人物": return "cartoon";
            case "交通工具": return "vehicles";
            case "食物美食": return "food";
            case "节日庆典": return "holidays";
            // 兼容旧的分类名称
            case "动物": return "animals";
            case "花卉植物": return "plants";
            case "人物角色": return "characters";
            case "食物饮品": return "food";
            case "玩具游戏": return "toys";
            case "自然风景": return "nature";
            case "抽象图案": return "abstract";
            default:
                log.warn("未知分类名称: {}, 使用default目录", category);
                return "default";
        }
    }
}
