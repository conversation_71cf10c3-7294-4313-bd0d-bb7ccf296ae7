package com.dreaming.service;

import com.dreaming.entity.ColoringResource;
import com.dreaming.entity.Category;
import com.dreaming.repository.ColoringResourceRepository;
import com.dreaming.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.List;

/**
 * 资源迁移服务
 * 用于修复现有资源的文件存储路径
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ResourceMigrationService {

    private final ColoringResourceRepository resourceRepository;
    private final CategoryRepository categoryRepository;
    private final FileStorageService fileStorageService;

    @Value("${app.file-storage.upload-dir}")
    private String uploadDir;

    @Value("${app.file-storage.base-url}")
    private String baseUrl;

    /**
     * 迁移所有资源到正确的分类文件夹
     */
    @Transactional
    public void migrateResourcesToCorrectFolders() {
        log.info("开始迁移资源到正确的分类文件夹");
        
        List<ColoringResource> allResources = resourceRepository.findAll();
        int successCount = 0;
        int errorCount = 0;

        for (ColoringResource resource : allResources) {
            try {
                if (migrateResource(resource)) {
                    successCount++;
                } else {
                    errorCount++;
                }
            } catch (Exception e) {
                log.error("迁移资源失败: {}", resource.getName(), e);
                errorCount++;
            }
        }

        log.info("资源迁移完成: 成功 {} 个, 失败 {} 个", successCount, errorCount);
    }

    /**
     * 迁移单个资源
     */
    private boolean migrateResource(ColoringResource resource) {
        try {
            // 获取正确的分类文件夹名称
            String correctCategoryDir = getCorrectCategoryDir(resource);
            
            // 检查当前文件路径
            String currentStoredFilename = resource.getStoredFilename();
            if (currentStoredFilename == null || currentStoredFilename.isEmpty()) {
                log.warn("资源 {} 没有存储文件名，跳过迁移", resource.getName());
                return false;
            }

            // 如果已经在正确的文件夹中，跳过
            if (currentStoredFilename.startsWith(correctCategoryDir + "/")) {
                log.debug("资源 {} 已在正确文件夹中: {}", resource.getName(), currentStoredFilename);
                return true;
            }

            // 构建源文件和目标文件路径
            Path uploadPath = Paths.get(uploadDir).toAbsolutePath().normalize();
            Path sourceFile = uploadPath.resolve(currentStoredFilename);
            
            // 提取文件名部分
            String filename = Paths.get(currentStoredFilename).getFileName().toString();
            String newStoredFilename = correctCategoryDir + "/" + filename;
            Path targetFile = uploadPath.resolve(newStoredFilename);

            // 创建目标目录
            Files.createDirectories(targetFile.getParent());

            // 移动文件
            if (Files.exists(sourceFile)) {
                Files.move(sourceFile, targetFile, StandardCopyOption.REPLACE_EXISTING);
                
                // 更新数据库记录
                resource.setStoredFilename(newStoredFilename);
                resource.setFilePath(targetFile.toString());
                resourceRepository.save(resource);
                
                log.info("成功迁移资源: {} -> {}", currentStoredFilename, newStoredFilename);
                
                // 同时迁移JSON文件（如果存在）
                migrateJsonFile(uploadPath, currentStoredFilename, newStoredFilename);
                
                return true;
            } else {
                log.warn("源文件不存在: {}", sourceFile);
                return false;
            }

        } catch (IOException e) {
            log.error("迁移资源文件失败: {}", resource.getName(), e);
            return false;
        }
    }

    /**
     * 迁移对应的JSON文件
     */
    private void migrateJsonFile(Path uploadPath, String currentStoredFilename, String newStoredFilename) {
        try {
            // 构建JSON文件路径
            String currentJsonFilename = currentStoredFilename.replaceAll("\\.(png|jpg|jpeg|gif)$", ".json");
            String newJsonFilename = newStoredFilename.replaceAll("\\.(png|jpg|jpeg|gif)$", ".json");
            
            Path sourceJsonFile = uploadPath.resolve(currentJsonFilename);
            Path targetJsonFile = uploadPath.resolve(newJsonFilename);

            if (Files.exists(sourceJsonFile)) {
                Files.move(sourceJsonFile, targetJsonFile, StandardCopyOption.REPLACE_EXISTING);
                log.info("成功迁移JSON文件: {} -> {}", currentJsonFilename, newJsonFilename);
            }
        } catch (IOException e) {
            log.warn("迁移JSON文件失败: {}", currentStoredFilename, e);
        }
    }

    /**
     * 获取正确的分类文件夹名称
     */
    private String getCorrectCategoryDir(ColoringResource resource) {
        // 首先尝试通过categoryId获取
        if (resource.getCategoryId() != null) {
            Category category = categoryRepository.findById(resource.getCategoryId()).orElse(null);
            if (category != null && category.getNameEn() != null && !category.getNameEn().trim().isEmpty()) {
                return category.getNameEn().toLowerCase();
            }
        }

        // 然后尝试通过category字段获取
        if (resource.getCategory() != null && !resource.getCategory().trim().isEmpty()) {
            // 首先尝试按英文名称查找
            Category category = categoryRepository.findByNameEn(resource.getCategory());
            if (category == null) {
                // 如果没找到，尝试按中文名称查找
                category = categoryRepository.findByName(resource.getCategory());
            }
            
            if (category != null && category.getNameEn() != null && !category.getNameEn().trim().isEmpty()) {
                return category.getNameEn().toLowerCase();
            }
        }

        // 如果都找不到，使用默认文件夹
        log.warn("无法确定资源 {} 的正确分类文件夹，使用default", resource.getName());
        return "default";
    }
}