package com.dreaming.service;

import com.dreaming.entity.Category;
import com.dreaming.entity.ColoringResource;
import com.dreaming.repository.CategoryRepository;
import com.dreaming.repository.ColoringResourceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

/**
 * 测试数据生成服务
 * 基于uploads/test目录中的文件创建测试数据
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TestDataGeneratorService {

    private final ColoringResourceRepository resourceRepository;
    private final CategoryRepository categoryRepository;

    private static final String TEST_SOURCE_DIR = "uploads/test";
    private static final String UPLOADS_DIR = "uploads";
    
    // 测试文件信息
    private static final String SOURCE_IMAGE = "longzhu3_android_outline.png";
    private static final String SOURCE_JSON = "longzhu3_android.json";

    /**
     * 基于现有文件生成测试数据
     */
    @Transactional
    public String generateTestData() {
        try {
            log.info("开始基于现有文件生成测试数据...");
            
            // 确保目录存在
            createDirectoriesIfNotExist();
            
            // 获取所有分类
            List<Category> categories = categoryRepository.findAll();
            if (categories.isEmpty()) {
                return "错误：没有找到分类数据，请先初始化分类";
            }
            
            int totalCreated = 0;
            Random random = new Random();
            
            // 为每个分类创建2-3个测试资源
            for (Category category : categories) {
                int resourceCount = 2 + random.nextInt(2); // 2-3个资源
                
                for (int i = 1; i <= resourceCount; i++) {
                    try {
                        // 生成新的文件名
                        String newBaseName = generateFileName(category.getNameEn(), i);
                        String newImageName = newBaseName + "_outline.png";
                        String newJsonName = newBaseName + "_data.json";
                        
                        // 复制文件
                        copyTestFiles(newImageName, newJsonName);
                        
                        // 创建数据库记录
                        ColoringResource resource = createResourceRecord(category, i, newImageName, newJsonName, random);
                        resourceRepository.save(resource);
                        
                        totalCreated++;
                        log.info("创建测试资源: {} - {}", category.getName(), resource.getName());
                        
                    } catch (Exception e) {
                        log.error("创建资源失败: {} - {}", category.getName(), i, e);
                    }
                }
            }
            
            log.info("测试数据生成完成！总共创建了 {} 个资源", totalCreated);
            return String.format("成功生成 %d 个测试资源", totalCreated);
            
        } catch (Exception e) {
            log.error("生成测试数据失败", e);
            return "生成测试数据失败: " + e.getMessage();
        }
    }

    /**
     * 创建必要的目录
     */
    private void createDirectoriesIfNotExist() throws IOException {
        Path uploadsPath = Paths.get(UPLOADS_DIR);
        if (!Files.exists(uploadsPath)) {
            Files.createDirectories(uploadsPath);
            log.info("创建uploads目录: {}", uploadsPath.toAbsolutePath());
        }
    }

    /**
     * 复制测试文件
     */
    private void copyTestFiles(String newImageName, String newJsonName) throws IOException {
        Path sourceImagePath = Paths.get(TEST_SOURCE_DIR, SOURCE_IMAGE);
        Path sourceJsonPath = Paths.get(TEST_SOURCE_DIR, SOURCE_JSON);
        
        Path targetImagePath = Paths.get(UPLOADS_DIR, newImageName);
        Path targetJsonPath = Paths.get(UPLOADS_DIR, newJsonName);
        
        // 检查源文件是否存在
        if (!Files.exists(sourceImagePath)) {
            throw new IOException("源图片文件不存在: " + sourceImagePath);
        }
        if (!Files.exists(sourceJsonPath)) {
            throw new IOException("源JSON文件不存在: " + sourceJsonPath);
        }
        
        // 复制文件
        Files.copy(sourceImagePath, targetImagePath, StandardCopyOption.REPLACE_EXISTING);
        Files.copy(sourceJsonPath, targetJsonPath, StandardCopyOption.REPLACE_EXISTING);
        
        log.debug("复制文件: {} -> {}", sourceImagePath, targetImagePath);
        log.debug("复制文件: {} -> {}", sourceJsonPath, targetJsonPath);
    }

    /**
     * 创建资源数据库记录
     */
    private ColoringResource createResourceRecord(Category category, int index, String imageName, String jsonName, Random random) {
        ColoringResource resource = new ColoringResource();
        
        // 基础信息
        resource.setName(generateResourceName(category.getName(), index));
        resource.setDescription(generateResourceDescription(category.getName(), index));
        resource.setCategory(category.getName());
        resource.setCategoryId(category.getId());
        
        // 难度和时间
        resource.setDifficulty(1 + random.nextInt(5)); // 1-5
        resource.setEstimatedTime(10 + random.nextInt(50)); // 10-60分钟
        
        // 区域和颜色
        resource.setRegionsCount(5 + random.nextInt(20)); // 5-25个区域
        resource.setColorsCount(3 + random.nextInt(10)); // 3-12种颜色
        
        // 文件信息
        resource.setOriginalFilename(imageName);
        resource.setStoredFilename(imageName);
        resource.setFilePath("/uploads/" + imageName);
        resource.setFileSize(50000L + random.nextInt(200000)); // 50KB-250KB
        resource.setContentType("image/png");
        
        // 统计信息
        resource.setDownloadCount((long) random.nextInt(1000));
        resource.setCompletionCount((long) random.nextInt(500));
        resource.setRating(3.0 + random.nextDouble() * 2.0); // 3.0-5.0
        resource.setRatingCount(random.nextInt(100));
        
        // 状态
        resource.setActive(true);
        resource.setIsPremium(random.nextBoolean() && random.nextDouble() < 0.3); // 30%概率为付费
        resource.setIsFeatured(random.nextBoolean() && random.nextDouble() < 0.2); // 20%概率为精选
        resource.setStatus("ACTIVE");
        
        // 时间信息
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime createdTime = now.minusDays(random.nextInt(30)); // 最近30天内创建
        resource.setCreatedAt(createdTime);
        resource.setUpdatedAt(createdTime);
        resource.setCreatedBy("system");
        resource.setUpdatedBy("system");
        
        // 版本
        resource.setVersion("1.0.0");
        
        // 额外信息
        resource.setAgeGroup(generateAgeGroup(resource.getDifficulty()));
        resource.setTheme(category.getName());
        resource.setTags(generateTags(category.getName()));
        
        // URLs
        resource.setColoringDataUrl("/api/files/" + jsonName);
        resource.setPreviewImageUrl("/api/files/preview_" + imageName);
        
        return resource;
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String categoryEn, int index) {
        return categoryEn.toLowerCase() + "_" + index;
    }

    /**
     * 生成资源名称
     */
    private String generateResourceName(String categoryName, int index) {
        String[] prefixes = {"可爱的", "美丽的", "精致的", "有趣的", "梦幻的", "温馨的", "神奇的", "迷人的"};
        String[] suffixes = {"图案", "设计", "作品", "画作", "图画", "插画"};
        
        Random random = new Random();
        String prefix = prefixes[random.nextInt(prefixes.length)];
        String suffix = suffixes[random.nextInt(suffixes.length)];
        
        return prefix + categoryName + suffix + " " + index;
    }

    /**
     * 生成资源描述
     */
    private String generateResourceDescription(String categoryName, int index) {
        return String.format("这是一个精美的%s主题涂色作品，适合各个年龄段的用户。通过涂色可以放松心情，培养专注力和创造力。", categoryName);
    }

    /**
     * 生成年龄组
     */
    private String generateAgeGroup(int difficulty) {
        switch (difficulty) {
            case 1: return "3-6岁";
            case 2: return "5-8岁";
            case 3: return "7-12岁";
            case 4: return "10-16岁";
            case 5: return "成人";
            default: return "全年龄";
        }
    }

    /**
     * 生成标签
     */
    private String generateTags(String categoryName) {
        return String.format("[\"%s\", \"涂色\", \"创意\", \"放松\"]", categoryName);
    }

    /**
     * 清理测试数据
     */
    @Transactional
    public String clearTestData() {
        try {
            long count = resourceRepository.count();
            resourceRepository.deleteAll();
            
            // 删除复制的文件（可选）
            // deleteGeneratedFiles();
            
            log.info("已清空 {} 个资源", count);
            return String.format("成功清空 %d 个资源", count);
        } catch (Exception e) {
            log.error("清空测试数据失败", e);
            return "清空测试数据失败: " + e.getMessage();
        }
    }
}
