<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: layout(~{::title}, ~{::content})}">

<head>
    <title>上传资源 - Dreaming涂色App管理后台</title>
</head>

<body>
    <div th:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-upload"></i>
                上传资源
            </h2>
            <div>
                <a th:href="@{/admin/resources/batch-upload}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-layer-group"></i>
                    批量上传
                </a>
                <a th:href="@{/admin/resources}" class="btn btn-outline-secondary">
                    <i class="fas fa-list"></i>
                    资源列表
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-upload"></i>
                            单个资源上传
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="/admin/resources/upload" method="post" enctype="multipart/form-data">

                            <!-- 文件上传区域 -->
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-image"></i>
                                    涂色图片 <span class="text-danger">*</span>
                                </label>
                                <div class="upload-area" id="uploadArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h5>拖拽文件到此处或点击选择</h5>
                                    <p class="text-muted">支持 JPG, PNG, GIF 格式，建议大小不超过5MB</p>
                                    <input type="file" name="file" id="fileInput" class="d-none" accept="image/*"
                                        required>
                                    <button type="button" class="btn btn-outline-primary" id="selectFileBtn">
                                        <i class="fas fa-folder-open"></i>
                                        选择文件
                                    </button>
                                </div>
                                <div id="filePreview" class="mt-3" style="display: none;">
                                    <img id="imagePreview" class="img-thumbnail" style="max-height: 200px;">
                                    <div class="mt-2">
                                        <span id="fileName" class="badge bg-info"></span>
                                        <span id="fileSize" class="badge bg-secondary"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- JSON配置文件 -->
                            <div class="mb-3">
                                <label for="jsonFile" class="form-label">
                                    <i class="fas fa-code"></i>
                                    JSON配置文件（可选）
                                </label>
                                <input type="file" class="form-control" name="jsonFile" id="jsonFile" accept=".json">
                                <div class="form-text">包含涂色区域配置的JSON文件</div>
                            </div>

                            <div class="row">
                                <!-- 资源名称 -->
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">
                                        <i class="fas fa-tag"></i>
                                        资源名称 <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" name="name" id="name" placeholder="请输入资源名称"
                                        required>
                                </div>

                                <!-- 难度等级 -->
                                <div class="col-md-6 mb-3">
                                    <label for="difficulty" class="form-label">
                                        <i class="fas fa-star"></i>
                                        难度等级 <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" name="difficulty" id="difficulty" required>
                                        <option value="">请选择难度</option>
                                        <option value="1">⭐ 简单</option>
                                        <option value="2">⭐⭐ 普通</option>
                                        <option value="3">⭐⭐⭐ 困难</option>
                                        <option value="4">⭐⭐⭐⭐ 专家</option>
                                        <option value="5">⭐⭐⭐⭐⭐ 大师</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 分类选择 -->
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-tags"></i>
                                    资源分类 <span class="text-danger">*</span>
                                </label>

                                <!-- 如果有分类DTO，显示改进的分类选择器 -->
                                <div th:if="${categoryDTOs != null and !#lists.isEmpty(categoryDTOs)}">
                                    <div class="row">
                                        <div th:each="category : ${categoryDTOs}" class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="categoryId"
                                                    th:id="'category_' + ${category.id}" th:value="${category.id}"
                                                    required>
                                                <label class="form-check-label d-flex align-items-center"
                                                    th:for="'category_' + ${category.id}">
                                                    <div class="me-2">
                                                        <img th:if="${category.iconUrl != null and category.iconUrl != ''}"
                                                            th:src="${category.iconUrl}" alt="分类图标" class="rounded"
                                                            style="width: 24px; height: 24px; object-fit: cover;">
                                                        <i th:unless="${category.iconUrl != null and category.iconUrl != ''}"
                                                            class="fas fa-tag text-muted"></i>
                                                    </div>
                                                    <div>
                                                        <div th:text="${category.name}">分类名称</div>
                                                        <small class="text-muted"
                                                            th:text="${category.nameEn}">english-name</small>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 降级方案：传统下拉选择 -->
                                <div th:if="${categoryDTOs == null or #lists.isEmpty(categoryDTOs)}">
                                    <select class="form-select" name="category" required>
                                        <option value="">请选择分类</option>
                                        <option th:each="category : ${categories}" th:value="${category}"
                                            th:text="${category}">分类</option>
                                    </select>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle"></i>
                                        没有找到分类信息，请先在 <a th:href="@{/admin/categories}">分类管理</a> 中创建分类
                                    </div>
                                </div>
                            </div>

                            <!-- 描述 -->
                            <div class="mb-3">
                                <label for="description" class="form-label">
                                    <i class="fas fa-align-left"></i>
                                    资源描述
                                </label>
                                <textarea class="form-control" name="description" id="description" rows="3"
                                    placeholder="请输入资源描述（可选）"></textarea>
                            </div>

                            <!-- 版本 -->
                            <div class="mb-4">
                                <label for="version" class="form-label">
                                    <i class="fas fa-code-branch"></i>
                                    版本号
                                </label>
                                <input type="text" class="form-control" name="version" id="version"
                                    th:value="${currentVersion}" readonly>
                                <div class="form-text">当前应用版本，自动填充</div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="d-flex justify-content-end gap-2">
                                <a th:href="@{/admin/resources}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                    取消
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-upload"></i>
                                    上传资源
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 右侧帮助信息 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-question-circle"></i>
                            上传说明
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6><i class="fas fa-image text-primary"></i> 图片要求</h6>
                            <ul class="small text-muted">
                                <li>支持JPG、PNG、GIF格式</li>
                                <li>建议分辨率：1024x1024或更高</li>
                                <li>文件大小不超过5MB</li>
                                <li>图片应为线稿或轮廓图</li>
                            </ul>
                        </div>

                        <div class="mb-3">
                            <h6><i class="fas fa-star text-warning"></i> 难度等级</h6>
                            <ul class="small text-muted">
                                <li>⭐ 简单：适合儿童，线条粗大</li>
                                <li>⭐⭐ 普通：一般复杂度</li>
                                <li>⭐⭐⭐ 困难：细节较多</li>
                                <li>⭐⭐⭐⭐ 专家：复杂图案</li>
                                <li>⭐⭐⭐⭐⭐ 大师：极其复杂</li>
                            </ul>
                        </div>

                        <div class="mb-3">
                            <h6><i class="fas fa-code text-info"></i> JSON配置</h6>
                            <p class="small text-muted">可选的JSON文件，包含涂色区域的配置信息，如颜色建议、区域划分等</p>
                        </div>
                    </div>
                </div>

                <!-- 分类统计 -->
                <div class="card mt-3" th:if="${categoryDTOs != null and !#lists.isEmpty(categoryDTOs)}">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-pie"></i>
                            分类统计
                        </h6>
                    </div>
                    <div class="card-body">
                        <div th:each="category : ${categoryDTOs}"
                            class="d-flex justify-content-between align-items-center mb-2">
                            <div class="d-flex align-items-center">
                                <img th:if="${category.iconUrl != null and category.iconUrl != ''}"
                                    th:src="${category.iconUrl}" alt="分类图标" class="rounded me-2"
                                    style="width: 20px; height: 20px; object-fit: cover;">
                                <i th:unless="${category.iconUrl != null and category.iconUrl != ''}"
                                    class="fas fa-tag text-muted me-2"></i>
                                <small th:text="${category.name}">分类名称</small>
                            </div>
                            <span class="badge bg-secondary" th:text="${category.resourceCount ?: 0}">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <style>
            .upload-area {
                border: 2px dashed #dee2e6;
                border-radius: 8px;
                padding: 2rem;
                text-align: center;
                transition: all 0.3s ease;
                cursor: pointer;
            }

            .upload-area:hover {
                border-color: #007bff;
                background-color: #f8f9fa;
            }

            .upload-area.dragover {
                border-color: #007bff;
                background-color: #e3f2fd;
            }

            .form-check-label {
                cursor: pointer;
                padding: 0.5rem;
                border-radius: 0.375rem;
                transition: background-color 0.15s ease-in-out;
            }

            .form-check-label:hover {
                background-color: #f8f9fa;
            }

            .form-check-input:checked+.form-check-label {
                background-color: #e3f2fd;
            }
        </style>

        <script>
            // 文件上传功能
            document.addEventListener('DOMContentLoaded', function () {
                const uploadArea = document.getElementById('uploadArea');
                const fileInput = document.getElementById('fileInput');
                const filePreview = document.getElementById('filePreview');
                const imagePreview = document.getElementById('imagePreview');
                const fileName = document.getElementById('fileName');
                const fileSize = document.getElementById('fileSize');
                const nameInput = document.getElementById('name');

                // 点击上传区域触发文件选择
                uploadArea.addEventListener('click', function () {
                    fileInput.click();
                });

                // 点击选择文件按钮
                const selectFileBtn = document.getElementById('selectFileBtn');
                if (selectFileBtn) {
                    selectFileBtn.addEventListener('click', function() {
                        fileInput.click();
                    });
                }

                // 拖拽功能
                uploadArea.addEventListener('dragover', function (e) {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', function (e) {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', function (e) {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        fileInput.files = files;
                        handleFileSelect(files[0]);
                    }
                });

                // 文件选择处理
                fileInput.addEventListener('change', function (e) {
                    if (e.target.files.length > 0) {
                        handleFileSelect(e.target.files[0]);
                    }
                });

                function handleFileSelect(file) {
                    // 显示文件信息
                    fileName.textContent = file.name;
                    fileSize.textContent = formatFileSize(file.size);

                    // 自动填充资源名称（去掉扩展名）
                    if (!nameInput.value) {
                        const nameWithoutExt = file.name.replace(/\.[^/.]+$/, "");
                        nameInput.value = nameWithoutExt;
                    }

                    // 显示图片预览
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function (e) {
                            imagePreview.src = e.target.result;
                            filePreview.style.display = 'block';
                        };
                        reader.readAsDataURL(file);
                    }
                }

                function formatFileSize(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                }
            });
        </script>
    </div>
</body>

</html>
<div class="row">
    <!-- 侧边栏 -->
    <div class="col-md-2 sidebar p-0">
        <div class="list-group list-group-flush">
            <a href="/admin" class="list-group-item list-group-item-action bg-transparent text-white">
                <i class="fas fa-tachometer-alt"></i> 仪表盘
            </a>
            <a href="/admin/resources" class="list-group-item list-group-item-action bg-transparent text-white">
                <i class="fas fa-images"></i> 资源管理
            </a>
            <a href="/admin/resources/upload"
                class="list-group-item list-group-item-action bg-transparent text-white active">
                <i class="fas fa-upload"></i> 上传资源
            </a>
            <a href="/admin/resources/batch-upload"
                class="list-group-item list-group-item-action bg-transparent text-white">
                <i class="fas fa-folder-open"></i> 批量上传
            </a>
            <a href="/admin/versions" class="list-group-item list-group-item-action bg-transparent text-white">
                <i class="fas fa-code-branch"></i> 版本管理
            </a>
            <a href="/admin/export" class="list-group-item list-group-item-action bg-transparent text-white">
                <i class="fas fa-download"></i> 数据导出
            </a>
            <a href="/admin/settings" class="list-group-item list-group-item-action bg-transparent text-white">
                <i class="fas fa-cog"></i> 系统设置
            </a>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="col-md-10">
        <div class="container-fluid py-4">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="fas fa-upload"></i>
                    上传新资源
                </h2>
                <a href="/admin/resources" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> 返回资源列表
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <!-- 上传表单 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-upload"></i>
                                资源信息
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post" enctype="multipart/form-data" action="/admin/resources/upload">
                                <!-- 线稿图片文件选择 -->
                                <div class="mb-3">
                                    <label for="file" class="form-label">选择线稿图片 <span
                                            class="text-danger">*</span></label>
                                    <input type="file" class="form-control" id="file" name="file" accept="image/*"
                                        required onchange="previewImage(this)">
                                    <div class="form-text">支持 JPG, PNG, GIF 等格式，建议大小不超过 10MB</div>
                                </div>

                                <!-- 涂色数据JSON文件选择 -->
                                <div class="mb-3">
                                    <label for="jsonFile" class="form-label">涂色数据JSON文件 <span
                                            class="text-muted">(可选)</span></label>
                                    <input type="file" class="form-control" id="jsonFile" name="jsonFile"
                                        accept=".json,application/json" onchange="previewJson(this)">
                                    <div class="form-text">包含涂色区域、调色板等数据的JSON文件</div>
                                    <div id="jsonPreview" class="mt-2" style="display: none;">
                                        <small class="text-muted">JSON文件预览：</small>
                                        <pre id="jsonContent" class="bg-light p-2 small"
                                            style="max-height: 200px; overflow-y: auto;"></pre>
                                    </div>
                                </div>

                                <!-- 资源名称 -->
                                <div class="mb-3">
                                    <label for="name" class="form-label">资源名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required
                                        placeholder="请输入资源名称">
                                </div>

                                <!-- 资源描述 -->
                                <div class="mb-3">
                                    <label for="description" class="form-label">资源描述</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"
                                        placeholder="请输入资源描述（可选）"></textarea>
                                </div>

                                <div class="row">
                                    <!-- 分类选择 -->
                                    <div class="col-md-6 mb-3">
                                        <label for="category" class="form-label">分类 <span
                                                class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="category" name="category"
                                                required readonly placeholder="请选择分类">
                                            <input type="hidden" id="categoryId" name="categoryId">
                                            <button type="button" class="btn btn-outline-primary"
                                                onclick="openCategorySelector()">
                                                <i class="fas fa-list"></i> 选择分类
                                            </button>
                                        </div>
                                        <div class="form-text">点击按钮选择预设分类</div>
                                    </div>

                                    <!-- 难度等级 -->
                                    <div class="col-md-6 mb-3">
                                        <label for="difficulty" class="form-label">难度等级 <span
                                                class="text-danger">*</span></label>
                                        <select class="form-select" id="difficulty" name="difficulty" required>
                                            <option value="">请选择难度</option>
                                            <option value="1">1 - 非常简单</option>
                                            <option value="2">2 - 简单</option>
                                            <option value="3">3 - 中等</option>
                                            <option value="4">4 - 困难</option>
                                            <option value="5">5 - 非常困难</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 版本 -->
                                <div class="mb-3">
                                    <label for="version" class="form-label">版本号 <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="version" name="version" required
                                        th:value="${currentVersion}" placeholder="1.0.0">
                                    <div class="form-text">建议使用当前版本号</div>
                                </div>

                                <!-- 提交按钮 -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="reset" class="btn btn-outline-secondary me-md-2">
                                        <i class="fas fa-undo"></i> 重置
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-upload"></i> 上传资源
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- 图片预览 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-eye"></i>
                                图片预览
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <img id="imagePreview" src="" alt="图片预览" class="img-fluid rounded"
                                style="display: none; max-height: 300px;">
                            <div id="previewPlaceholder" class="text-muted py-5">
                                <i class="fas fa-image fa-3x mb-3"></i>
                                <p>选择图片后将在此处显示预览</p>
                            </div>
                        </div>
                    </div>

                    <!-- 上传提示 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle"></i>
                                上传提示
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    支持 JPG、PNG、GIF 格式
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    建议图片尺寸为正方形
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    文件大小不超过 10MB
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    图片内容适合涂色
                                </li>
                                <li>
                                    <i class="fas fa-check text-success"></i>
                                    避免过于复杂的细节
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- 分类说明 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-tags"></i>
                                分类建议
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">
                                        • 动物<br>
                                        • 植物<br>
                                        • 风景<br>
                                        • 人物
                                    </small>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">
                                        • 建筑<br>
                                        • 抽象<br>
                                        • 节日<br>
                                        • 卡通
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            function previewImage(input) {
                const preview = document.getElementById('imagePreview');
                const placeholder = document.getElementById('previewPlaceholder');

                if (input.files && input.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function (e) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                        placeholder.style.display = 'none';
                    }

                    reader.readAsDataURL(input.files[0]);

                    // 自动填充文件名作为资源名称（如果名称字段为空）
                    const nameInput = document.getElementById('name');
                    if (!nameInput.value) {
                        const fileName = input.files[0].name;
                        const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
                        nameInput.value = nameWithoutExt;
                    }
                } else {
                    preview.style.display = 'none';
                    placeholder.style.display = 'block';
                }
            }

            // 表单验证
            document.querySelector('form').addEventListener('submit', function (e) {
                const fileInput = document.getElementById('file');
                const nameInput = document.getElementById('name');
                const categoryInput = document.getElementById('category');
                const difficultyInput = document.getElementById('difficulty');
                const versionInput = document.getElementById('version');

                if (!fileInput.files[0]) {
                    alert('请选择要上传的图片文件');
                    e.preventDefault();
                    return;
                }

                if (!nameInput.value.trim()) {
                    alert('请输入资源名称');
                    nameInput.focus();
                    e.preventDefault();
                    return;
                }

                if (!categoryInput.value.trim()) {
                    alert('请输入资源分类');
                    categoryInput.focus();
                    e.preventDefault();
                    return;
                }

                if (!difficultyInput.value) {
                    alert('请选择难度等级');
                    difficultyInput.focus();
                    e.preventDefault();
                    return;
                }

                if (!versionInput.value.trim()) {
                    alert('请输入版本号');
                    versionInput.focus();
                    e.preventDefault();
                    return;
                }

                // 显示上传进度
                const submitBtn = document.querySelector('button[type="submit"]');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 上传中...';
                submitBtn.disabled = true;
            });

            // 打开分类选择器
            function openCategorySelector() {
                const popup = window.open('/admin/category-selector', 'categorySelector',
                    'width=800,height=600,scrollbars=yes,resizable=yes');

                // 监听来自分类选择器的消息
                window.addEventListener('message', function (event) {
                    if (event.data.type === 'categorySelected') {
                        const category = event.data.category;
                        document.getElementById('category').value = category.name;
                        document.getElementById('categoryId').value = category.id;
                    }
                });
            }

            // JSON文件预览
            function previewJson(input) {
                const file = input.files[0];
                const preview = document.getElementById('jsonPreview');
                const content = document.getElementById('jsonContent');

                if (file && file.type === 'application/json') {
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        try {
                            const jsonData = JSON.parse(e.target.result);
                            content.textContent = JSON.stringify(jsonData, null, 2);
                            preview.style.display = 'block';

                            // 自动填充一些字段
                            if (jsonData.meta) {
                                if (jsonData.meta.regions && !document.getElementById('regionsCount').value) {
                                    // 如果有regions字段，可以在这里设置
                                }
                                if (jsonData.meta.colors && !document.getElementById('colorsCount').value) {
                                    // 如果有colors字段，可以在这里设置
                                }
                            }
                        } catch (error) {
                            content.textContent = '无效的JSON文件格式';
                            preview.style.display = 'block';
                        }
                    };
                    reader.readAsText(file);
                } else {
                    preview.style.display = 'none';
                }
            }
        </script>
    </div>
</div>
</div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>