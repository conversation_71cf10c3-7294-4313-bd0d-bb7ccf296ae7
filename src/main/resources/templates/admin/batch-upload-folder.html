<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: layout(~{::title}, ~{::content})}">
<head>
    <title>文件夹批量上传 - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-folder-open"></i>
                文件夹批量上传
            </h2>
            <a th:href="@{/admin/resources/list}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i>
                查看资源列表
            </a>
        </div>

        <!-- 成功消息 -->
        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            <span th:text="${successMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 警告消息 -->
        <div th:if="${warningMessages}" class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>注意事项：</strong>
            <ul class="mb-0 mt-2">
                <li th:each="warning : ${warningMessages}" th:text="${warning}"></li>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 错误消息 -->
        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            <span th:text="${errorMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-folder-plus"></i>
                    选择文件夹批量上传
                </h5>
                <small class="text-muted">推荐使用文件夹上传，可以一次性上传整个文件夹中的所有资源</small>
            </div>
            <div class="card-body">
                <form action="/admin/resources/batch-upload" method="post" enctype="multipart/form-data" id="uploadForm">
                    
                    <!-- 文件夹选择区域 -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-folder"></i>
                            选择资源文件夹 <span class="text-danger">*</span>
                        </label>
                        
                        <!-- 文件夹选择（主要方式） -->
                        <div class="border rounded p-4 text-center mb-3" 
                             style="cursor: pointer; background-color: #f8f9fa; border: 2px dashed #dee2e6;" 
                             onclick="selectFolder()" id="folderDropZone">
                            <i class="fas fa-folder-open fa-3x text-primary mb-3"></i>
                            <h5 class="text-primary">点击选择文件夹</h5>
                            <p class="text-muted mb-0">
                                选择包含图片和JSON文件的文件夹<br>
                                <small>支持拖拽文件夹到此区域</small>
                            </p>
                        </div>
                        
                        <!-- 隐藏的文件夹输入 -->
                        <input type="file" id="folderInput" name="files" class="d-none" 
                               webkitdirectory directory multiple required>
                        
                        <!-- 备用文件选择 -->
                        <div class="text-center">
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectFiles()">
                                <i class="fas fa-file"></i>
                                或选择多个文件
                            </button>
                            <input type="file" id="filesInput" class="d-none" 
                                   accept="image/*,.json" multiple>
                        </div>
                        
                        <!-- 文件列表显示 -->
                        <div id="filesList" class="mt-3" style="display: none;">
                            <div class="alert alert-light">
                                <h6><i class="fas fa-list"></i> 已选择的文件：</h6>
                                <div id="filesContainer"></div>
                                <div id="pairingStatus" class="mt-2"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 分类选择 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-tags"></i>
                            选择分类 <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" name="categoryId" required>
                            <option value="">请选择分类</option>
                            <option th:each="category : ${categoryDTOs}" 
                                    th:value="${category.id}" 
                                    th:text="${category.name + ' (' + category.nameEn + ')'}">分类名称</option>
                        </select>
                    </div>

                    <div class="row">
                        <!-- 难度等级 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-star"></i>
                                难度等级 <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" name="difficulty" required>
                                <option value="">请选择难度</option>
                                <option value="1">⭐ 简单</option>
                                <option value="2">⭐⭐ 容易</option>
                                <option value="3" selected>⭐⭐⭐ 中等</option>
                                <option value="4">⭐⭐⭐⭐ 困难</option>
                                <option value="5">⭐⭐⭐⭐⭐ 专家</option>
                            </select>
                        </div>

                        <!-- 版本号 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-code-branch"></i>
                                版本号 <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" name="version" 
                                   th:value="${currentVersion}" required>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-success" id="submitBtn" disabled>
                            <i class="fas fa-upload"></i>
                            开始批量上传
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle"></i>
                    文件夹上传说明
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-folder-tree"></i> 文件夹结构示例</h6>
                        <pre class="small bg-light p-2 rounded">
resources/
├── house1.png
├── house1.json
├── house2.jpg
├── house2.json
├── tree1.png
├── tree1.json
└── ...
                        </pre>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle"></i> 上传优势</h6>
                        <ul class="small">
                            <li>一次选择整个文件夹</li>
                            <li>自动读取所有子文件</li>
                            <li>自动配对图片和JSON文件</li>
                            <li>批量处理，效率更高</li>
                            <li>减少手动选择文件的工作量</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];

        // 选择文件夹
        function selectFolder() {
            console.log('选择文件夹');
            document.getElementById('folderInput').click();
        }

        // 选择文件
        function selectFiles() {
            console.log('选择文件');
            document.getElementById('filesInput').click();
        }

        // 显示文件列表
        function displayFiles() {
            const filesList = document.getElementById('filesList');
            const filesContainer = document.getElementById('filesContainer');
            const submitBtn = document.getElementById('submitBtn');
            
            if (selectedFiles.length === 0) {
                filesList.style.display = 'none';
                submitBtn.disabled = true;
                return;
            }

            filesList.style.display = 'block';
            submitBtn.disabled = false;
            filesContainer.innerHTML = '';

            // 按类型分组显示
            const imageFiles = selectedFiles.filter(f => /\.(png|jpg|jpeg|gif)$/i.test(f.name));
            const jsonFiles = selectedFiles.filter(f => /\.json$/i.test(f.name));

            let html = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>图片文件 (${imageFiles.length}个):</strong>
                        <div class="small mt-1">
            `;
            
            imageFiles.slice(0, 10).forEach(file => {
                html += `<div><i class="fas fa-image text-primary me-1"></i>${file.name}</div>`;
            });
            
            if (imageFiles.length > 10) {
                html += `<div class="text-muted">... 还有 ${imageFiles.length - 10} 个图片文件</div>`;
            }
            
            html += `
                        </div>
                    </div>
                    <div class="col-md-6">
                        <strong>JSON文件 (${jsonFiles.length}个):</strong>
                        <div class="small mt-1">
            `;
            
            jsonFiles.slice(0, 10).forEach(file => {
                html += `<div><i class="fas fa-file-code text-success me-1"></i>${file.name}</div>`;
            });
            
            if (jsonFiles.length > 10) {
                html += `<div class="text-muted">... 还有 ${jsonFiles.length - 10} 个JSON文件</div>`;
            }
            
            html += `
                        </div>
                    </div>
                </div>
            `;

            filesContainer.innerHTML = html;
            
            // 显示配对状态
            validateFilePairing();
        }

        // 验证文件配对
        function validateFilePairing() {
            const pairingStatus = document.getElementById('pairingStatus');
            const imageFiles = new Map();
            const jsonFiles = new Map();
            
            selectedFiles.forEach(file => {
                const baseName = file.name.replace(/\.[^/.]+$/, "");
                if (/\.(png|jpg|jpeg|gif)$/i.test(file.name)) {
                    imageFiles.set(baseName, file);
                } else if (/\.json$/i.test(file.name)) {
                    jsonFiles.set(baseName, file);
                }
            });

            let pairedCount = 0;
            let warnings = [];

            imageFiles.forEach((file, baseName) => {
                if (jsonFiles.has(baseName)) {
                    pairedCount++;
                } else {
                    warnings.push(`图片 ${file.name} 缺少对应的JSON文件`);
                }
            });

            jsonFiles.forEach((file, baseName) => {
                if (!imageFiles.has(baseName)) {
                    warnings.push(`JSON文件 ${file.name} 缺少对应的图片文件`);
                }
            });

            let statusHtml = `<strong>配对状态：</strong> ${pairedCount} 个有效文件对`;
            if (warnings.length > 0) {
                statusHtml += `<div class="text-warning mt-2"><i class="fas fa-exclamation-triangle"></i> 警告 (${warnings.length}个):</div>`;
                statusHtml += '<div class="small">';
                warnings.slice(0, 5).forEach(warning => {
                    statusHtml += `<div>• ${warning}</div>`;
                });
                if (warnings.length > 5) {
                    statusHtml += `<div class="text-muted">... 还有 ${warnings.length - 5} 个警告</div>`;
                }
                statusHtml += '</div>';
            }

            pairingStatus.innerHTML = statusHtml;
        }

        // 页面加载完成后设置事件监听
        document.addEventListener('DOMContentLoaded', function() {
            // 文件夹选择监听
            document.getElementById('folderInput').addEventListener('change', function(e) {
                console.log('文件夹选择变化:', e.target.files.length, '个文件');
                selectedFiles = Array.from(e.target.files);
                displayFiles();
            });

            // 文件选择监听
            document.getElementById('filesInput').addEventListener('change', function(e) {
                console.log('文件选择变化:', e.target.files.length, '个文件');
                selectedFiles = Array.from(e.target.files);
                displayFiles();
                
                // 将文件复制到文件夹输入框
                const folderInput = document.getElementById('folderInput');
                const dt = new DataTransfer();
                Array.from(e.target.files).forEach(file => dt.items.add(file));
                folderInput.files = dt.files;
            });

            // 拖拽功能
            const dropZone = document.getElementById('folderDropZone');
            
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#0d6efd';
                dropZone.style.backgroundColor = '#f0f8ff';
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.style.borderColor = '#dee2e6';
                dropZone.style.backgroundColor = '#f8f9fa';
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#dee2e6';
                dropZone.style.backgroundColor = '#f8f9fa';
                
                console.log('拖拽文件:', e.dataTransfer.files.length, '个文件');
                selectedFiles = Array.from(e.dataTransfer.files);
                displayFiles();
                
                // 将文件设置到表单输入框
                const folderInput = document.getElementById('folderInput');
                folderInput.files = e.dataTransfer.files;
            });

            // 表单提交验证
            document.getElementById('uploadForm').addEventListener('submit', function(e) {
                if (selectedFiles.length === 0) {
                    alert('请选择要上传的文件夹或文件');
                    e.preventDefault();
                    return;
                }

                const submitBtn = document.getElementById('submitBtn');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 上传中...';
                submitBtn.disabled = true;
            });
        });
    </script>
</body>
</html>
