<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: layout(~{::title}, ~{::content})}">
<head>
    <title>批量上传 - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-upload"></i>
                批量上传资源
            </h2>
            <a th:href="@{/admin/resources/list}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i>
                查看资源列表
            </a>
        </div>

        <!-- 成功消息 -->
        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            <span th:text="${successMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 警告消息 -->
        <div th:if="${warningMessages}" class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>注意事项：</strong>
            <ul class="mb-0 mt-2">
                <li th:each="warning : ${warningMessages}" th:text="${warning}"></li>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 错误消息 -->
        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            <span th:text="${errorMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload"></i>
                    批量上传资源（简化版）
                </h5>
                <small class="text-muted">支持图片文件和对应的JSON配置文件配对上传</small>
            </div>
            <div class="card-body">
                <form action="/admin/resources/batch-upload" method="post" enctype="multipart/form-data" id="uploadForm">
                    
                    <!-- 文件选择区域 -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-images"></i>
                            选择文件 <span class="text-danger">*</span>
                        </label>
                        <div class="file-drop-zone" id="dropZone" onclick="document.getElementById('filesInput').click();">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>拖拽文件到此处或点击选择</h5>
                            <p class="text-muted">
                                支持图片文件(JPG, PNG, GIF)和JSON配置文件<br>
                                <strong>文件命名规则：</strong> 图片和JSON文件需要同名（如：house1.png 和 house1.json）
                            </p>
                            <input type="file" name="files" id="filesInput" class="d-none"
                                   accept="image/*,.json" multiple required onchange="handleFileSelect(this)">
                            <button type="button" class="btn btn-primary" id="selectFilesBtn" onclick="document.getElementById('filesInput').click();">
                                <i class="fas fa-folder-open"></i>
                                选择文件
                            </button>
                        </div>
                        
                        <!-- 文件列表 -->
                        <div id="filesList" class="mt-3" style="display: none;">
                            <div class="alert alert-light">
                                <h6><i class="fas fa-list"></i> 已选择的文件：</h6>
                                <div id="filesContainer"></div>
                                <div id="pairingStatus" class="mt-2"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 分类选择 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-tags"></i>
                            选择分类 <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" name="categoryId" id="categorySelect" required>
                            <option value="">请选择分类</option>
                            <option th:each="category : ${categoryDTOs}" 
                                    th:value="${category.id}" 
                                    th:text="${category.name + ' (' + category.nameEn + ')'}">分类名称</option>
                        </select>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            文件将按照英文分类名存储到对应目录
                        </div>
                    </div>

                    <div class="row">
                        <!-- 难度等级 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-star"></i>
                                难度等级 <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" name="difficulty" required>
                                <option value="">请选择难度</option>
                                <option value="1">⭐ 简单</option>
                                <option value="2">⭐⭐ 容易</option>
                                <option value="3" selected>⭐⭐⭐ 中等</option>
                                <option value="4">⭐⭐⭐⭐ 困难</option>
                                <option value="5">⭐⭐⭐⭐⭐ 专家</option>
                            </select>
                        </div>

                        <!-- 版本号 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-code-branch"></i>
                                版本号 <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" name="version" 
                                   th:value="${currentVersion}" required>
                            <div class="form-text">当前版本: <span th:text="${currentVersion}"></span></div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-success" id="submitBtn" disabled>
                            <i class="fas fa-upload"></i>
                            开始批量上传
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle"></i>
                    使用说明
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-file-image"></i> 文件要求</h6>
                        <ul class="small">
                            <li>支持图片格式：JPG, PNG, GIF</li>
                            <li>支持JSON配置文件</li>
                            <li>单个文件最大10MB</li>
                            <li>总上传大小最大50MB</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-link"></i> 文件配对</h6>
                        <ul class="small">
                            <li>图片和JSON文件需要同名</li>
                            <li>例如：house1.png 配对 house1.json</li>
                            <li>系统会自动校验文件配对</li>
                            <li>缺少配对的文件会显示警告</li>
                        </ul>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6><i class="fas fa-folder"></i> 存储规则</h6>
                        <ul class="small">
                            <li>文件按英文分类名存储</li>
                            <li>自动生成资源名称和描述</li>
                            <li>重复资源会被跳过</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-cogs"></i> 处理流程</h6>
                        <ul class="small">
                            <li>1. 选择文件并校验配对</li>
                            <li>2. 选择分类和设置参数</li>
                            <li>3. 批量上传并存储</li>
                            <li>4. 显示处理结果和警告</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let filesList, filesContainer, pairingStatus, submitBtn;

        // 全局文件处理函数
        function handleFiles(files) {
            selectedFiles = Array.from(files);
            displayFiles();
            validateFilePairing();
            updateSubmitButton();
        }

        // 全局文件选择处理函数
        function handleFileSelect(input) {
            handleFiles(input.files);
        }

        document.addEventListener('DOMContentLoaded', function() {
            const dropZone = document.getElementById('dropZone');
            const filesInput = document.getElementById('filesInput');
            const selectFilesBtn = document.getElementById('selectFilesBtn');

            // 初始化全局变量
            filesList = document.getElementById('filesList');
            filesContainer = document.getElementById('filesContainer');
            pairingStatus = document.getElementById('pairingStatus');
            submitBtn = document.getElementById('submitBtn');

            // 拖拽功能
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });
        });

        // 全局显示文件函数
        function displayFiles() {
                if (selectedFiles.length === 0) {
                    filesList.style.display = 'none';
                    return;
                }

                filesList.style.display = 'block';
                filesContainer.innerHTML = '';

                selectedFiles.forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item d-flex justify-content-between align-items-center p-2 border rounded mb-2';
                    
                    const fileInfo = document.createElement('div');
                    const fileType = file.name.toLowerCase().endsWith('.json') ? 'JSON' : '图片';
                    const fileIcon = file.name.toLowerCase().endsWith('.json') ? 'fa-file-code' : 'fa-file-image';
                    
                    fileInfo.innerHTML = `
                        <i class="fas ${fileIcon} me-2"></i>
                        <strong>${file.name}</strong>
                        <span class="badge bg-secondary ms-2">${fileType}</span>
                        <small class="text-muted ms-2">(${(file.size / 1024).toFixed(1)} KB)</small>
                    `;
                    
                    fileItem.appendChild(fileInfo);
                    filesContainer.appendChild(fileItem);
                });
        }

        // 全局文件配对验证函数
        function validateFilePairing() {
                const imageFiles = new Map();
                const jsonFiles = new Map();
                
                selectedFiles.forEach(file => {
                    const baseName = getBaseName(file.name);
                    const isJson = file.name.toLowerCase().endsWith('.json');
                    
                    if (isJson) {
                        jsonFiles.set(baseName, file);
                    } else {
                        imageFiles.set(baseName, file);
                    }
                });

                let pairedCount = 0;
                let warnings = [];

                // 检查配对
                imageFiles.forEach((file, baseName) => {
                    if (jsonFiles.has(baseName)) {
                        pairedCount++;
                    } else {
                        warnings.push(`图片 ${file.name} 缺少对应的JSON文件`);
                    }
                });

                jsonFiles.forEach((file, baseName) => {
                    if (!imageFiles.has(baseName)) {
                        warnings.push(`JSON文件 ${file.name} 缺少对应的图片文件`);
                    }
                });

                // 显示配对状态
                let statusHtml = `<strong>配对状态：</strong> ${pairedCount} 个有效文件对`;
                if (warnings.length > 0) {
                    statusHtml += `<div class="text-warning mt-2"><i class="fas fa-exclamation-triangle"></i> 警告：</div>`;
                    statusHtml += '<ul class="small mb-0">';
                    warnings.forEach(warning => {
                        statusHtml += `<li>${warning}</li>`;
                    });
                    statusHtml += '</ul>';
                }

            pairingStatus.innerHTML = statusHtml;
        }

        // 全局获取基础文件名函数
        function getBaseName(filename) {
            const lastDot = filename.lastIndexOf('.');
            return lastDot > 0 ? filename.substring(0, lastDot) : filename;
        }

        // 全局更新提交按钮函数
        function updateSubmitButton() {
            if (submitBtn) {
                submitBtn.disabled = selectedFiles.length === 0;
            }
        }

        // 表单提交验证（在DOM加载后设置）
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('uploadForm').addEventListener('submit', function(e) {
                if (selectedFiles.length === 0) {
                    alert('请选择要上传的文件');
                    e.preventDefault();
                    return;
                }

                // 显示上传进度
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 上传中...';
                    submitBtn.disabled = true;
                }
            });
        });
    </script>

    <style>
        .file-drop-zone {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-drop-zone:hover {
            border-color: #0d6efd;
            background-color: #f8f9ff;
        }

        .file-drop-zone:hover,
        .file-drop-zone.dragover {
            border-color: #0d6efd;
            background-color: #f8f9ff;
        }

        .file-item {
            background-color: #f8f9fa;
        }
    </style>
</body>
</html>
