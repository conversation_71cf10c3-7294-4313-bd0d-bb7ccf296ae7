<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: layout(~{::title}, ~{::content})}">
<head>
    <title>批量上传测试 - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-upload"></i>
                批量上传测试
            </h2>
            <a th:href="@{/admin/resources/list}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i>
                查看资源列表
            </a>
        </div>

        <!-- 成功消息 -->
        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            <span th:text="${successMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 警告消息 -->
        <div th:if="${warningMessages}" class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>注意事项：</strong>
            <ul class="mb-0 mt-2">
                <li th:each="warning : ${warningMessages}" th:text="${warning}"></li>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 错误消息 -->
        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            <span th:text="${errorMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload"></i>
                    批量上传资源（测试版）
                </h5>
            </div>
            <div class="card-body">
                <form action="/admin/resources/batch-upload" method="post" enctype="multipart/form-data" id="uploadForm">
                    
                    <!-- 简单文件选择 -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-images"></i>
                            选择文件 <span class="text-danger">*</span>
                        </label>
                        
                        <!-- 方法1：直接的文件输入 -->
                        <div class="mb-3">
                            <input type="file" name="files" class="form-control" 
                                   accept="image/*,.json" multiple required id="directFileInput">
                            <div class="form-text">直接文件选择（方法1）</div>
                        </div>

                        <!-- 方法2：按钮触发 -->
                        <div class="mb-3">
                            <input type="file" id="hiddenFileInput" class="d-none" 
                                   accept="image/*,.json" multiple>
                            <button type="button" class="btn btn-outline-primary" onclick="triggerFileSelect()">
                                <i class="fas fa-folder-open"></i>
                                点击选择文件（方法2）
                            </button>
                            <div class="form-text">按钮触发文件选择</div>
                        </div>

                        <!-- 方法3：点击区域 -->
                        <div class="border rounded p-4 text-center" style="cursor: pointer; background-color: #f8f9fa;" 
                             onclick="document.getElementById('areaFileInput').click();">
                            <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                            <p class="mb-0">点击此区域选择文件（方法3）</p>
                            <input type="file" id="areaFileInput" class="d-none" 
                                   accept="image/*,.json" multiple>
                        </div>
                        
                        <!-- 文件列表显示 -->
                        <div id="filesList" class="mt-3" style="display: none;">
                            <div class="alert alert-light">
                                <h6><i class="fas fa-list"></i> 已选择的文件：</h6>
                                <div id="filesContainer"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 分类选择 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-tags"></i>
                            选择分类 <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" name="categoryId" required>
                            <option value="">请选择分类</option>
                            <option th:each="category : ${categoryDTOs}" 
                                    th:value="${category.id}" 
                                    th:text="${category.name + ' (' + category.nameEn + ')'}">分类名称</option>
                        </select>
                    </div>

                    <div class="row">
                        <!-- 难度等级 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-star"></i>
                                难度等级 <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" name="difficulty" required>
                                <option value="">请选择难度</option>
                                <option value="1">⭐ 简单</option>
                                <option value="2">⭐⭐ 容易</option>
                                <option value="3" selected>⭐⭐⭐ 中等</option>
                                <option value="4">⭐⭐⭐⭐ 困难</option>
                                <option value="5">⭐⭐⭐⭐⭐ 专家</option>
                            </select>
                        </div>

                        <!-- 版本号 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-code-branch"></i>
                                版本号 <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" name="version" 
                                   th:value="${currentVersion}" required>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-upload"></i>
                            开始批量上传
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 方法2：按钮触发文件选择
        function triggerFileSelect() {
            console.log('触发文件选择');
            document.getElementById('hiddenFileInput').click();
        }

        // 文件选择处理
        function handleFileDisplay(files, containerId) {
            const container = document.getElementById(containerId);
            const filesList = document.getElementById('filesList');
            
            if (files.length > 0) {
                filesList.style.display = 'block';
                container.innerHTML = '';
                
                Array.from(files).forEach(file => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'mb-1';
                    fileItem.innerHTML = `
                        <i class="fas fa-file me-2"></i>
                        <strong>${file.name}</strong>
                        <small class="text-muted ms-2">(${(file.size / 1024).toFixed(1)} KB)</small>
                    `;
                    container.appendChild(fileItem);
                });
            } else {
                filesList.style.display = 'none';
            }
        }

        // 为所有文件输入添加事件监听
        document.addEventListener('DOMContentLoaded', function() {
            // 直接文件输入
            document.getElementById('directFileInput').addEventListener('change', function(e) {
                console.log('直接文件选择:', e.target.files.length);
                handleFileDisplay(e.target.files, 'filesContainer');
            });

            // 隐藏文件输入
            document.getElementById('hiddenFileInput').addEventListener('change', function(e) {
                console.log('按钮文件选择:', e.target.files.length);
                handleFileDisplay(e.target.files, 'filesContainer');
                // 将选择的文件复制到表单的files字段
                document.getElementById('directFileInput').files = e.target.files;
            });

            // 区域文件输入
            document.getElementById('areaFileInput').addEventListener('change', function(e) {
                console.log('区域文件选择:', e.target.files.length);
                handleFileDisplay(e.target.files, 'filesContainer');
                // 将选择的文件复制到表单的files字段
                document.getElementById('directFileInput').files = e.target.files;
            });
        });

        // 表单提交前验证
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            const files = document.getElementById('directFileInput').files;
            if (files.length === 0) {
                alert('请选择要上传的文件');
                e.preventDefault();
                return false;
            }
            console.log('提交表单，文件数量:', files.length);
        });
    </script>
</body>
</html>
