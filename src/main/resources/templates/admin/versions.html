<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本管理 - Dreaming涂色App管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .version-card {
            transition: transform 0.2s;
        }
        .version-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">
                <i class="fas fa-palette"></i>
                Dreaming涂色App 管理后台
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/swagger-ui.html" target="_blank">
                    <i class="fas fa-book"></i> API文档
                </a>
                <a class="nav-link" href="/h2-console" target="_blank">
                    <i class="fas fa-database"></i> 数据库
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar p-0">
                <div class="list-group list-group-flush">
                    <a href="/admin" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-tachometer-alt"></i> 仪表盘
                    </a>
                    <a href="/admin/resources" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-images"></i> 资源管理
                    </a>
                    <a href="/admin/resources/upload" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-upload"></i> 上传资源
                    </a>
                    <a href="/admin/versions" class="list-group-item list-group-item-action bg-transparent text-white active">
                        <i class="fas fa-code-branch"></i> 版本管理
                    </a>
                    <a href="/admin/export" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-download"></i> 数据导出
                    </a>
                    <a href="/admin/settings" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-cog"></i> 系统设置
                    </a>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10">
                <div class="container-fluid py-4">
                    <!-- 页面标题和操作按钮 -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>版本管理</h2>
            <a th:href="@{/admin/versions/create}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 创建新版本
            </a>
        </div>

        <!-- 当前版本信息 -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-star"></i>
                    当前版本
                </h5>
            </div>
            <div class="card-body">
                <div th:if="${currentVersion != null}">
                    <div class="row">
                        <div class="col-md-6">
                            <h4 th:text="${currentVersion.version}">1.0.0</h4>
                            <h6 class="text-muted" th:text="${currentVersion.versionName}">版本名称</h6>
                            <p th:text="${currentVersion.description}">版本描述</p>
                        </div>
                        <div class="col-md-6">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="border-end">
                                        <h5 th:text="${currentVersion.resourceCount}">0</h5>
                                        <small class="text-muted">资源数量</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border-end">
                                        <h5 th:text="${#numbers.formatDecimal(currentVersion.totalSize / 1024 / 1024, 1, 2)} + ' MB'">0 MB</h5>
                                        <small class="text-muted">总大小</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <h5>
                                        <span th:if="${currentVersion.forceUpdate}" class="badge bg-danger">强制</span>
                                        <span th:unless="${currentVersion.forceUpdate}" class="badge bg-success">可选</span>
                                    </h5>
                                    <small class="text-muted">更新类型</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div th:unless="${currentVersion != null}" class="text-center text-muted py-3">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>尚未设置当前版本</p>
                </div>
            </div>
        </div>

        <!-- 版本列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i>
                    所有版本
                    <span class="badge bg-secondary ms-2" th:text="${#lists.size(versions)}">0</span>
                </h5>
            </div>
            <div class="card-body">
                <div th:if="${#lists.isEmpty(versions)}" class="text-center py-5">
                    <i class="fas fa-code-branch fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无版本</h5>
                    <p class="text-muted">点击上方按钮创建您的第一个版本</p>
                </div>

                <div th:unless="${#lists.isEmpty(versions)}">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>版本号</th>
                                    <th>版本名称</th>
                                    <th>描述</th>
                                    <th>资源数量</th>
                                    <th>总大小</th>
                                    <th>更新类型</th>
                                    <th>发布时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="version : ${versions}">
                                    <td>
                                        <strong th:text="${version.version}">1.0.0</strong>
                                    </td>
                                    <td th:text="${version.versionName}">版本名称</td>
                                    <td>
                                        <span th:text="${#strings.abbreviate(version.description, 50)}" 
                                              th:title="${version.description}">描述</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info" th:text="${version.resourceCount}">0</span>
                                    </td>
                                    <td th:text="${#numbers.formatDecimal(version.totalSize / 1024 / 1024, 1, 2)} + ' MB'">0 MB</td>
                                    <td>
                                        <span th:if="${version.forceUpdate}" class="badge bg-danger">强制更新</span>
                                        <span th:unless="${version.forceUpdate}" class="badge bg-success">可选更新</span>
                                    </td>
                                    <td>
                                        <small th:text="${#temporals.format(version.releaseTime, 'yyyy-MM-dd HH:mm')}">2023-01-01 12:00</small>
                                    </td>
                                    <td>
                                        <span th:if="${version.isCurrent}" class="badge bg-success">
                                            <i class="fas fa-star"></i> 当前版本
                                        </span>
                                        <span th:unless="${version.isCurrent}" class="badge bg-secondary">历史版本</span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <!-- 设置为当前版本 -->
                                            <button th:unless="${version.isCurrent}"
                                                    type="button" 
                                                    class="btn btn-outline-success btn-sm"
                                                    th:onclick="'setCurrentVersion(\'' + ${version.version} + '\')'">
                                                <i class="fas fa-star"></i>
                                            </button>
                                            
                                            <!-- 更新统计 -->
                                            <button type="button" 
                                                    class="btn btn-outline-info btn-sm"
                                                    th:onclick="'updateStats(\'' + ${version.version} + '\')'">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                            
                                            <!-- 删除版本 -->
                                            <button th:unless="${version.isCurrent}"
                                                    type="button" 
                                                    class="btn btn-outline-danger btn-sm"
                                                    th:onclick="'deleteVersion(\'' + ${version.version} + '\')'">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 版本统计 -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">总版本数</h5>
                        <h2 class="text-primary" th:text="${#lists.size(versions)}">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">强制更新版本</h5>
                        <h2 class="text-danger" th:text="${#lists.size(#lists.select(versions, version.forceUpdate))}">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">总资源数</h5>
                        <h2 class="text-success" th:text="${#aggregates.sum(versions.![resourceCount])}">0</h2>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function setCurrentVersion(version) {
            if (confirm('确定要将版本 ' + version + ' 设置为当前版本吗？')) {
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/versions/' + version + '/current';
                document.body.appendChild(form);
                form.submit();
            }
        }

        function updateStats(version) {
            if (confirm('确定要更新版本 ' + version + ' 的统计信息吗？')) {
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/versions/' + version + '/stats';
                document.body.appendChild(form);
                form.submit();
            }
        }

        function deleteVersion(version) {
            if (confirm('确定要删除版本 ' + version + ' 吗？\n\n注意：此操作不可恢复！')) {
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/versions/' + version + '/delete';
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
