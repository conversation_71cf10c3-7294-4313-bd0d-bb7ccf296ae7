<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="layout/base :: layout(~{::title}, ~{::content})">
<head>
    <title>系统设置 - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-cog"></i>
                系统设置
            </h2>
        </div>

        <div class="row">
            <!-- 系统信息 -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i>
                            系统信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">Java版本:</td>
                                        <td th:text="${systemInfo.javaVersion}">11.0.15</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">操作系统:</td>
                                        <td th:text="${systemInfo.osName + ' ' + systemInfo.osVersion}">Windows 10</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">服务端口:</td>
                                        <td th:text="${systemInfo.serverPort}">8080</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">数据库:</td>
                                        <td th:text="${systemInfo.datasourceUrl}">jdbc:h2:mem:testdb</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">上传目录:</td>
                                        <td th:text="${systemInfo.uploadDir}">./uploads</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">文件基础URL:</td>
                                        <td th:text="${systemInfo.baseUrl}">http://localhost:8080/api/files</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">目录状态:</td>
                                        <td>
                                            <span th:if="${systemInfo.uploadDirExists}" class="badge bg-success">存在</span>
                                            <span th:unless="${systemInfo.uploadDirExists}" class="badge bg-danger">不存在</span>
                                            <span th:if="${systemInfo.uploadDirWritable}" class="badge bg-success">可写</span>
                                            <span th:unless="${systemInfo.uploadDirWritable}" class="badge bg-warning">只读</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 资源使用情况 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie"></i>
                            资源使用情况
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- 内存使用 -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-bold">内存使用</span>
                                <span class="text-muted">
                                    <span th:text="${systemInfo.usedMemory}">0 MB</span> / 
                                    <span th:text="${systemInfo.totalMemory}">0 MB</span>
                                </span>
                            </div>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar" 
                                     role="progressbar" 
                                     th:style="'width: ' + ${systemInfo.memoryUsagePercent} + '%'"
                                     th:classappend="${systemInfo.memoryUsagePercent > 80} ? 'bg-danger' : (${systemInfo.memoryUsagePercent > 60} ? 'bg-warning' : 'bg-success')">
                                    <span th:text="${systemInfo.memoryUsagePercent} + '%'">0%</span>
                                </div>
                            </div>
                        </div>

                        <!-- 磁盘使用 -->
                        <div th:if="${systemInfo.diskTotal != null}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-bold">磁盘使用</span>
                                <span class="text-muted">
                                    <span th:text="${systemInfo.diskUsed}">0 GB</span> / 
                                    <span th:text="${systemInfo.diskTotal}">0 GB</span>
                                </span>
                            </div>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar" 
                                     role="progressbar" 
                                     th:style="'width: ' + ${systemInfo.diskUsagePercent} + '%'"
                                     th:classappend="${systemInfo.diskUsagePercent > 90} ? 'bg-danger' : (${systemInfo.diskUsagePercent > 70} ? 'bg-warning' : 'bg-success')">
                                    <span th:text="${systemInfo.diskUsagePercent} + '%'">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统维护 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tools"></i>
                            系统维护
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-broom fa-2x text-warning mb-2"></i>
                                        <h6>清理临时文件</h6>
                                        <p class="text-muted small">清理系统产生的临时文件和缓存</p>
                                        <form method="post" th:action="@{/admin/settings/cleanup}" style="display: inline;">
                                            <button type="submit" class="btn btn-warning btn-sm" onclick="return confirm('确定要清理临时文件吗？')">
                                                <i class="fas fa-broom"></i> 开始清理
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-calculator fa-2x text-info mb-2"></i>
                                        <h6>重新计算统计</h6>
                                        <p class="text-muted small">重新计算资源下载次数等统计信息</p>
                                        <form method="post" th:action="@{/admin/settings/recalculate-stats}" style="display: inline;">
                                            <button type="submit" class="btn btn-info btn-sm" onclick="return confirm('确定要重新计算统计信息吗？')">
                                                <i class="fas fa-calculator"></i> 开始计算
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速链接 -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-external-link-alt"></i>
                            快速链接
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a th:href="@{/api/swagger-ui.html}" target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-book"></i> API文档
                            </a>
                            <a th:href="@{/api/h2-console}" target="_blank" class="btn btn-outline-info">
                                <i class="fas fa-database"></i> 数据库控制台
                            </a>
                            <a th:href="@{/api/health}" target="_blank" class="btn btn-outline-success">
                                <i class="fas fa-heartbeat"></i> 健康检查
                            </a>
                            <a th:href="@{/admin/export}" class="btn btn-outline-warning">
                                <i class="fas fa-download"></i> 数据导出
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 系统状态 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-heartbeat"></i>
                            系统状态
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="border-end">
                                    <div class="status-indicator bg-success"></div>
                                    <h6 class="mt-2">Web服务</h6>
                                    <small class="text-muted">正常运行</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="status-indicator bg-success"></div>
                                <h6 class="mt-2">数据库</h6>
                                <small class="text-muted">连接正常</small>
                            </div>
                            <div class="col-6">
                                <div class="border-end">
                                    <div class="status-indicator" 
                                         th:classappend="${systemInfo.uploadDirExists and systemInfo.uploadDirWritable} ? 'bg-success' : 'bg-warning'"></div>
                                    <h6 class="mt-2">文件存储</h6>
                                    <small class="text-muted" 
                                           th:text="${systemInfo.uploadDirExists and systemInfo.uploadDirWritable} ? '正常' : '异常'">正常</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="status-indicator bg-success"></div>
                                <h6 class="mt-2">API服务</h6>
                                <small class="text-muted">正常运行</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 版本信息 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tag"></i>
                            版本信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td class="fw-bold">应用版本:</td>
                                <td>1.0.0</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Spring Boot:</td>
                                <td>3.2.0</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">构建时间:</td>
                                <td>2024-01-01</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin: 0 auto;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>

    <script>
        // 定期刷新系统状态
        setInterval(function() {
            // 这里可以添加AJAX请求来更新系统状态
            console.log('系统状态检查...');
        }, 30000); // 每30秒检查一次
    </script>
</body>
</html>
