<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源管理 - Dreaming涂色App管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .resource-card {
            transition: transform 0.2s;
        }
        .resource-card:hover {
            transform: translateY(-2px);
        }
        .resource-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin">
                <i class="fas fa-palette"></i>
                Dreaming涂色App 管理后台
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/swagger-ui.html" target="_blank">
                    <i class="fas fa-book"></i> API文档
                </a>
                <a class="nav-link" href="/h2-console" target="_blank">
                    <i class="fas fa-database"></i> 数据库
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar p-0">
                <div class="list-group list-group-flush">
                    <a href="/admin" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-tachometer-alt"></i> 仪表盘
                    </a>
                    <a href="/admin/resources" class="list-group-item list-group-item-action bg-transparent text-white active">
                        <i class="fas fa-images"></i> 资源管理
                    </a>
                    <a href="/admin/resources/upload" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-upload"></i> 上传资源
                    </a>
                    <a href="/admin/versions" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-code-branch"></i> 版本管理
                    </a>
                    <a href="/admin/export" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-download"></i> 数据导出
                    </a>
                    <a href="/admin/settings" class="list-group-item list-group-item-action bg-transparent text-white">
                        <i class="fas fa-cog"></i> 系统设置
                    </a>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10">
                <div class="container-fluid py-4">
                    <!-- 页面标题和操作按钮 -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>资源管理</h2>
            <div>
                <form method="post" action="/admin/resources/migrate" style="display: inline;" 
                      onsubmit="return confirm('确定要迁移所有资源到正确的分类文件夹吗？这个操作可能需要一些时间。')">
                    <button type="submit" class="btn btn-warning me-2">
                        <i class="fas fa-sync"></i> 迁移文件夹
                    </button>
                </form>
                <a th:href="@{/admin/resources/upload}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 上传新资源
                </a>
            </div>
        </div>

        <!-- 筛选和搜索 -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="get" th:action="@{/admin/resources}">
                    <div class="row align-items-end">
                        <div class="col-md-4">
                            <label for="category" class="form-label">分类筛选</label>
                            <select name="category" id="category" class="form-select">
                                <option value="">全部分类</option>
                                <option th:each="cat : ${categories}" 
                                        th:value="${cat}" 
                                        th:text="${cat}"
                                        th:selected="${cat == selectedCategory}">分类</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="pageSize" class="form-label">每页显示</label>
                            <select name="size" id="pageSize" class="form-select">
                                <option value="10">10条</option>
                                <option value="20" selected>20条</option>
                                <option value="50">50条</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-filter"></i> 筛选
                            </button>
                            <a th:href="@{/admin/resources}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> 清除
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 资源列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-images"></i>
                    资源列表
                    <span class="badge bg-secondary ms-2" th:text="${resources.totalElements}">0</span>
                </h5>
            </div>
            <div class="card-body">
                <div th:if="${resources.empty}" class="text-center py-5">
                    <i class="fas fa-image fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无资源</h5>
                    <p class="text-muted">点击上方按钮开始上传您的第一个涂色资源</p>
                </div>

                <div th:unless="${resources.empty}">
                    <!-- 资源网格 -->
                    <div class="row">
                        <div th:each="resource : ${resources.content}" class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="card h-100">
                                <div class="position-relative">
                                    <img th:src="${resource.downloadUrl}" 
                                         class="card-img-top" 
                                         style="height: 200px; object-fit: cover;"
                                         th:alt="${resource.name}"
                                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMDAgODBDMTA1LjUyMyA4MCAxMTAgNzUuNTIzIDExMCA3MEMxMTAgNjQuNDc3IDEwNS41MjMgNjAgMTAwIDYwQzk0LjQ3NyA2MCA5MCA2NC40NzcgOTAgNzBDOTAgNzUuNTIzIDk0LjQ3NyA4MCAxMDAgODBaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0xNDAgMTQwSDYwTDgwIDEwMEwxMDAgMTIwTDEyMCA4MEwxNDAgMTQwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'">
                                    
                                    <!-- 状态标识 -->
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span th:if="${resource.active}" class="badge bg-success">激活</span>
                                        <span th:unless="${resource.active}" class="badge bg-secondary">停用</span>
                                    </div>
                                    
                                    <!-- 难度标识 -->
                                    <div class="position-absolute top-0 start-0 m-2">
                                        <span class="badge bg-primary">
                                            难度 <span th:text="${resource.difficulty}">1</span>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title" th:text="${resource.name}">资源名称</h6>
                                    <p class="card-text text-muted small flex-grow-1" 
                                       th:text="${#strings.abbreviate(resource.description, 50)}">资源描述</p>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-tag"></i> <span th:text="${resource.category}">分类</span> |
                                            <i class="fas fa-download"></i> <span th:text="${resource.downloadCount}">0</span>
                                        </small>
                                    </div>
                                    
                                    <div class="btn-group w-100" role="group">
                                        <a th:href="@{'/admin/resources/' + ${resource.id}}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-outline-warning btn-sm"
                                                th:onclick="'toggleStatus(' + ${resource.id} + ')'">
                                            <i class="fas fa-power-off"></i>
                                        </button>
                                        <button type="button" 
                                                class="btn btn-outline-danger btn-sm"
                                                th:onclick="'deleteResource(' + ${resource.id} + ', \'' + ${resource.name} + '\')'">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分页 -->
                    <nav th:if="${resources.totalPages > 1}" aria-label="资源分页">
                        <ul class="pagination justify-content-center">
                            <li class="page-item" th:classappend="${resources.first} ? 'disabled'">
                                <a class="page-link" 
                                   th:href="@{/admin/resources(page=${currentPage - 1}, size=${resources.size}, category=${selectedCategory})}">
                                    上一页
                                </a>
                            </li>
                            
                            <li th:each="pageNum : ${#numbers.sequence(0, resources.totalPages - 1)}"
                                class="page-item"
                                th:classappend="${pageNum == currentPage} ? 'active'">
                                <a class="page-link" 
                                   th:href="@{/admin/resources(page=${pageNum}, size=${resources.size}, category=${selectedCategory})}"
                                   th:text="${pageNum + 1}">1</a>
                            </li>
                            
                            <li class="page-item" th:classappend="${resources.last} ? 'disabled'">
                                <a class="page-link" 
                                   th:href="@{/admin/resources(page=${currentPage + 1}, size=${resources.size}, category=${selectedCategory})}">
                                    下一页
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除资源 "<span id="deleteResourceName"></span>" 吗？</p>
                    <p class="text-danger">此操作不可恢复，文件也会被永久删除。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <form id="deleteForm" method="post" style="display: inline;">
                        <button type="submit" class="btn btn-danger">确认删除</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function deleteResource(id, name) {
            document.getElementById('deleteResourceName').textContent = name;
            document.getElementById('deleteForm').action = '/admin/resources/' + id + '/delete';
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function toggleStatus(id) {
            if (confirm('确定要切换资源状态吗？')) {
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/resources/' + id + '/toggle';
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
