<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: layout(~{::title}, ~{::content})}">
<head>
    <title>批量上传（最小版） - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-upload"></i>
                批量上传资源（最小版）
            </h2>
            <a th:href="@{/admin/resources/list}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i>
                查看资源列表
            </a>
        </div>

        <!-- 成功消息 -->
        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            <span th:text="${successMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 警告消息 -->
        <div th:if="${warningMessages}" class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>注意事项：</strong>
            <ul class="mb-0 mt-2">
                <li th:each="warning : ${warningMessages}" th:text="${warning}"></li>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 错误消息 -->
        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            <span th:text="${errorMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload"></i>
                    批量上传资源
                </h5>
            </div>
            <div class="card-body">
                <form action="/admin/resources/batch-upload" method="post" enctype="multipart/form-data">
                    
                    <!-- 文件选择 -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-images"></i>
                            选择文件 <span class="text-danger">*</span>
                        </label>
                        
                        <!-- 直接文件输入 -->
                        <input type="file" name="files" class="form-control mb-3" 
                               accept="image/*,.json" multiple required id="fileInput">
                        
                        <!-- 按钮方式 -->
                        <div class="mb-3">
                            <input type="file" id="hiddenInput" class="d-none" 
                                   accept="image/*,.json" multiple>
                            <button type="button" class="btn btn-outline-primary" onclick="selectFiles()">
                                <i class="fas fa-folder-open"></i>
                                点击选择文件
                            </button>
                        </div>

                        <!-- 文件列表 -->
                        <div id="fileList" class="mt-3" style="display: none;">
                            <div class="alert alert-light">
                                <h6><i class="fas fa-list"></i> 已选择的文件：</h6>
                                <div id="fileContainer"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 分类选择 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-tags"></i>
                            选择分类 <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" name="categoryId" required>
                            <option value="">请选择分类</option>
                            <option th:each="category : ${categoryDTOs}" 
                                    th:value="${category.id}" 
                                    th:text="${category.name + ' (' + category.nameEn + ')'}">分类名称</option>
                        </select>
                    </div>

                    <div class="row">
                        <!-- 难度等级 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-star"></i>
                                难度等级 <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" name="difficulty" required>
                                <option value="">请选择难度</option>
                                <option value="1">⭐ 简单</option>
                                <option value="2">⭐⭐ 容易</option>
                                <option value="3" selected>⭐⭐⭐ 中等</option>
                                <option value="4">⭐⭐⭐⭐ 困难</option>
                                <option value="5">⭐⭐⭐⭐⭐ 专家</option>
                            </select>
                        </div>

                        <!-- 版本号 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-code-branch"></i>
                                版本号 <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" name="version" 
                                   th:value="${currentVersion}" required>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-upload"></i>
                            开始批量上传
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle"></i>
                    使用说明
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-file-image"></i> 文件要求</h6>
                        <ul class="small">
                            <li>支持图片格式：JPG, PNG, GIF</li>
                            <li>支持JSON配置文件</li>
                            <li>文件命名规则：图片和JSON文件需要同名</li>
                            <li>例如：house1.png 配对 house1.json</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-folder"></i> 存储规则</h6>
                        <ul class="small">
                            <li>文件按英文分类名存储</li>
                            <li>自动生成资源名称和描述</li>
                            <li>重复资源会被跳过</li>
                            <li>系统会自动校验文件配对</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 按钮选择文件
        function selectFiles() {
            console.log('selectFiles 函数被调用');
            document.getElementById('hiddenInput').click();
        }

        // 显示文件列表
        function showFiles(files) {
            const fileList = document.getElementById('fileList');
            const fileContainer = document.getElementById('fileContainer');
            
            if (files.length > 0) {
                fileList.style.display = 'block';
                fileContainer.innerHTML = '';
                
                Array.from(files).forEach((file, index) => {
                    const fileDiv = document.createElement('div');
                    fileDiv.className = 'mb-1';
                    fileDiv.innerHTML = `
                        <i class="fas fa-file me-2"></i>
                        <strong>${file.name}</strong>
                        <small class="text-muted ms-2">(${(file.size / 1024).toFixed(1)} KB)</small>
                    `;
                    fileContainer.appendChild(fileDiv);
                });
            } else {
                fileList.style.display = 'none';
            }
        }

        // 页面加载完成后设置事件监听
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            
            // 直接文件输入监听
            document.getElementById('fileInput').addEventListener('change', function(e) {
                console.log('直接文件输入变化:', e.target.files.length);
                showFiles(e.target.files);
            });

            // 隐藏文件输入监听
            document.getElementById('hiddenInput').addEventListener('change', function(e) {
                console.log('隐藏文件输入变化:', e.target.files.length);
                showFiles(e.target.files);
                // 将文件复制到主输入框
                const mainInput = document.getElementById('fileInput');
                mainInput.files = e.target.files;
            });
        });
    </script>
</body>
</html>
