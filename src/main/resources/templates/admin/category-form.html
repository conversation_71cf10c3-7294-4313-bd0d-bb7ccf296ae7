<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: layout(~{::title}, ~{::content})}">
<head>
    <title th:text="${isEdit ? '编辑分类' : '创建分类'} + ' - Dreaming涂色App管理后台'">分类管理 - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-tags"></i>
                <span th:text="${isEdit ? '编辑分类' : '创建分类'}">创建分类</span>
            </h2>
            <a th:href="@{/admin/categories}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i>
                返回列表
            </a>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit"></i>
                            分类信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <form th:action="${isEdit ? '/admin/categories/' + category.id + '/update' : '/admin/categories/create'}" 
                              method="post" th:object="${category}">
                            
                            <!-- 中文名称 -->
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-font"></i>
                                    中文名称 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" th:field="*{name}" 
                                       placeholder="请输入分类的中文名称" required>
                                <div class="form-text">分类的中文显示名称，用于管理后台显示</div>
                            </div>

                            <!-- 英文名称 -->
                            <div class="mb-3">
                                <label for="nameEn" class="form-label">
                                    <i class="fas fa-globe"></i>
                                    英文名称 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="nameEn" th:field="*{nameEn}" 
                                       placeholder="请输入分类的英文名称" required>
                                <div class="form-text">分类的英文名称，用于API通信和文件存储目录名</div>
                            </div>

                            <!-- 描述 -->
                            <div class="mb-3">
                                <label for="description" class="form-label">
                                    <i class="fas fa-align-left"></i>
                                    描述
                                </label>
                                <textarea class="form-control" id="description" th:field="*{description}" 
                                          rows="3" placeholder="请输入分类描述（可选）"></textarea>
                                <div class="form-text">分类的详细描述，帮助用户了解分类内容</div>
                            </div>

                            <!-- 图标URL -->
                            <div class="mb-3">
                                <label for="iconUrl" class="form-label">
                                    <i class="fas fa-image"></i>
                                    图标URL
                                </label>
                                <input type="url" class="form-control" id="iconUrl" th:field="*{iconUrl}" 
                                       placeholder="请输入图标的URL地址（可选）">
                                <div class="form-text">分类图标的URL地址，建议使用32x32像素的图标</div>
                            </div>

                            <!-- 排序顺序 -->
                            <div class="mb-3">
                                <label for="sortOrder" class="form-label">
                                    <i class="fas fa-sort-numeric-down"></i>
                                    排序顺序
                                </label>
                                <input type="number" class="form-control" id="sortOrder" th:field="*{sortOrder}" 
                                       value="0" min="0" max="999">
                                <div class="form-text">数字越小排序越靠前，默认为0</div>
                            </div>

                            <!-- 状态 -->
                            <div class="mb-4">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="isActive" 
                                           th:field="*{isActive}" th:checked="${category.isActive == null ? true : category.isActive}">
                                    <label class="form-check-label" for="isActive">
                                        <i class="fas fa-toggle-on"></i>
                                        激活状态
                                    </label>
                                    <div class="form-text">只有激活的分类才会在客户端API中返回</div>
                                </div>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="d-flex justify-content-end gap-2">
                                <a th:href="@{/admin/categories}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                    取消
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    <span th:text="${isEdit ? '更新分类' : '创建分类'}">创建分类</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 右侧帮助信息 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-question-circle"></i>
                            填写说明
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6><i class="fas fa-font text-primary"></i> 中文名称</h6>
                            <p class="small text-muted">用于管理后台显示的分类名称，如"动物"、"植物"等</p>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-globe text-info"></i> 英文名称</h6>
                            <p class="small text-muted">用于API通信和文件存储，如"animals"、"plants"等，建议使用小写字母</p>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-sort-numeric-down text-warning"></i> 排序顺序</h6>
                            <p class="small text-muted">控制分类在列表中的显示顺序，数字越小越靠前</p>
                        </div>
                        
                        <div class="mb-3">
                            <h6><i class="fas fa-image text-success"></i> 图标建议</h6>
                            <p class="small text-muted">推荐使用32x32像素的PNG图标，可以使用Font Awesome图标或自定义图片</p>
                        </div>
                    </div>
                </div>

                <!-- 预览区域 -->
                <div class="card mt-3" th:if="${isEdit}">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-eye"></i>
                            分类预览
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div th:if="${category.iconUrl != null and category.iconUrl != ''}">
                                    <img th:src="${category.iconUrl}" alt="分类图标" 
                                         class="rounded" style="width: 32px; height: 32px; object-fit: cover;">
                                </div>
                                <div th:unless="${category.iconUrl != null and category.iconUrl != ''}">
                                    <i class="fas fa-image text-muted" style="font-size: 24px;"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-1" th:text="${category.name}">分类名称</h6>
                                <small class="text-muted" th:text="${category.nameEn}">english-name</small>
                            </div>
                        </div>
                        <p class="small text-muted mt-2 mb-0" th:text="${category.description}">分类描述</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义JavaScript -->
    <script>
        // 实时预览功能
        document.addEventListener('DOMContentLoaded', function() {
            // 英文名称自动转换为小写
            const nameEnInput = document.getElementById('nameEn');
            if (nameEnInput) {
                nameEnInput.addEventListener('input', function() {
                    this.value = this.value.toLowerCase().replace(/[^a-z0-9-_]/g, '');
                });
            }

            // 图标URL预览
            const iconUrlInput = document.getElementById('iconUrl');
            if (iconUrlInput) {
                iconUrlInput.addEventListener('input', function() {
                    // 这里可以添加图标预览功能
                });
            }
        });
    </script>
</body>
</html>