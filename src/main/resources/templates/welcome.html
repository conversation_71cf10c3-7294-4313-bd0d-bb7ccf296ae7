<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎使用 Dreaming涂色App后端服务</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .service-card {
            transition: transform 0.3s ease;
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .service-card:hover {
            transform: translateY(-5px);
        }
        .status-badge {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="welcome-card p-5">
                    <!-- 标题 -->
                    <div class="text-center mb-5">
                        <i class="fas fa-palette fa-4x text-primary mb-3"></i>
                        <h1 class="display-4 fw-bold text-primary">Dreaming涂色App</h1>
                        <h2 class="h3 text-secondary">后端服务管理平台</h2>
                        <div class="mt-3">
                            <span class="badge bg-success status-badge fs-6">
                                <i class="fas fa-check-circle"></i> 服务运行正常
                            </span>
                        </div>
                    </div>

                    <!-- 服务入口 -->
                    <div class="row g-4 mb-5">
                        <div class="col-md-6 col-lg-3">
                            <div class="card service-card h-100 text-center">
                                <div class="card-body">
                                    <i class="fas fa-tachometer-alt fa-3x text-primary mb-3"></i>
                                    <h5 class="card-title">管理后台</h5>
                                    <p class="card-text">资源和版本管理界面</p>
                                    <a href="/admin" class="btn btn-primary">
                                        <i class="fas fa-arrow-right"></i> 进入
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-3">
                            <div class="card service-card h-100 text-center">
                                <div class="card-body">
                                    <i class="fas fa-book fa-3x text-info mb-3"></i>
                                    <h5 class="card-title">API文档</h5>
                                    <p class="card-text">完整的接口文档和测试</p>
                                    <a href="/api/swagger-ui.html" target="_blank" class="btn btn-info">
                                        <i class="fas fa-external-link-alt"></i> 查看
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-3">
                            <div class="card service-card h-100 text-center">
                                <div class="card-body">
                                    <i class="fas fa-database fa-3x text-warning mb-3"></i>
                                    <h5 class="card-title">数据库控制台</h5>
                                    <p class="card-text">H2数据库管理界面</p>
                                    <a href="/api/h2-console" target="_blank" class="btn btn-warning">
                                        <i class="fas fa-external-link-alt"></i> 访问
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-3">
                            <div class="card service-card h-100 text-center">
                                <div class="card-body">
                                    <i class="fas fa-heartbeat fa-3x text-success mb-3"></i>
                                    <h5 class="card-title">健康检查</h5>
                                    <p class="card-text">服务状态监控接口</p>
                                    <a href="/api/health" target="_blank" class="btn btn-success">
                                        <i class="fas fa-external-link-alt"></i> 检查
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速开始 -->
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-rocket"></i> 快速开始
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <ol class="mb-0">
                                        <li class="mb-2">访问 <strong>管理后台</strong> 开始管理资源</li>
                                        <li class="mb-2">上传您的第一个涂色图片</li>
                                        <li class="mb-2">创建应用版本</li>
                                        <li class="mb-2">在Android应用中集成API</li>
                                        <li>开始为用户提供涂色资源</li>
                                    </ol>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-info-circle"></i> 系统信息
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled mb-0">
                                        <li class="mb-2">
                                            <strong>服务地址:</strong> http://localhost:8080
                                        </li>
                                        <li class="mb-2">
                                            <strong>API前缀:</strong> /api
                                        </li>
                                        <li class="mb-2">
                                            <strong>数据库:</strong> H2 (内存模式)
                                        </li>
                                        <li class="mb-2">
                                            <strong>文件存储:</strong> ./uploads
                                        </li>
                                        <li>
                                            <strong>版本:</strong> 1.0.0
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部信息 -->
                    <div class="text-center mt-5 pt-4 border-top">
                        <p class="text-muted mb-2">
                            <i class="fas fa-code"></i> 
                            Dreaming涂色App后端服务 - 为您的Android应用提供强大的资源管理能力
                        </p>
                        <p class="text-muted small">
                            需要帮助？查看 <a href="https://github.com/your-repo" target="_blank">项目文档</a> 
                            或 <a href="/api/swagger-ui.html" target="_blank">API文档</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 检查服务状态
        fetch('/api/health')
            .then(response => response.json())
            .then(data => {
                console.log('服务状态:', data);
            })
            .catch(error => {
                console.error('服务状态检查失败:', error);
                document.querySelector('.status-badge').innerHTML = 
                    '<i class="fas fa-exclamation-triangle"></i> 服务异常';
                document.querySelector('.status-badge').className = 'badge bg-danger status-badge fs-6';
            });
    </script>
</body>
</html>
