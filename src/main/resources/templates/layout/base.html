<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:fragment="layout (title, content)">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} ?: 'Dreaming涂色App管理后台'">Dreaming涂色App管理后台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin: 0.25rem 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem 1rem 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .alert {
            border-radius: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-palette"></i>
                            Dreaming管理后台
                        </h4>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#request.requestURI == '/admin' ? 'active' : ''}" 
                               th:href="@{/admin}">
                                <i class="fas fa-tachometer-alt"></i>
                                仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.startsWith(#request.requestURI, '/admin/resources') ? 'active' : ''}" 
                               th:href="@{/admin/resources}">
                                <i class="fas fa-images"></i>
                                资源管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.startsWith(#request.requestURI, '/admin/categories') ? 'active' : ''}" 
                               th:href="@{/admin/categories}">
                                <i class="fas fa-tags"></i>
                                分类管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.startsWith(#request.requestURI, '/admin/versions') ? 'active' : ''}"
                               th:href="@{/admin/versions}">
                                <i class="fas fa-code-branch"></i>
                                版本管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.startsWith(#request.requestURI, '/admin/export') ? 'active' : ''}"
                               th:href="@{/admin/export}">
                                <i class="fas fa-download"></i>
                                数据导出
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.startsWith(#request.requestURI, '/admin/settings') ? 'active' : ''}"
                               th:href="@{/admin/settings}">
                                <i class="fas fa-cog"></i>
                                系统设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/api/swagger-ui.html}" target="_blank">
                                <i class="fas fa-book"></i>
                                API文档
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/api/h2-console}" target="_blank">
                                <i class="fas fa-database"></i>
                                数据库控制台
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3 pb-2 mb-3">
                    <!-- 页面标题 -->
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center">
                        <h1 class="h2" th:text="${pageTitle} ?: '管理后台'">管理后台</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <span class="badge bg-success">在线</span>
                            </div>
                        </div>
                    </div>

                    <!-- 消息提示 -->
                    <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i>
                        <span th:text="${successMessage}"></span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    
                    <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle"></i>
                        <span th:text="${errorMessage}"></span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>

                    <!-- 页面内容 -->
                    <div th:replace="${content}">
                        <!-- 页面内容将在这里替换 -->
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义JavaScript -->
    <script>
        // 自动隐藏提示消息
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // 确认删除对话框
        function confirmDelete(message) {
            return confirm(message || '确定要删除吗？此操作不可恢复。');
        }

        // 文件上传预览
        function previewImage(input) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    var preview = document.getElementById('imagePreview');
                    if (preview) {
                        preview.src = e.target.result;
                        preview.style.display = 'block';
                    }
                }
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</body>
</html>
