@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ================================
echo Dreaming涂色App后端服务启动脚本
echo ================================

REM 设置应用端口（从application.yml读取，默认8083）
set APP_PORT=8083

echo [1/6] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java环境
    echo    请先安装Java 11或更高版本
    echo    下载地址: https://adoptium.net/
    pause
    exit /b 1
) else (
    echo ✅ Java环境检查通过
)

echo.
echo [2/6] 检查项目文件...
if not exist "pom.xml" (
    echo ❌ 错误: 未找到pom.xml文件
    echo    请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

if not exist "src\main\java\com\dreaming\DreamingColoringApplication.java" (
    echo ❌ 错误: 项目文件不完整
    echo    请检查项目文件是否完整
    pause
    exit /b 1
)
echo ✅ 项目文件检查通过

echo.
echo [3/6] 创建必要目录...
if not exist "uploads" (
    mkdir uploads
    echo ✅ 创建uploads目录
)
if not exist "logs" (
    mkdir logs
    echo ✅ 创建logs目录
)

echo.
echo [4/6] 检查端口占用...
echo 检查端口 %APP_PORT% 是否被占用...

REM 查找占用端口的进程
set OCCUPIED_PID=
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%APP_PORT% " 2^>nul') do (
    set OCCUPIED_PID=%%a
    goto :portOccupied
)

echo ✅ 端口 %APP_PORT% 未被占用
goto :skipPortKill

:portOccupied
if "%OCCUPIED_PID%"=="" (
    echo ✅ 端口 %APP_PORT% 未被占用
    goto :skipPortKill
)

echo ⚠️  端口 %APP_PORT% 被进程 %OCCUPIED_PID% 占用
echo 正在自动停止占用端口的进程...

taskkill /PID %OCCUPIED_PID% /F >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ 成功停止进程 %OCCUPIED_PID%
    timeout /t 2 /nobreak >nul
) else (
    echo ❌ 停止进程失败，请手动停止或以管理员身份运行
    echo    或者修改application.yml中的端口配置
    pause
    exit /b 1
)

:skipPortKill

echo.
echo [5/6] 清理旧进程...
echo 检查并清理可能的旧Java进程...

REM 简化的进程清理：查找并停止包含spring-boot的Java进程
for /f "skip=1 tokens=2" %%a in ('tasklist /fi "imagename eq java.exe" /fo table 2^>nul') do (
    set JAVA_PID=%%a
    
    REM 使用wmic检查命令行参数
    for /f %%b in ('wmic process where "ProcessId=!JAVA_PID!" get CommandLine 2^>nul ^| findstr /i "spring-boot:run\|DreamingColoringApplication" ^| find /c /v ""') do (
        if %%b gtr 0 (
            echo ⚠️  发现旧的Spring Boot进程: !JAVA_PID!
            taskkill /PID !JAVA_PID! /F >nul 2>&1
            if !errorlevel! equ 0 (
                echo ✅ 成功停止旧进程 !JAVA_PID!
            )
        )
    )
)

echo ✅ 进程清理完成

echo.
echo [6/6] 启动应用...
echo 正在启动Spring Boot应用，请稍候...
echo 应用将在端口 %APP_PORT% 启动
echo.

if exist "mvnw.cmd" (
    echo 使用Maven Wrapper启动...
    call mvnw.cmd spring-boot:run -Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS
) else (
    echo 使用系统Maven启动...
    mvn spring-boot:run -Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS
)

if %errorlevel% neq 0 (
    echo.
    echo ❌ 应用启动失败！
    echo 请检查以上错误信息，常见问题：
    echo 1. 端口%APP_PORT%被占用 - 脚本会自动处理，如失败请手动停止
    echo 2. Java版本不兼容 - 需要Java 11+
    echo 3. 依赖下载失败 - 检查网络连接
    echo 4. 权限不足 - 尝试以管理员身份运行
    echo.
    pause
    exit /b 1
)

pause
