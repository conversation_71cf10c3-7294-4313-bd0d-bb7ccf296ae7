@echo off
chcp 65001 >nul
echo ================================
echo Dreaming涂色App后端服务启动脚本
echo ================================

echo [1/4] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Java环境
    echo    请先安装Java 11或更高版本
    echo    下载地址: https://adoptium.net/
    pause
    exit /b 1
) else (
    echo ✅ Java环境检查通过
)

echo.
echo [2/4] 检查项目文件...
if not exist "pom.xml" (
    echo ❌ 错误: 未找到pom.xml文件
    echo    请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

if not exist "src\main\java\com\dreaming\DreamingColoringApplication.java" (
    echo ❌ 错误: 项目文件不完整
    echo    请检查项目文件是否完整
    pause
    exit /b 1
)
echo ✅ 项目文件检查通过

echo.
echo [3/4] 创建必要目录...
if not exist "uploads" (
    mkdir uploads
    echo ✅ 创建uploads目录
)
if not exist "logs" (
    mkdir logs
    echo ✅ 创建logs目录
)

echo.
echo [4/4] 启动应用...
echo 正在启动Spring Boot应用，请稍候...
echo.

if exist "mvnw.cmd" (
    echo 使用Maven Wrapper启动...
    call mvnw.cmd spring-boot:run -Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS
) else (
    echo 使用系统Maven启动...
    mvn spring-boot:run -Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS
)

if %errorlevel% neq 0 (
    echo.
    echo ❌ 应用启动失败！
    echo 请检查以上错误信息，常见问题：
    echo 1. 端口8080被占用 - 修改application.yml中的端口
    echo 2. Java版本不兼容 - 需要Java 11+
    echo 3. 依赖下载失败 - 检查网络连接
    echo.
    pause
    exit /b 1
)

pause
